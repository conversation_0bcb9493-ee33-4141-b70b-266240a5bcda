
import React from 'react';
import { Star, Quote } from 'lucide-react';

export const Reviews = () => {
  const reviews = [
    {
      name: "<PERSON>",
      text: "Joining Stockink changed everything for me! Their classes made stock market strategies easy to understand and apply. Thanks to their expert guidance, I now make consistent profits. I highly recommend Stockink to anyone who wants to learn trading and grow financially!",
      rating: 5
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      text: "Stockink join pannadhu en life la oru nalla decision! Avanga teaching style simple ah irundhudhu, so stock market strategies easy ah purinjikiten. Ippo naa confident ah trade panni nalla profit eduthutu iruken. Trading kathukanum na, kandippa Stockink try pannunga, 10/10 would recommend!",
      rating: 5
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      text: "Hi, This is <PERSON><PERSON>. Starting la trading na yenna nu thariyathu enaku, konjam kasta irukumo na expect pannitu class start pannnan but neinga teach pannathu enaku clear puruinchithu. Basic level to advanced level varaikum clear explain panninga, doubts athana thadava katalum solluringa and fast response pannureinga. Doubts session vaikereinga athu romba useful iruku. Epo trading la enaku konjam thariyathu ennum na learn pannanum, athuku unga support venum. Thank you @inkcharts team",
      rating: 5
    },
    {
      name: "Balaji",
      text: "Hey, this is Balaji! Trading pathi zero knowledge la start pannen. First la loss agamothu bayama irunthuchu, aana neenga kudutha guidance nala slow ah confidence build aayiduchu. Technical analysis, risk management ellam step by step purinjikiten. IPO, options, forex ellathulayum neenga clarity kuduthinga. Most importantly, support system super ah iruku—doubts kekka hesitation eh illa. Innum nalla learn panni consistent profits earn pannanum!",
      rating: 5
    }
  ];

  return (
    <section className="py-20 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-[#05044A] mb-6">
              Student Success Stories
            </h2>
            <p className="text-xl text-gray-600">
              Hear from our 1500+ successfully trained students
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {reviews.map((review, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-8 border hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-[#56D07F] rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">
                        {review.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#05044A] text-lg">{review.name}</h4>
                      <div className="flex items-center space-x-1">
                        {[...Array(review.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                    </div>
                  </div>
                  <Quote className="w-8 h-8 text-[#56D07F] opacity-50" />
                </div>
                
                <p className="text-gray-700 leading-relaxed italic">
                  "{review.text}"
                </p>
              </div>
            ))}
          </div>
          
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-r from-[#56D07F] to-emerald-400 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Join 1500+ Successful Traders</h3>
              <p className="text-lg opacity-90">
                "Success leaves clues. These are real results from real students who took action."
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
