"use strict";var e=require("react"),t=e=>"checkbox"===e.type,r=e=>e instanceof Date,s=e=>null==e;const a=e=>"object"==typeof e;var n=e=>!s(e)&&!Array.isArray(e)&&a(e)&&!r(e),i=e=>n(e)&&e.target?t(e.target)?e.target.checked:e.target.value:e,o=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),u=e=>{const t=e.constructor&&e.constructor.prototype;return n(t)&&t.hasOwnProperty("isPrototypeOf")},l="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function d(e){let t;const r=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(l&&(e instanceof Blob||e instanceof FileList)||!r&&!n(e))return e;if(t=r?[]:{},r||u(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=d(e[r]));else t=e}return t}var c=e=>Array.isArray(e)?e.filter(Boolean):[],f=e=>void 0===e,m=(e,t,r)=>{if(!t||!n(e))return r;const a=c(t.split(/[,[\].]+?/)).reduce(((e,t)=>s(e)?e:e[t]),e);return f(a)||a===e?f(e[t])?r:e[t]:a},y=e=>"boolean"==typeof e,p=e=>/^\w*$/.test(e),_=e=>c(e.replace(/["|']|\]/g,"").split(/\.|\[/)),g=(e,t,r)=>{let s=-1;const a=p(t)?[t]:_(t),i=a.length,o=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==o){const r=e[t];i=n(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t)return;e[t]=i,e=e[t]}return e};const v={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},h={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},b="max",x="min",A="maxLength",F="minLength",V="pattern",S="required",w="validate",k=e.createContext(null),D=()=>e.useContext(k);var C=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const n in e)Object.defineProperty(a,n,{get:()=>{const a=n;return t._proxyFormState[a]!==h.all&&(t._proxyFormState[a]=!s||h.all),r&&(r[a]=!0),e[a]}});return a},E=e=>n(e)&&!Object.keys(e).length,O=(e,t,r,s)=>{r(e);const{name:a,...n}=e;return E(n)||Object.keys(n).length>=Object.keys(t).length||Object.keys(n).find((e=>t[e]===(!s||h.all)))},j=e=>Array.isArray(e)?e:[e],T=(e,t,r)=>!e||!t||e===t||j(e).some((e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))));function U(t){const r=e.useRef(t);r.current=t,e.useEffect((()=>{const e=!t.disabled&&r.current.subject&&r.current.subject.subscribe({next:r.current.next});return()=>{e&&e.unsubscribe()}}),[t.disabled])}function B(t){const r=D(),{control:s=r.control,disabled:a,name:n,exact:i}=t||{},[o,u]=e.useState(s._formState),l=e.useRef(!0),d=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=e.useRef(n);return c.current=n,U({disabled:a,next:e=>l.current&&T(c.current,e.name,i)&&O(e,d.current,s._updateFormState)&&u({...s._formState,...e}),subject:s._subjects.state}),e.useEffect((()=>(l.current=!0,d.current.isValid&&s._updateValid(!0),()=>{l.current=!1})),[s]),C(o,s,d.current,!1)}var N=e=>"string"==typeof e,L=(e,t,r,s,a)=>N(e)?(s&&t.watch.add(e),m(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),m(r,e)))):(s&&(t.watchAll=!0),r);function M(t){const r=D(),{control:s=r.control,name:a,defaultValue:n,disabled:i,exact:o}=t||{},u=e.useRef(a);u.current=a,U({disabled:i,subject:s._subjects.values,next:e=>{T(u.current,e.name,o)&&c(d(L(u.current,s._names,e.values||s._formValues,!1,n)))}});const[l,c]=e.useState(s._getWatch(a,n));return e.useEffect((()=>s._removeUnmounted())),l}function R(t){const r=D(),{name:s,disabled:a,control:n=r.control,shouldUnregister:u}=t,l=o(n._names.array,s),c=M({control:n,name:s,defaultValue:m(n._formValues,s,m(n._defaultValues,s,t.defaultValue)),exact:!0}),p=B({control:n,name:s,exact:!0}),_=e.useRef(n.register(s,{...t.rules,value:c,...y(t.disabled)?{disabled:t.disabled}:{}}));return e.useEffect((()=>{const e=n._options.shouldUnregister||u,t=(e,t)=>{const r=m(n._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=d(m(n._options.defaultValues,s));g(n._defaultValues,s,e),f(m(n._formValues,s))&&g(n._formValues,s,e)}return()=>{(l?e&&!n._state.action:e)?n.unregister(s):t(s,!1)}}),[s,n,l,u]),e.useEffect((()=>{m(n._fields,s)&&n._updateDisabledField({disabled:a,fields:n._fields,name:s,value:m(n._fields,s)._f.value})}),[a,s,n]),{field:{name:s,value:c,...y(a)||p.disabled?{disabled:p.disabled||a}:{},onChange:e.useCallback((e=>_.current.onChange({target:{value:i(e),name:s},type:v.CHANGE})),[s]),onBlur:e.useCallback((()=>_.current.onBlur({target:{value:m(n._formValues,s),name:s},type:v.BLUR})),[s,n]),ref:e.useCallback((e=>{const t=m(n._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[n._fields,s])},formState:p,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!m(p.errors,s)},isDirty:{enumerable:!0,get:()=>!!m(p.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!m(p.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!m(p.validatingFields,s)},error:{enumerable:!0,get:()=>m(p.errors,s)}})}}const P=e=>{const t={};for(const r of Object.keys(e))if(a(e[r])){const s=P(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},q="post";var W=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},$=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},I=(e,t,r={})=>r.shouldFocus||f(r.shouldFocus)?r.focusName||`${e}.${f(r.focusIndex)?t:r.focusIndex}.`:"",H=e=>({isOnSubmit:!e||e===h.onSubmit,isOnBlur:e===h.onBlur,isOnChange:e===h.onChange,isOnAll:e===h.all,isOnTouch:e===h.onTouched}),G=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const J=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=m(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(J(i,t))break}else if(n(i)&&J(i,t))break}}};var z=(e,t,r)=>{const s=j(m(e,r));return g(s,"root",t[r]),g(e,r,s),e},K=e=>"file"===e.type,Q=e=>"function"==typeof e,X=e=>{if(!l)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Y=e=>N(e),Z=e=>"radio"===e.type,ee=e=>e instanceof RegExp;const te={value:!1,isValid:!1},re={value:!0,isValid:!0};var se=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!f(e[0].attributes.value)?f(e[0].value)||""===e[0].value?re:{value:e[0].value,isValid:!0}:re:te}return te};const ae={isValid:!1,value:null};var ne=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),ae):ae;function ie(e,t,r="validate"){if(Y(e)||Array.isArray(e)&&e.every(Y)||y(e)&&!e)return{type:r,message:Y(e)?e:"",ref:t}}var oe=e=>n(e)&&!ee(e)?e:{value:e,message:""},ue=async(e,r,a,i,o)=>{const{ref:u,refs:l,required:d,maxLength:c,minLength:p,min:_,max:g,pattern:v,validate:h,name:k,valueAsNumber:D,mount:C,disabled:O}=e._f,j=m(r,k);if(!C||O)return{};const T=l?l[0]:u,U=e=>{i&&T.reportValidity&&(T.setCustomValidity(y(e)?"":e||""),T.reportValidity())},B={},L=Z(u),M=t(u),R=L||M,P=(D||K(u))&&f(u.value)&&f(j)||X(u)&&""===u.value||""===j||Array.isArray(j)&&!j.length,q=W.bind(null,k,a,B),$=(e,t,r,s=A,a=F)=>{const n=e?t:r;B[k]={type:e?s:a,message:n,ref:u,...q(e?s:a,n)}};if(o?!Array.isArray(j)||!j.length:d&&(!R&&(P||s(j))||y(j)&&!j||M&&!se(l).isValid||L&&!ne(l).isValid)){const{value:e,message:t}=Y(d)?{value:!!d,message:d}:oe(d);if(e&&(B[k]={type:S,message:t,ref:T,...q(S,t)},!a))return U(t),B}if(!(P||s(_)&&s(g))){let e,t;const r=oe(g),n=oe(_);if(s(j)||isNaN(j)){const s=u.valueAsDate||new Date(j),a=e=>new Date((new Date).toDateString()+" "+e),i="time"==u.type,o="week"==u.type;N(r.value)&&j&&(e=i?a(j)>a(r.value):o?j>r.value:s>new Date(r.value)),N(n.value)&&j&&(t=i?a(j)<a(n.value):o?j<n.value:s<new Date(n.value))}else{const a=u.valueAsNumber||(j?+j:j);s(r.value)||(e=a>r.value),s(n.value)||(t=a<n.value)}if((e||t)&&($(!!e,r.message,n.message,b,x),!a))return U(B[k].message),B}if((c||p)&&!P&&(N(j)||o&&Array.isArray(j))){const e=oe(c),t=oe(p),r=!s(e.value)&&j.length>+e.value,n=!s(t.value)&&j.length<+t.value;if((r||n)&&($(r,e.message,t.message),!a))return U(B[k].message),B}if(v&&!P&&N(j)){const{value:e,message:t}=oe(v);if(ee(e)&&!j.match(e)&&(B[k]={type:V,message:t,ref:u,...q(V,t)},!a))return U(t),B}if(h)if(Q(h)){const e=ie(await h(j,r),T);if(e&&(B[k]={...e,...q(w,e.message)},!a))return U(e.message),B}else if(n(h)){let e={};for(const t in h){if(!E(e)&&!a)break;const s=ie(await h[t](j,r),T,t);s&&(e={...s,...q(t,s.message)},U(s.message),a&&(B[k]=e))}if(!E(e)&&(B[k]={ref:T,...e},!a))return B}return U(!0),B},le=(e,t)=>[...e,...j(t)],de=e=>Array.isArray(e)?e.map((()=>{})):void 0;function ce(e,t,r){return[...e.slice(0,t),...j(r),...e.slice(t)]}var fe=(e,t,r)=>Array.isArray(e)?(f(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],me=(e,t)=>[...j(t),...j(e)];var ye=(e,t)=>f(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return c(s).length?s:[]}(e,j(t).sort(((e,t)=>e-t))),pe=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]};function _e(e,t){const r=Array.isArray(t)?t:p(t)?[t]:_(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=f(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(n(s)&&E(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!f(e[t]))return!1;return!0}(s))&&_e(e,r.slice(0,-1)),e}var ge=(e,t,r)=>(e[t]=r,e);var ve=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},he=e=>s(e)||!a(e);function be(e,t){if(he(e)||he(t))return e===t;if(r(e)&&r(t))return e.getTime()===t.getTime();const s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(const i of s){const s=e[i];if(!a.includes(i))return!1;if("ref"!==i){const e=t[i];if(r(s)&&r(e)||n(s)&&n(e)||Array.isArray(s)&&Array.isArray(e)?!be(s,e):s!==e)return!1}}return!0}var xe=e=>"select-multiple"===e.type,Ae=e=>Z(e)||t(e),Fe=e=>X(e)&&e.isConnected,Ve=e=>{for(const t in e)if(Q(e[t]))return!0;return!1};function Se(e,t={}){const r=Array.isArray(e);if(n(e)||r)for(const r in e)Array.isArray(e[r])||n(e[r])&&!Ve(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Se(e[r],t[r])):s(e[r])||(t[r]=!0);return t}function we(e,t,r){const a=Array.isArray(e);if(n(e)||a)for(const a in e)Array.isArray(e[a])||n(e[a])&&!Ve(e[a])?f(t)||he(r[a])?r[a]=Array.isArray(e[a])?Se(e[a],[]):{...Se(e[a])}:we(e[a],s(t)?{}:t[a],r[a]):r[a]=!be(e[a],t[a]);return r}var ke=(e,t)=>we(e,t,Se(t)),De=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>f(e)?e:t?""===e?NaN:e?+e:e:r&&N(e)?new Date(e):s?s(e):e;function Ce(e){const r=e.ref;if(!(e.refs?e.refs.every((e=>e.disabled)):r.disabled))return K(r)?r.files:Z(r)?ne(e.refs).value:xe(r)?[...r.selectedOptions].map((({value:e})=>e)):t(r)?se(e.refs).value:De(f(r.value)?e.ref.value:r.value,e)}var Ee=(e,t,r,s)=>{const a={};for(const r of e){const e=m(t,r);e&&g(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}},Oe=e=>f(e)?e:ee(e)?e.source:n(e)?ee(e.value)?e.value.source:e.value:e;const je="AsyncFunction";var Te=e=>!(e&&e.validate||!(Q(e.validate)&&e.validate.constructor.name===je||n(e.validate)&&Object.values(e.validate).find((e=>e.constructor.name===je)))),Ue=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function Be(e,t,r){const s=m(e,r);if(s||p(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),n=m(t,s),i=m(e,s);if(n&&!Array.isArray(n)&&r!==s)return{name:r};if(i&&i.type)return{name:s,error:i};a.pop()}return{name:r}}var Ne=(e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e),Le=(e,t)=>!c(m(e,t)).length&&_e(e,t);const Me={mode:h.onSubmit,reValidateMode:h.onChange,shouldFocusError:!0};function Re(e={}){let a,u={...Me,...e},p={submitCount:0,isDirty:!1,isLoading:Q(u.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:u.errors||{},disabled:u.disabled||!1},_={},b=(n(u.defaultValues)||n(u.values))&&d(u.defaultValues||u.values)||{},x=u.shouldUnregister?{}:d(b),A={action:!1,mount:!1,watch:!1},F={mount:new Set,unMount:new Set,array:new Set,watch:new Set},V=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},w={values:ve(),array:ve(),state:ve()},k=H(u.mode),D=H(u.reValidateMode),C=u.criteriaMode===h.all,O=async t=>{if(!e.disabled&&(S.isValid||t)){const e=u.resolver?E((await R()).errors):await P(_,!0);e!==p.isValid&&w.state.next({isValid:e})}},T=(t,r)=>{e.disabled||!S.isValidating&&!S.validatingFields||((t||Array.from(F.mount)).forEach((e=>{e&&(r?g(p.validatingFields,e,r):_e(p.validatingFields,e))})),w.state.next({validatingFields:p.validatingFields,isValidating:!E(p.validatingFields)}))},U=(e,t,r,s)=>{const a=m(_,e);if(a){const n=m(x,e,f(r)?m(b,e):r);f(n)||s&&s.defaultChecked||t?g(x,e,t?n:Ce(a._f)):$(e,n),A.mount&&O()}},B=(t,r,s,a,n)=>{let i=!1,o=!1;const u={name:t};if(!e.disabled){const e=!!(m(_,t)&&m(_,t)._f&&m(_,t)._f.disabled);if(!s||a){S.isDirty&&(o=p.isDirty,p.isDirty=u.isDirty=q(),i=o!==u.isDirty);const s=e||be(m(b,t),r);o=!(e||!m(p.dirtyFields,t)),s||e?_e(p.dirtyFields,t):g(p.dirtyFields,t,!0),u.dirtyFields=p.dirtyFields,i=i||S.dirtyFields&&o!==!s}if(s){const e=m(p.touchedFields,t);e||(g(p.touchedFields,t,s),u.touchedFields=p.touchedFields,i=i||S.touchedFields&&e!==s)}i&&n&&w.state.next(u)}return i?u:{}},M=(t,r,s,n)=>{const i=m(p.errors,t),o=S.isValid&&y(r)&&p.isValid!==r;var u;if(e.delayError&&s?(u=()=>((e,t)=>{g(p.errors,e,t),w.state.next({errors:p.errors})})(t,s),a=e=>{clearTimeout(V),V=setTimeout(u,e)},a(e.delayError)):(clearTimeout(V),a=null,s?g(p.errors,t,s):_e(p.errors,t)),(s?!be(i,s):i)||!E(n)||o){const e={...n,...o&&y(r)?{isValid:r}:{},errors:p.errors,name:t};p={...p,...e},w.state.next(e)}},R=async e=>{T(e,!0);const t=await u.resolver(x,u.context,Ee(e||F.mount,_,u.criteriaMode,u.shouldUseNativeValidation));return T(e),t},P=async(e,t,r={valid:!0})=>{for(const s in e){const a=e[s];if(a){const{_f:e,...n}=a;if(e){const n=F.array.has(e.name),i=a._f&&Te(a._f);i&&S.validatingFields&&T([s],!0);const o=await ue(a,x,C,u.shouldUseNativeValidation&&!t,n);if(i&&S.validatingFields&&T([s]),o[e.name]&&(r.valid=!1,t))break;!t&&(m(o,e.name)?n?z(p.errors,o,e.name):g(p.errors,e.name,o[e.name]):_e(p.errors,e.name))}!E(n)&&await P(n,t,r)}}return r.valid},q=(t,r)=>!e.disabled&&(t&&r&&g(x,t,r),!be(re(),b)),W=(e,t,r)=>L(e,F,{...A.mount?x:f(t)?b:N(e)?{[e]:t}:t},r,t),$=(e,r,a={})=>{const n=m(_,e);let i=r;if(n){const a=n._f;a&&(!a.disabled&&g(x,e,De(r,a)),i=X(a.ref)&&s(r)?"":r,xe(a.ref)?[...a.ref.options].forEach((e=>e.selected=i.includes(e.value))):a.refs?t(a.ref)?a.refs.length>1?a.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(i)?!!i.find((t=>t===e.value)):i===e.value))):a.refs[0]&&(a.refs[0].checked=!!i):a.refs.forEach((e=>e.checked=e.value===i)):K(a.ref)?a.ref.value="":(a.ref.value=i,a.ref.type||w.values.next({name:e,values:{...x}})))}(a.shouldDirty||a.shouldTouch)&&B(e,i,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&te(e)},I=(e,t,s)=>{for(const a in t){const i=t[a],o=`${e}.${a}`,u=m(_,o);(F.array.has(e)||n(i)||u&&!u._f)&&!r(i)?I(o,i,s):$(o,i,s)}},Y=(e,t,r={})=>{const a=m(_,e),n=F.array.has(e),i=d(t);g(x,e,i),n?(w.array.next({name:e,values:{...x}}),(S.isDirty||S.dirtyFields)&&r.shouldDirty&&w.state.next({name:e,dirtyFields:ke(b,x),isDirty:q(e,i)})):!a||a._f||s(i)?$(e,i,r):I(e,i,r),G(e,F)&&w.state.next({...p}),w.values.next({name:A.mount?e:void 0,values:{...x}})},Z=async t=>{A.mount=!0;const s=t.target;let n=s.name,o=!0;const l=m(_,n),d=e=>{o=Number.isNaN(e)||r(e)&&isNaN(e.getTime())||be(e,m(x,n,e))};if(l){let r,c;const f=s.type?Ce(l._f):i(t),y=t.type===v.BLUR||t.type===v.FOCUS_OUT,h=!Ue(l._f)&&!u.resolver&&!m(p.errors,n)&&!l._f.deps||Ne(y,m(p.touchedFields,n),p.isSubmitted,D,k),b=G(n,F,y);g(x,n,f),y?(l._f.onBlur&&l._f.onBlur(t),a&&a(0)):l._f.onChange&&l._f.onChange(t);const A=B(n,f,y,!1),V=!E(A)||b;if(!y&&w.values.next({name:n,type:t.type,values:{...x}}),h)return S.isValid&&("onBlur"===e.mode?y&&O():O()),V&&w.state.next({name:n,...b?{}:A});if(!y&&b&&w.state.next({...p}),u.resolver){const{errors:e}=await R([n]);if(d(f),o){const t=Be(p.errors,_,n),s=Be(e,_,t.name||n);r=s.error,n=s.name,c=E(e)}}else T([n],!0),r=(await ue(l,x,C,u.shouldUseNativeValidation))[n],T([n]),d(f),o&&(r?c=!1:S.isValid&&(c=await P(_,!0)));o&&(l._f.deps&&te(l._f.deps),M(n,c,r,A))}},ee=(e,t)=>{if(m(p.errors,t)&&e.focus)return e.focus(),1},te=async(e,t={})=>{let r,s;const a=j(e);if(u.resolver){const t=await(async e=>{const{errors:t}=await R(e);if(e)for(const r of e){const e=m(t,r);e?g(p.errors,r,e):_e(p.errors,r)}else p.errors=t;return t})(f(e)?e:a);r=E(t),s=e?!a.some((e=>m(t,e))):r}else e?(s=(await Promise.all(a.map((async e=>{const t=m(_,e);return await P(t&&t._f?{[e]:t}:t)})))).every(Boolean),(s||p.isValid)&&O()):s=r=await P(_);return w.state.next({...!N(e)||S.isValid&&r!==p.isValid?{}:{name:e},...u.resolver||!e?{isValid:r}:{},errors:p.errors}),t.shouldFocus&&!s&&J(_,ee,e?a:F.mount),s},re=e=>{const t={...A.mount?x:b};return f(e)?t:N(e)?m(t,e):e.map((e=>m(t,e)))},se=(e,t)=>({invalid:!!m((t||p).errors,e),isDirty:!!m((t||p).dirtyFields,e),error:m((t||p).errors,e),isValidating:!!m(p.validatingFields,e),isTouched:!!m((t||p).touchedFields,e)}),ae=(e,t,r)=>{const s=(m(_,e,{_f:{}})._f||{}).ref,a=m(p.errors,e)||{},{ref:n,message:i,type:o,...u}=a;g(p.errors,e,{...u,...t,ref:s}),w.state.next({name:e,errors:p.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},ne=(e,t={})=>{for(const r of e?j(e):F.mount)F.mount.delete(r),F.array.delete(r),t.keepValue||(_e(_,r),_e(x,r)),!t.keepError&&_e(p.errors,r),!t.keepDirty&&_e(p.dirtyFields,r),!t.keepTouched&&_e(p.touchedFields,r),!t.keepIsValidating&&_e(p.validatingFields,r),!u.shouldUnregister&&!t.keepDefaultValue&&_e(b,r);w.values.next({values:{...x}}),w.state.next({...p,...t.keepDirty?{isDirty:q()}:{}}),!t.keepIsValid&&O()},ie=({disabled:e,name:t,field:r,fields:s,value:a})=>{if(y(e)&&A.mount||e){const n=e?void 0:f(a)?Ce(r?r._f:m(s,t)._f):a;g(x,t,n),B(t,n,!1,!1,!0)}},oe=(t,r={})=>{let s=m(_,t);const a=y(r.disabled)||y(e.disabled);return g(_,t,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:t}},name:t,mount:!0,...r}}),F.mount.add(t),s?ie({field:s,disabled:y(r.disabled)?r.disabled:e.disabled,name:t,value:r.value}):U(t,!0,r.value),{...a?{disabled:r.disabled||e.disabled}:{},...u.progressive?{required:!!r.required,min:Oe(r.min),max:Oe(r.max),minLength:Oe(r.minLength),maxLength:Oe(r.maxLength),pattern:Oe(r.pattern)}:{},name:t,onChange:Z,onBlur:Z,ref:e=>{if(e){oe(t,r),s=m(_,t);const a=f(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,n=Ae(a),i=s._f.refs||[];if(n?i.find((e=>e===a)):a===s._f.ref)return;g(_,t,{_f:{...s._f,...n?{refs:[...i.filter(Fe),a,...Array.isArray(m(b,t))?[{}]:[]],ref:{type:a.type,name:t}}:{ref:a}}}),U(t,!1,void 0,a)}else s=m(_,t,{}),s._f&&(s._f.mount=!1),(u.shouldUnregister||r.shouldUnregister)&&(!o(F.array,t)||!A.action)&&F.unMount.add(t)}}},le=()=>u.shouldFocusError&&J(_,ee,F.mount),de=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=d(x);if(w.state.next({isSubmitting:!0}),u.resolver){const{errors:e,values:t}=await R();p.errors=e,a=t}else await P(_);if(_e(p.errors,"root"),E(p.errors)){w.state.next({errors:{}});try{await e(a,r)}catch(e){s=e}}else t&&await t({...p.errors},r),le(),setTimeout(le);if(w.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:E(p.errors)&&!s,submitCount:p.submitCount+1,errors:p.errors}),s)throw s},ce=(t,r={})=>{const s=t?d(t):b,a=d(s),n=E(t),i=n?b:a;if(r.keepDefaultValues||(b=s),!r.keepValues){if(r.keepDirtyValues){const e=new Set([...F.mount,...Object.keys(ke(b,x))]);for(const t of Array.from(e))m(p.dirtyFields,t)?g(i,t,m(x,t)):Y(t,m(i,t))}else{if(l&&f(t))for(const e of F.mount){const t=m(_,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(X(e)){const t=e.closest("form");if(t){t.reset();break}}}}_={}}x=e.shouldUnregister?r.keepDefaultValues?d(b):{}:d(i),w.array.next({values:{...i}}),w.values.next({values:{...i}})}F={mount:r.keepDirtyValues?F.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},A.mount=!S.isValid||!!r.keepIsValid||!!r.keepDirtyValues,A.watch=!!e.shouldUnregister,w.state.next({submitCount:r.keepSubmitCount?p.submitCount:0,isDirty:!n&&(r.keepDirty?p.isDirty:!(!r.keepDefaultValues||be(t,b))),isSubmitted:!!r.keepIsSubmitted&&p.isSubmitted,dirtyFields:n?{}:r.keepDirtyValues?r.keepDefaultValues&&x?ke(b,x):p.dirtyFields:r.keepDefaultValues&&t?ke(b,t):r.keepDirty?p.dirtyFields:{},touchedFields:r.keepTouched?p.touchedFields:{},errors:r.keepErrors?p.errors:{},isSubmitSuccessful:!!r.keepIsSubmitSuccessful&&p.isSubmitSuccessful,isSubmitting:!1})},fe=(e,t)=>ce(Q(e)?e(x):e,t);return{control:{register:oe,unregister:ne,getFieldState:se,handleSubmit:de,setError:ae,_executeSchema:R,_getWatch:W,_getDirty:q,_updateValid:O,_removeUnmounted:()=>{for(const e of F.unMount){const t=m(_,e);t&&(t._f.refs?t._f.refs.every((e=>!Fe(e))):!Fe(t._f.ref))&&ne(e)}F.unMount=new Set},_updateFieldArray:(t,r=[],s,a,n=!0,i=!0)=>{if(a&&s&&!e.disabled){if(A.action=!0,i&&Array.isArray(m(_,t))){const e=s(m(_,t),a.argA,a.argB);n&&g(_,t,e)}if(i&&Array.isArray(m(p.errors,t))){const e=s(m(p.errors,t),a.argA,a.argB);n&&g(p.errors,t,e),Le(p.errors,t)}if(S.touchedFields&&i&&Array.isArray(m(p.touchedFields,t))){const e=s(m(p.touchedFields,t),a.argA,a.argB);n&&g(p.touchedFields,t,e)}S.dirtyFields&&(p.dirtyFields=ke(b,x)),w.state.next({name:t,isDirty:q(t,r),dirtyFields:p.dirtyFields,errors:p.errors,isValid:p.isValid})}else g(x,t,r)},_updateDisabledField:ie,_getFieldArray:t=>c(m(A.mount?x:b,t,e.shouldUnregister?m(b,t,[]):[])),_reset:ce,_resetDefaultValues:()=>Q(u.defaultValues)&&u.defaultValues().then((e=>{fe(e,u.resetOptions),w.state.next({isLoading:!1})})),_updateFormState:e=>{p={...p,...e}},_disableForm:e=>{y(e)&&(w.state.next({disabled:e}),J(_,((t,r)=>{const s=m(_,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:w,_proxyFormState:S,_setErrors:e=>{p.errors=e,w.state.next({errors:p.errors,isValid:!1})},get _fields(){return _},get _formValues(){return x},get _state(){return A},set _state(e){A=e},get _defaultValues(){return b},get _names(){return F},set _names(e){F=e},get _formState(){return p},set _formState(e){p=e},get _options(){return u},set _options(e){u={...u,...e}}},trigger:te,register:oe,handleSubmit:de,watch:(e,t)=>Q(e)?w.values.subscribe({next:r=>e(W(void 0,t),r)}):W(e,t,!0),setValue:Y,getValues:re,reset:fe,resetField:(e,t={})=>{m(_,e)&&(f(t.defaultValue)?Y(e,d(m(b,e))):(Y(e,t.defaultValue),g(b,e,d(t.defaultValue))),t.keepTouched||_e(p.touchedFields,e),t.keepDirty||(_e(p.dirtyFields,e),p.isDirty=t.defaultValue?q(e,d(m(b,e))):q()),t.keepError||(_e(p.errors,e),S.isValid&&O()),w.state.next({...p}))},clearErrors:e=>{e&&j(e).forEach((e=>_e(p.errors,e))),w.state.next({errors:e?p.errors:{}})},unregister:ne,setError:ae,setFocus:(e,t={})=>{const r=m(_,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&e.select())}},getFieldState:se}}exports.Controller=e=>e.render(R(e)),exports.Form=function(t){const r=D(),[s,a]=e.useState(!1),{control:n=r.control,onSubmit:i,children:o,action:u,method:l=q,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:p,..._}=t,g=async e=>{let r=!1,s="";await n.handleSubmit((async t=>{const a=new FormData;let o="";try{o=JSON.stringify(t)}catch(e){}const m=P(n._formValues);for(const e in m)a.append(e,m[e]);if(i&&await i({data:t,event:e,method:l,formData:a,formDataJson:o}),u)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(u,{method:l,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(p?!p(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(e),r&&t.control&&(t.control._subjects.state.next({isSubmitSuccessful:!1}),t.control.setError("root.server",{type:s}))};return e.useEffect((()=>{a(!0)}),[]),m?e.createElement(e.Fragment,null,m({submit:g})):e.createElement("form",{noValidate:s,action:u,method:l,encType:c,onSubmit:g,..._},o)},exports.FormProvider=t=>{const{children:r,...s}=t;return e.createElement(k.Provider,{value:s},r)},exports.appendErrors=W,exports.get=m,exports.set=g,exports.useController=R,exports.useFieldArray=function(t){const r=D(),{control:s=r.control,name:a,keyName:n="id",shouldUnregister:i}=t,[o,u]=e.useState(s._getFieldArray(a)),l=e.useRef(s._getFieldArray(a).map($)),c=e.useRef(o),f=e.useRef(a),y=e.useRef(!1);f.current=a,c.current=o,s._names.array.add(a),t.rules&&s.register(a,t.rules),U({next:({values:e,name:t})=>{if(t===f.current||!t){const t=m(e,f.current);Array.isArray(t)&&(u(t),l.current=t.map($))}},subject:s._subjects.array});const p=e.useCallback((e=>{y.current=!0,s._updateFieldArray(a,e)}),[s,a]);return e.useEffect((()=>{if(s._state.action=!1,G(a,s._names)&&s._subjects.state.next({...s._formState}),y.current&&(!H(s._options.mode).isOnSubmit||s._formState.isSubmitted))if(s._options.resolver)s._executeSchema([a]).then((e=>{const t=m(e.errors,a),r=m(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?g(s._formState.errors,a,t):_e(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=m(s._fields,a);!e||!e._f||H(s._options.reValidateMode).isOnSubmit&&H(s._options.mode).isOnSubmit||ue(e,s._formValues,s._options.criteriaMode===h.all,s._options.shouldUseNativeValidation,!0).then((e=>!E(e)&&s._subjects.state.next({errors:z(s._formState.errors,e,a)})))}s._subjects.values.next({name:a,values:{...s._formValues}}),s._names.focus&&J(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._updateValid(),y.current=!1}),[o,a,s]),e.useEffect((()=>(!m(s._formValues,a)&&s._updateFieldArray(a),()=>{(s._options.shouldUnregister||i)&&s.unregister(a)})),[a,s,n,i]),{swap:e.useCallback(((e,t)=>{const r=s._getFieldArray(a);pe(r,e,t),pe(l.current,e,t),p(r),u(r),s._updateFieldArray(a,r,pe,{argA:e,argB:t},!1)}),[p,a,s]),move:e.useCallback(((e,t)=>{const r=s._getFieldArray(a);fe(r,e,t),fe(l.current,e,t),p(r),u(r),s._updateFieldArray(a,r,fe,{argA:e,argB:t},!1)}),[p,a,s]),prepend:e.useCallback(((e,t)=>{const r=j(d(e)),n=me(s._getFieldArray(a),r);s._names.focus=I(a,0,t),l.current=me(l.current,r.map($)),p(n),u(n),s._updateFieldArray(a,n,me,{argA:de(e)})}),[p,a,s]),append:e.useCallback(((e,t)=>{const r=j(d(e)),n=le(s._getFieldArray(a),r);s._names.focus=I(a,n.length-1,t),l.current=le(l.current,r.map($)),p(n),u(n),s._updateFieldArray(a,n,le,{argA:de(e)})}),[p,a,s]),remove:e.useCallback((e=>{const t=ye(s._getFieldArray(a),e);l.current=ye(l.current,e),p(t),u(t),s._updateFieldArray(a,t,ye,{argA:e})}),[p,a,s]),insert:e.useCallback(((e,t,r)=>{const n=j(d(t)),i=ce(s._getFieldArray(a),e,n);s._names.focus=I(a,e,r),l.current=ce(l.current,e,n.map($)),p(i),u(i),s._updateFieldArray(a,i,ce,{argA:e,argB:de(t)})}),[p,a,s]),update:e.useCallback(((e,t)=>{const r=d(t),n=ge(s._getFieldArray(a),e,r);l.current=[...n].map(((t,r)=>t&&r!==e?l.current[r]:$())),p(n),u([...n]),s._updateFieldArray(a,n,ge,{argA:e,argB:r},!0,!1)}),[p,a,s]),replace:e.useCallback((e=>{const t=j(d(e));l.current=t.map($),p([...t]),u([...t]),s._updateFieldArray(a,[...t],(e=>e),{},!0,!1)}),[p,a,s]),fields:e.useMemo((()=>o.map(((e,t)=>({...e,[n]:l.current[t]||$()})))),[o,n])}},exports.useForm=function(t={}){const r=e.useRef(),s=e.useRef(),[a,n]=e.useState({isDirty:!1,isValidating:!1,isLoading:Q(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,defaultValues:Q(t.defaultValues)?void 0:t.defaultValues});r.current||(r.current={...Re(t),formState:a});const i=r.current.control;return i._options=t,U({subject:i._subjects.state,next:e=>{O(e,i._proxyFormState,i._updateFormState,!0)&&n({...i._formState})}}),e.useEffect((()=>i._disableForm(t.disabled)),[i,t.disabled]),e.useEffect((()=>{if(i._proxyFormState.isDirty){const e=i._getDirty();e!==a.isDirty&&i._subjects.state.next({isDirty:e})}}),[i,a.isDirty]),e.useEffect((()=>{t.values&&!be(t.values,s.current)?(i._reset(t.values,i._options.resetOptions),s.current=t.values,n((e=>({...e})))):i._resetDefaultValues()}),[t.values,i]),e.useEffect((()=>{t.errors&&i._setErrors(t.errors)}),[t.errors,i]),e.useEffect((()=>{i._state.mount||(i._updateValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()})),e.useEffect((()=>{t.shouldUnregister&&i._subjects.values.next({values:i._getWatch()})}),[t.shouldUnregister,i]),e.useEffect((()=>{r.current&&(r.current.watch=r.current.watch.bind({}))}),[a]),r.current.formState=C(a,i),r.current},exports.useFormContext=D,exports.useFormState=B,exports.useWatch=M;
//# sourceMappingURL=index.cjs.js.map
