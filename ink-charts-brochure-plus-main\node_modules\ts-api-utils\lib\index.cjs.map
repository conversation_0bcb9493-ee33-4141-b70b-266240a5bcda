{"version": 3, "sources": ["../src/index.ts", "../src/comments.ts", "../src/tokens.ts", "../src/compilerOptions.ts", "../src/flags.ts", "../src/modifiers.ts", "../src/nodes/access.ts", "../src/syntax.ts", "../src/nodes/typeGuards/compound.ts", "../src/nodes/typeGuards/single.ts", "../src/nodes/typeGuards/union.ts", "../src/utils.ts", "../src/scopes.ts", "../src/types/getters.ts", "../src/types/typeGuards/intrinsic.ts", "../src/types/typeGuards/objects.ts", "../src/types/typeGuards/single.ts", "../src/types/typeGuards/compound.ts", "../src/types/typeGuards/literal.ts", "../src/types/utilities.ts", "../src/nodes/utilities.ts", "../src/usage/UsageWalker.ts", "../src/usage/Scope.ts", "../src/usage/declarations.ts", "../src/usage/utils.ts", "../src/usage/getPropertyName.ts", "../src/usage/getUsageDomain.ts", "../src/usage/scopes.ts", "../src/usage/collectVariableUsage.ts"], "sourcesContent": ["export * from \"./comments\";\nexport * from \"./compilerOptions\";\nexport * from \"./flags\";\nexport * from \"./modifiers\";\nexport * from \"./nodes\";\nexport * from \"./scopes\";\nexport * from \"./syntax\";\nexport * from \"./tokens\";\nexport * from \"./types\";\nexport * from \"./usage\";\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { forEachToken } from \"./tokens\";\n\n/**\n * Exclude trailing positions that would lead to scanning for trivia inside `JsxText`.\n * @internal\n */\nfunction canHaveTrailingTrivia(token: ts.Node): boolean {\n\tswitch (token.kind) {\n\t\tcase ts.SyntaxKind.CloseBraceToken:\n\t\t\t// after a JsxExpression inside a JsxElement's body can only be other JsxChild, but no trivia\n\t\t\treturn (\n\t\t\t\ttoken.parent.kind !== ts.SyntaxKind.JsxExpression ||\n\t\t\t\t!isJsxElementOrFragment(token.parent.parent)\n\t\t\t);\n\t\tcase ts.SyntaxKind.GreaterThanToken:\n\t\t\tswitch (token.parent.kind) {\n\t\t\t\tcase ts.SyntaxKind.JsxOpeningElement:\n\t\t\t\t\t// if end is not equal, this is part of the type arguments list. in all other cases it would be inside the element body\n\t\t\t\t\treturn token.end !== token.parent.end;\n\t\t\t\tcase ts.SyntaxKind.JsxOpeningFragment:\n\t\t\t\t\treturn false; // would be inside the fragment\n\t\t\t\tcase ts.SyntaxKind.JsxSelfClosingElement:\n\t\t\t\t\treturn (\n\t\t\t\t\t\ttoken.end !== token.parent.end || // if end is not equal, this is part of the type arguments list\n\t\t\t\t\t\t!isJsxElementOrFragment(token.parent.parent)\n\t\t\t\t\t); // there's only trailing trivia if it's the end of the top element\n\t\t\t\tcase ts.SyntaxKind.JsxClosingElement:\n\t\t\t\tcase ts.SyntaxKind.JsxClosingFragment:\n\t\t\t\t\t// there's only trailing trivia if it's the end of the top element\n\t\t\t\t\treturn !isJsxElementOrFragment(token.parent.parent.parent);\n\t\t\t}\n\t}\n\n\treturn true;\n}\n\n/**\n * Test if a node is a `JsxElement` or `JsxFragment`.\n * @internal\n */\nfunction isJsxElementOrFragment(\n\tnode: ts.Node,\n): node is ts.JsxElement | ts.JsxFragment {\n\treturn (\n\t\tnode.kind === ts.SyntaxKind.JsxElement ||\n\t\tnode.kind === ts.SyntaxKind.JsxFragment\n\t);\n}\n\n/**\n * Callback type used for {@link forEachComment}.\n * @category Callbacks\n */\nexport type ForEachCommentCallback = (\n\tfullText: string,\n\tcomment: ts.CommentRange,\n) => void;\n\n/**\n * Iterates over all comments owned by `node` or its children.\n * @category Nodes - Other Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * forEachComment(node, (fullText, comment) => {\n *    console.log(`Found comment at position ${comment.pos}: '${fullText}'.`);\n * });\n * ```\n */\nexport function forEachComment(\n\tnode: ts.Node,\n\tcallback: ForEachCommentCallback,\n\tsourceFile: ts.SourceFile = node.getSourceFile(),\n): void {\n\t/* Visit all tokens and skip trivia.\n       Comment ranges between tokens are parsed without the need of a scanner.\n       forEachTokenWithWhitespace does intentionally not pay attention to the correct comment ownership of nodes as it always\n       scans all trivia before each token, which could include trailing comments of the previous token.\n       Comment ownership is done right in this function*/\n\tconst fullText = sourceFile.text;\n\tconst notJsx = sourceFile.languageVariant !== ts.LanguageVariant.JSX;\n\treturn forEachToken(\n\t\tnode,\n\t\t(token) => {\n\t\t\tif (token.pos === token.end) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (token.kind !== ts.SyntaxKind.JsxText) {\n\t\t\t\tts.forEachLeadingCommentRange(\n\t\t\t\t\tfullText,\n\t\t\t\t\t// skip shebang at position 0\n\t\t\t\t\ttoken.pos === 0 ? (ts.getShebang(fullText) ?? \"\").length : token.pos,\n\t\t\t\t\tcommentCallback,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (notJsx || canHaveTrailingTrivia(token)) {\n\t\t\t\treturn ts.forEachTrailingCommentRange(\n\t\t\t\t\tfullText,\n\t\t\t\t\ttoken.end,\n\t\t\t\t\tcommentCallback,\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\t\tsourceFile,\n\t);\n\tfunction commentCallback(pos: number, end: number, kind: ts.CommentKind) {\n\t\tcallback(fullText, { end, kind, pos });\n\t}\n}\n", "// Code largely based on ajafff/tsutils:\n// https://github.com/ajafff/tsutils/blob/03b4df15d14702f9c7a128ac3fae77171778d613/util/util.ts\n// Original license MIT:\n// https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\n/**\n * Callback type used for {@link forEachToken}.\n * @category Callbacks\n */\nexport type ForEachTokenCallback = (token: ts.Node) => void;\n\n/**\n * Iterates over all tokens of `node`\n * @category Nodes - Other Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * forEachToken(node, (token) => {\n * \tconsole.log(\"Found token:\", token.getText());\n * });\n * ```\n * @param node - The node whose tokens should be visited\n * @param callback - Is called for every token contained in `node`\n */\nexport function forEachToken(\n\tnode: ts.Node,\n\tcallback: ForEachTokenCallback,\n\tsourceFile: ts.SourceFile = node.getSourceFile(),\n): void {\n\tconst queue = [];\n\twhile (true) {\n\t\tif (ts.isTokenKind(node.kind)) {\n\t\t\tcallback(node);\n\t\t} else if (\n\t\t\t// eslint-disable-next-line deprecation/deprecation -- need for support of TS < 4.7\n\t\t\tnode.kind !== ts.SyntaxKind.JSDocComment\n\t\t) {\n\t\t\tconst children = node.getChildren(sourceFile);\n\t\t\tif (children.length === 1) {\n\t\t\t\tnode = children[0];\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// add children in reverse order, when we pop the next element from the queue, it's the first child\n\t\t\tfor (let i = children.length - 1; i >= 0; --i) {\n\t\t\t\tqueue.push(children[i]);\n\t\t\t}\n\t\t}\n\n\t\tif (queue.length === 0) {\n\t\t\tbreak;\n\t\t}\n\n\t\tnode = queue.pop()!;\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\n/**\n * An option that can be tested with {@link isCompilerOptionEnabled}.\n * @category Compiler Options\n */\nexport type BooleanCompilerOptions = keyof {\n\t[K in keyof ts.CompilerOptions as NonNullable<\n\t\tts.CompilerOptions[K]\n\t> extends boolean\n\t\t? K\n\t\t: never]: unknown;\n};\n\n/**\n * Checks if a given compiler option is enabled.\n * It handles dependencies of options, e.g. `declaration` is implicitly enabled by `composite` or `strictNullChecks` is enabled by `strict`.\n * However, it does not check dependencies that are already checked and reported as errors, e.g. `checkJs` without `allowJs`.\n * This function only handles boolean flags.\n * @category Compiler Options\n * @example\n * ```ts\n * const options = {\n * \tallowJs: true,\n * };\n *\n * isCompilerOptionEnabled(options, \"allowJs\"); // true\n * isCompilerOptionEnabled(options, \"allowSyntheticDefaultImports\"); // false\n * ```\n */\nexport function isCompilerOptionEnabled(\n\toptions: ts.CompilerOptions,\n\toption: BooleanCompilerOptions,\n): boolean {\n\tswitch (option) {\n\t\tcase \"stripInternal\":\n\t\tcase \"declarationMap\":\n\t\tcase \"emitDeclarationOnly\":\n\t\t\treturn (\n\t\t\t\toptions[option] === true &&\n\t\t\t\tisCompilerOptionEnabled(options, \"declaration\")\n\t\t\t);\n\t\tcase \"declaration\":\n\t\t\treturn (\n\t\t\t\toptions.declaration || isCompilerOptionEnabled(options, \"composite\")\n\t\t\t);\n\t\tcase \"incremental\":\n\t\t\treturn options.incremental === undefined\n\t\t\t\t? isCompilerOptionEnabled(options, \"composite\")\n\t\t\t\t: options.incremental;\n\t\tcase \"skipDefaultLibCheck\":\n\t\t\treturn (\n\t\t\t\toptions.skipDefaultLibCheck ||\n\t\t\t\tisCompilerOptionEnabled(options, \"skipLibCheck\")\n\t\t\t);\n\t\tcase \"suppressImplicitAnyIndexErrors\":\n\t\t\treturn (\n\t\t\t\toptions.suppressImplicitAnyIndexErrors === true &&\n\t\t\t\tisCompilerOptionEnabled(options, \"noImplicitAny\")\n\t\t\t);\n\t\tcase \"allowSyntheticDefaultImports\":\n\t\t\treturn options.allowSyntheticDefaultImports !== undefined\n\t\t\t\t? options.allowSyntheticDefaultImports\n\t\t\t\t: isCompilerOptionEnabled(options, \"esModuleInterop\") ||\n\t\t\t\t\t\toptions.module === ts.ModuleKind.System;\n\t\tcase \"noUncheckedIndexedAccess\":\n\t\t\treturn (\n\t\t\t\toptions.noUncheckedIndexedAccess === true &&\n\t\t\t\tisCompilerOptionEnabled(options, \"strictNullChecks\")\n\t\t\t);\n\t\tcase \"allowJs\":\n\t\t\treturn options.allowJs === undefined\n\t\t\t\t? isCompilerOptionEnabled(options, \"checkJs\")\n\t\t\t\t: options.allowJs;\n\t\tcase \"noImplicitAny\":\n\t\tcase \"noImplicitThis\":\n\t\tcase \"strictNullChecks\":\n\t\tcase \"strictFunctionTypes\":\n\t\tcase \"strictPropertyInitialization\":\n\t\tcase \"alwaysStrict\":\n\t\tcase \"strictBindCallApply\":\n\t\t\ttype AssertEqual<T, U extends T> = U; // make sure all strict options are handled here\n\t\t\treturn isStrictCompilerOptionEnabled(\n\t\t\t\toptions,\n\t\t\t\toption as AssertEqual<typeof option, StrictCompilerOption>,\n\t\t\t);\n\t}\n\n\treturn options[option] === true;\n}\n\n/**\n * An option that can be tested with {@link isStrictCompilerOptionEnabled}.\n * @category Compiler Options\n */\nexport type StrictCompilerOption =\n\t| \"alwaysStrict\"\n\t| \"noImplicitAny\"\n\t| \"noImplicitThis\"\n\t| \"strictBindCallApply\"\n\t| \"strictFunctionTypes\"\n\t| \"strictNullChecks\"\n\t| \"strictPropertyInitialization\";\n\n/**\n * Checks if a given compiler option is enabled, accounting for whether all flags\n * (except `strictPropertyInitialization`) have been enabled by `strict: true`.\n * @category Compiler Options\n * @example\n * ```ts\n * const optionsLenient = {\n * \tnoImplicitAny: true,\n * };\n *\n * isStrictCompilerOptionEnabled(optionsLenient, \"noImplicitAny\"); // true\n * isStrictCompilerOptionEnabled(optionsLenient, \"noImplicitThis\"); // false\n * ```\n * @example\n * ```ts\n * const optionsStrict = {\n * \tnoImplicitThis: false,\n * \tstrict: true,\n * };\n *\n * isStrictCompilerOptionEnabled(optionsStrict, \"noImplicitAny\"); // true\n * isStrictCompilerOptionEnabled(optionsStrict, \"noImplicitThis\"); // false\n * ```\n */\nexport function isStrictCompilerOptionEnabled(\n\toptions: ts.CompilerOptions,\n\toption: StrictCompilerOption,\n): boolean {\n\treturn (\n\t\t(options.strict ? options[option] !== false : options[option] === true) &&\n\t\t(option !== \"strictPropertyInitialization\" ||\n\t\t\tisStrictCompilerOptionEnabled(options, \"strictNullChecks\"))\n\t);\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\n/**\n * Test if the given flag is set on the combined flags.\n * @internal\n */\nfunction isFlagSet(allFlags: number, flag: number): boolean {\n\treturn (allFlags & flag) !== 0;\n}\n\n/**\n * Test if the given flag is set on the given object.\n * @internal\n */\nfunction isFlagSetOnObject(obj: { flags: number }, flag: number): boolean {\n\treturn isFlagSet(obj.flags, flag);\n}\n\n/**\n * Test if the given node has the given `ModifierFlags` set.\n * @category Nodes - Flag Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isModifierFlagSet(node, ts.ModifierFlags.Abstract)) {\n *   // ...\n * }\n * ```\n */\nexport function isModifierFlagSet(\n\tnode: ts.Declaration,\n\tflag: ts.ModifierFlags,\n): boolean {\n\treturn isFlagSet(ts.getCombinedModifierFlags(node), flag);\n}\n\n/**\n * Test if the given node has the given `NodeFlags` set.\n * @category Nodes - Flag Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNodeFlagSet(node, ts.NodeFlags.AwaitContext)) {\n *   // ...\n * }\n * ```\n */\nexport const isNodeFlagSet: (node: ts.Node, flag: ts.NodeFlags) => boolean =\n\tisFlagSetOnObject;\n\n/**\n * Test if the given node has the given `ObjectFlags` set.\n * @category Nodes - Flag Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isObjectFlagSet(node, ts.ObjectFlags.Anonymous)) {\n *   // ...\n * }\n * ```\n */\nexport function isObjectFlagSet(\n\tobjectType: ts.ObjectType,\n\tflag: ts.ObjectFlags,\n): boolean {\n\treturn isFlagSet(objectType.objectFlags, flag);\n}\n\n/**\n * Test if the given node has the given `SymbolFlags` set.\n * @category Nodes - Flag Utilities\n * @example\n * ```ts\n * declare const symbol: ts.Symbol;\n *\n * if (isSymbolFlagSet(symbol, ts.SymbolFlags.Accessor)) {\n *   // ...\n * }\n * ```\n */\nexport const isSymbolFlagSet: (\n\tsymbol: ts.Symbol,\n\tflag: ts.SymbolFlags,\n) => boolean = isFlagSetOnObject;\n\n/**\n * Test if the given node has the given `TypeFlags` set.\n * @category Nodes - Flag Utilities\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTypeFlagSet(type, ts.TypeFlags.Any)) {\n *   // ...\n * }\n * ```\n */\nexport const isTypeFlagSet: (type: ts.Type, flag: ts.TypeFlags) => boolean =\n\tisFlagSetOnObject;\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\n/**\n * Test if the given iterable includes a modifier of any of the given kinds.\n * @category Modifier Utilities\n * @example\n * ```ts\n * declare const modifiers: ts.Modifier[];\n *\n * includesModifier(modifiers, ts.SyntaxKind.AbstractKeyword);\n * ```\n */\nexport function includesModifier(\n\tmodifiers: Iterable<ts.ModifierLike> | undefined,\n\t...kinds: ts.ModifierSyntaxKind[]\n): boolean {\n\tif (modifiers === undefined) {\n\t\treturn false;\n\t}\n\n\tfor (const modifier of modifiers) {\n\t\tif (kinds.includes(modifier.kind as ts.ModifierSyntaxKind)) {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { isAssignmentKind } from \"../syntax\";\n\n/* eslint-disable perfectionist/sort-enums */\n/**\n * What operations(s), if any, an expression applies.\n */\nexport enum AccessKind {\n\tNone = 0,\n\tRead = 1 << 0,\n\tWrite = 1 << 1,\n\tDelete = 1 << 2,\n\tReadWrite = Read | Write,\n\t/* eslint-enable perfectionist/sort-enums */\n}\n\n/**\n * Determines which operation(s), if any, an expression applies.\n * @example\n * ```ts\n * declare const node: ts.Expression;\n *\n * if (getAccessKind(node).Write & AccessKind.Write) !== 0) {\n *   // this is a reassignment (write)\n * }\n * ```\n */\nexport function getAccessKind(node: ts.Expression): AccessKind {\n\tconst parent = node.parent;\n\tswitch (parent.kind) {\n\t\tcase ts.SyntaxKind.DeleteExpression:\n\t\t\treturn AccessKind.Delete;\n\t\tcase ts.SyntaxKind.PostfixUnaryExpression:\n\t\t\treturn AccessKind.ReadWrite;\n\t\tcase ts.SyntaxKind.PrefixUnaryExpression:\n\t\t\treturn (parent as ts.PrefixUnaryExpression).operator ===\n\t\t\t\tts.SyntaxKind.PlusPlusToken ||\n\t\t\t\t(parent as ts.PrefixUnaryExpression).operator ===\n\t\t\t\t\tts.SyntaxKind.MinusMinusToken\n\t\t\t\t? AccessKind.ReadWrite\n\t\t\t\t: AccessKind.Read;\n\t\tcase ts.SyntaxKind.BinaryExpression:\n\t\t\treturn (parent as ts.BinaryExpression).right === node\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: !isAssignmentKind((parent as ts.BinaryExpression).operatorToken.kind)\n\t\t\t\t\t? AccessKind.Read\n\t\t\t\t\t: (parent as ts.BinaryExpression).operatorToken.kind ===\n\t\t\t\t\t\t  ts.SyntaxKind.EqualsToken\n\t\t\t\t\t\t? AccessKind.Write\n\t\t\t\t\t\t: AccessKind.ReadWrite;\n\t\tcase ts.SyntaxKind.ShorthandPropertyAssignment:\n\t\t\treturn (parent as ts.ShorthandPropertyAssignment)\n\t\t\t\t.objectAssignmentInitializer === node\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: isInDestructuringAssignment(parent as ts.ShorthandPropertyAssignment)\n\t\t\t\t\t? AccessKind.Write\n\t\t\t\t\t: AccessKind.Read;\n\t\tcase ts.SyntaxKind.PropertyAssignment:\n\t\t\treturn (parent as ts.PropertyAssignment).name === node\n\t\t\t\t? AccessKind.None\n\t\t\t\t: isInDestructuringAssignment(parent as ts.PropertyAssignment)\n\t\t\t\t\t? AccessKind.Write\n\t\t\t\t\t: AccessKind.Read;\n\t\tcase ts.SyntaxKind.ArrayLiteralExpression:\n\t\tcase ts.SyntaxKind.SpreadElement:\n\t\tcase ts.SyntaxKind.SpreadAssignment:\n\t\t\treturn isInDestructuringAssignment(\n\t\t\t\tparent as\n\t\t\t\t\t| ts.ArrayLiteralExpression\n\t\t\t\t\t| ts.SpreadAssignment\n\t\t\t\t\t| ts.SpreadElement,\n\t\t\t)\n\t\t\t\t? AccessKind.Write\n\t\t\t\t: AccessKind.Read;\n\t\tcase ts.SyntaxKind.ParenthesizedExpression:\n\t\tcase ts.SyntaxKind.NonNullExpression:\n\t\tcase ts.SyntaxKind.TypeAssertionExpression:\n\t\tcase ts.SyntaxKind.AsExpression:\n\t\t\t// (<number>foo! as {})++\n\t\t\treturn getAccessKind(parent as ts.Expression);\n\t\tcase ts.SyntaxKind.ForOfStatement:\n\t\tcase ts.SyntaxKind.ForInStatement:\n\t\t\treturn (parent as ts.ForInOrOfStatement).initializer === node\n\t\t\t\t? AccessKind.Write\n\t\t\t\t: AccessKind.Read;\n\t\tcase ts.SyntaxKind.ExpressionWithTypeArguments:\n\t\t\treturn (\n\t\t\t\t(parent as ts.ExpressionWithTypeArguments).parent as ts.HeritageClause\n\t\t\t).token === ts.SyntaxKind.ExtendsKeyword &&\n\t\t\t\tparent.parent.parent.kind !== ts.SyntaxKind.InterfaceDeclaration\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: AccessKind.None;\n\t\tcase ts.SyntaxKind.ComputedPropertyName:\n\t\tcase ts.SyntaxKind.ExpressionStatement:\n\t\tcase ts.SyntaxKind.TypeOfExpression:\n\t\tcase ts.SyntaxKind.ElementAccessExpression:\n\t\tcase ts.SyntaxKind.ForStatement:\n\t\tcase ts.SyntaxKind.IfStatement:\n\t\tcase ts.SyntaxKind.DoStatement:\n\t\tcase ts.SyntaxKind.WhileStatement:\n\t\tcase ts.SyntaxKind.SwitchStatement:\n\t\tcase ts.SyntaxKind.WithStatement:\n\t\tcase ts.SyntaxKind.ThrowStatement:\n\t\tcase ts.SyntaxKind.CallExpression:\n\t\tcase ts.SyntaxKind.NewExpression:\n\t\tcase ts.SyntaxKind.TaggedTemplateExpression:\n\t\tcase ts.SyntaxKind.JsxExpression:\n\t\tcase ts.SyntaxKind.Decorator:\n\t\tcase ts.SyntaxKind.TemplateSpan:\n\t\tcase ts.SyntaxKind.JsxOpeningElement:\n\t\tcase ts.SyntaxKind.JsxSelfClosingElement:\n\t\tcase ts.SyntaxKind.JsxSpreadAttribute:\n\t\tcase ts.SyntaxKind.VoidExpression:\n\t\tcase ts.SyntaxKind.ReturnStatement:\n\t\tcase ts.SyntaxKind.AwaitExpression:\n\t\tcase ts.SyntaxKind.YieldExpression:\n\t\tcase ts.SyntaxKind.ConditionalExpression:\n\t\tcase ts.SyntaxKind.CaseClause:\n\t\tcase ts.SyntaxKind.JsxElement:\n\t\t\treturn AccessKind.Read;\n\t\tcase ts.SyntaxKind.ArrowFunction:\n\t\t\treturn (parent as ts.ArrowFunction).body === node\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: AccessKind.Write;\n\t\tcase ts.SyntaxKind.PropertyDeclaration:\n\t\tcase ts.SyntaxKind.VariableDeclaration:\n\t\tcase ts.SyntaxKind.Parameter:\n\t\tcase ts.SyntaxKind.EnumMember:\n\t\tcase ts.SyntaxKind.BindingElement:\n\t\tcase ts.SyntaxKind.JsxAttribute:\n\t\t\treturn (parent as ts.JsxAttribute).initializer === node\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: AccessKind.None;\n\t\tcase ts.SyntaxKind.PropertyAccessExpression:\n\t\t\treturn (parent as ts.PropertyAccessExpression).expression === node\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: AccessKind.None;\n\t\tcase ts.SyntaxKind.ExportAssignment:\n\t\t\treturn (parent as ts.ExportAssignment).isExportEquals\n\t\t\t\t? AccessKind.Read\n\t\t\t\t: AccessKind.None;\n\t}\n\n\treturn AccessKind.None;\n}\n\nfunction isInDestructuringAssignment(\n\tnode:\n\t\t| ts.ArrayLiteralExpression\n\t\t| ts.ObjectLiteralExpression\n\t\t| ts.PropertyAssignment\n\t\t| ts.ShorthandPropertyAssignment\n\t\t| ts.SpreadAssignment\n\t\t| ts.SpreadElement,\n): boolean {\n\tswitch (node.kind) {\n\t\tcase ts.SyntaxKind.ShorthandPropertyAssignment:\n\t\t\tif (node.objectAssignmentInitializer !== undefined) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t// falls through\n\t\tcase ts.SyntaxKind.PropertyAssignment:\n\t\tcase ts.SyntaxKind.SpreadAssignment:\n\t\t\tnode = node.parent as\n\t\t\t\t| ts.ArrayLiteralExpression\n\t\t\t\t| ts.ObjectLiteralExpression;\n\t\t\tbreak;\n\t\tcase ts.SyntaxKind.SpreadElement:\n\t\t\tif (node.parent.kind !== ts.SyntaxKind.ArrayLiteralExpression) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tnode = node.parent;\n\t}\n\n\twhile (true) {\n\t\tswitch (node.parent.kind) {\n\t\t\tcase ts.SyntaxKind.BinaryExpression:\n\t\t\t\treturn (\n\t\t\t\t\t(node.parent as ts.BinaryExpression).left === node &&\n\t\t\t\t\t(node.parent as ts.BinaryExpression).operatorToken.kind ===\n\t\t\t\t\t\tts.SyntaxKind.EqualsToken\n\t\t\t\t);\n\t\t\tcase ts.SyntaxKind.ForOfStatement:\n\t\t\t\treturn (node.parent as ts.ForOfStatement).initializer === node;\n\t\t\tcase ts.SyntaxKind.ArrayLiteralExpression:\n\t\t\tcase ts.SyntaxKind.ObjectLiteralExpression:\n\t\t\t\tnode = node.parent as\n\t\t\t\t\t| ts.ArrayLiteralExpression\n\t\t\t\t\t| ts.ObjectLiteralExpression;\n\t\t\t\tbreak;\n\t\t\tcase ts.SyntaxKind.SpreadAssignment:\n\t\t\tcase ts.SyntaxKind.PropertyAssignment:\n\t\t\t\tnode = node.parent.parent as ts.ObjectLiteralExpression;\n\t\t\t\tbreak;\n\t\t\tcase ts.SyntaxKind.SpreadElement:\n\t\t\t\tif (node.parent.parent.kind !== ts.SyntaxKind.ArrayLiteralExpression) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tnode = node.parent.parent as ts.ArrayLiteralExpression;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\treturn false;\n\t\t}\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\n/**\n * Test of the kind given is for assignment.\n * @category Syntax Utilities\n * @example\n * ```ts\n * declare const kind: ts.SyntaxKind;\n *\n * isAssignmentKind(kind);\n * ```\n */\nexport function isAssignmentKind(kind: ts.SyntaxKind): boolean {\n\treturn (\n\t\tkind >= ts.SyntaxKind.FirstAssignment &&\n\t\tkind <= ts.SyntaxKind.LastAssignment\n\t);\n}\n\n/**\n * Test if a string is numeric.\n * @category Syntax Utilities\n * @example\n * ```ts\n * isNumericPropertyName(\"abc\"); // false\n * isNumericPropertyName(\"123\"); // true\n * ```\n */\nexport function isNumericPropertyName(name: string | ts.__String): boolean {\n\treturn String(+name) === name;\n}\n\nfunction charSize(ch: number) {\n\treturn ch >= 0x10000 ? 2 : 1;\n}\n\n/**\n * Determines whether the given text can be used to access a property with a `PropertyAccessExpression` while preserving the property's name.\n * @category Syntax Utilities\n * @example\n * ```ts\n * isValidPropertyAccess(\"abc\"); // true\n * isValidPropertyAccess(\"123\"); // false\n * ```\n */\nexport function isValidPropertyAccess(\n\ttext: string,\n\tlanguageVersion = ts.ScriptTarget.Latest,\n): boolean {\n\tif (text.length === 0) {\n\t\treturn false;\n\t}\n\n\tlet ch = text.codePointAt(0)!;\n\tif (!ts.isIdentifierStart(ch, languageVersion)) {\n\t\treturn false;\n\t}\n\n\tfor (let i = charSize(ch); i < text.length; i += charSize(ch)) {\n\t\tch = text.codePointAt(i)!;\n\t\tif (!ts.isIdentifierPart(ch, languageVersion)) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n", "import ts from \"typescript\";\n\nimport { isSuperExpression } from \"./single\";\nimport {\n\tisDeclarationName,\n\tisEntityNameExpression,\n\tisJSDocNamespaceBody,\n\tisJsxTagNameExpression,\n\tisNamespaceBody,\n} from \"./union\";\n\n/**\n * An `AssertionExpression` that is declared as const.\n * @category Node Types\n */\nexport type ConstAssertionExpression = ts.AssertionExpression & {\n\ttype: ts.TypeReferenceNode;\n\ttypeName: ConstAssertionIdentifier;\n};\n\n/**\n * An `Identifier` with an `escapedText` value of `\"const\"`.\n * @category Node Types\n */\nexport type ConstAssertionIdentifier = ts.Identifier & {\n\tescapedText: ts.__String & \"const\";\n};\n\n/**\n * Test if a node is a {@link ConstAssertionExpression}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isConstAssertionExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link ConstAssertionExpression}.\n */\nexport function isConstAssertionExpression(\n\tnode: ts.AssertionExpression,\n): node is ConstAssertionExpression {\n\treturn (\n\t\tts.isTypeReferenceNode(node.type) &&\n\t\tts.isIdentifier(node.type.typeName) &&\n\t\tnode.type.typeName.escapedText === \"const\"\n\t);\n}\n\n/**\n * Test if a node is an `IterationStatement`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isIterationStatement(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `IterationStatement`.\n */\nexport function isIterationStatement(\n\tnode: ts.Node,\n): node is ts.IterationStatement {\n\tswitch (node.kind) {\n\t\tcase ts.SyntaxKind.DoStatement:\n\t\tcase ts.SyntaxKind.ForInStatement:\n\t\tcase ts.SyntaxKind.ForOfStatement:\n\t\tcase ts.SyntaxKind.ForStatement:\n\t\tcase ts.SyntaxKind.WhileStatement:\n\t\t\treturn true;\n\t\tdefault:\n\t\t\treturn false;\n\t}\n}\n\n/**\n * Test if a node is a `JSDocNamespaceDeclaration`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJSDocNamespaceDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JSDocNamespaceDeclaration`.\n */\nexport function isJSDocNamespaceDeclaration(\n\tnode: ts.Node,\n): node is ts.JSDocNamespaceDeclaration {\n\treturn (\n\t\tts.isModuleDeclaration(node) &&\n\t\tts.isIdentifier(node.name) &&\n\t\t(node.body === undefined || isJSDocNamespaceBody(node.body))\n\t);\n}\n\n/**\n * Test if a node is a `JsxTagNamePropertyAccess`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsxTagNamePropertyAccess(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsxTagNamePropertyAccess`.\n */\nexport function isJsxTagNamePropertyAccess(\n\tnode: ts.Node,\n): node is ts.JsxTagNamePropertyAccess {\n\treturn (\n\t\tts.isPropertyAccessExpression(node) &&\n\t\t// eslint-disable-next-line deprecation/deprecation -- Keep compatibility with ts < 5\n\t\tisJsxTagNameExpression(node.expression)\n\t);\n}\n\n/**\n * a `NamedDeclaration` that definitely has a name.\n * @category Node Types\n */\nexport interface NamedDeclarationWithName extends ts.NamedDeclaration {\n\tname: ts.DeclarationName;\n}\n\n/**\n * Test if a node is a {@link NamedDeclarationWithName}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNamedDeclarationWithName(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link NamedDeclarationWithName}.\n */\nexport function isNamedDeclarationWithName(\n\tnode: ts.Declaration,\n): node is NamedDeclarationWithName {\n\treturn (\n\t\t\"name\" in node &&\n\t\tnode.name !== undefined &&\n\t\tnode.name !== null &&\n\t\tisDeclarationName(node.name as ts.Node)\n\t);\n}\n\n/**\n * Test if a node is a `NamespaceDeclaration`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNamespaceDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `NamespaceDeclaration`.\n */\nexport function isNamespaceDeclaration(\n\tnode: ts.Node,\n): node is ts.NamespaceDeclaration {\n\treturn (\n\t\tts.isModuleDeclaration(node) &&\n\t\tts.isIdentifier(node.name) &&\n\t\tnode.body !== undefined &&\n\t\tisNamespaceBody(node.body)\n\t);\n}\n\n/**\n * A number or string-like literal.\n * @category Node Types\n */\nexport type NumericOrStringLikeLiteral =\n\t| ts.NumericLiteral\n\t| ts.StringLiteralLike;\n\n/**\n * Test if a node is a {@link NumericOrStringLikeLiteral}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNumericOrStringLikeLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link NumericOrStringLikeLiteral}.\n */\nexport function isNumericOrStringLikeLiteral(\n\tnode: ts.Node,\n): node is NumericOrStringLikeLiteral {\n\tswitch (node.kind) {\n\t\tcase ts.SyntaxKind.StringLiteral:\n\t\tcase ts.SyntaxKind.NumericLiteral:\n\t\tcase ts.SyntaxKind.NoSubstitutionTemplateLiteral:\n\t\t\treturn true;\n\t\tdefault:\n\t\t\treturn false;\n\t}\n}\n\n/**\n * Test if a node is a `PropertyAccessEntityNameExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isPropertyAccessEntityNameExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `PropertyAccessEntityNameExpression`.\n */\nexport function isPropertyAccessEntityNameExpression(\n\tnode: ts.Node,\n): node is ts.PropertyAccessEntityNameExpression {\n\treturn (\n\t\tts.isPropertyAccessExpression(node) &&\n\t\tts.isIdentifier(node.name) &&\n\t\tisEntityNameExpression(node.expression)\n\t);\n}\n\n/**\n * Test if a node is a `SuperElementAccessExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSuperElementAccessExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `SuperElementAccessExpression`.\n */\nexport function isSuperElementAccessExpression(\n\tnode: ts.Node,\n): node is ts.SuperElementAccessExpression {\n\treturn (\n\t\tts.isElementAccessExpression(node) && isSuperExpression(node.expression)\n\t);\n}\n\n/**\n * Test if a node is a `SuperPropertyAccessExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSuperPropertyAccessExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `SuperPropertyAccessExpression`.\n */\nexport function isSuperPropertyAccessExpression(\n\tnode: ts.Node,\n): node is ts.SuperPropertyAccessExpression {\n\treturn (\n\t\tts.isPropertyAccessExpression(node) && isSuperExpression(node.expression)\n\t);\n}\n", "import ts from \"typescript\";\n\n/**\n * A node that represents the any keyword.\n * @category Node Types\n */\nexport type AnyKeyword = ts.KeywordToken<ts.SyntaxKind.AnyKeyword>;\n\n/**\n * A node that represents the bigint keyword.\n * @category Node Types\n */\nexport type BigIntKeyword = ts.KeywordToken<ts.SyntaxKind.BigIntKeyword>;\n\n/**\n * A node that represents the boolean keyword.\n * @category Node Types\n */\nexport type BooleanKeyword = ts.KeywordToken<ts.SyntaxKind.BooleanKeyword>;\n\n/**\n * A node that represents the false keyword.\n * @category Node Types\n */\nexport type FalseKeyword = ts.KeywordToken<ts.SyntaxKind.FalseKeyword>;\n\n/**\n * A node that represents the import keyword.\n * @category Node Types\n */\nexport type ImportKeyword = ts.KeywordToken<ts.SyntaxKind.ImportKeyword>;\n\n/**\n * A node that represents the never keyword.\n * @category Node Types\n */\nexport type NeverKeyword = ts.KeywordToken<ts.SyntaxKind.NeverKeyword>;\n\n/**\n * A node that represents the null keyword.\n * @category Node Types\n */\nexport type NullKeyword = ts.KeywordToken<ts.SyntaxKind.NullKeyword>;\n\n/**\n * A node that represents the number keyword.\n * @category Node Types\n */\nexport type NumberKeyword = ts.KeywordToken<ts.SyntaxKind.NumberKeyword>;\n\n/**\n * A node that represents the object keyword.\n * @category Node Types\n */\nexport type ObjectKeyword = ts.KeywordToken<ts.SyntaxKind.ObjectKeyword>;\n\n/**\n * A node that represents the string keyword.\n * @category Node Types\n */\nexport type StringKeyword = ts.KeywordToken<ts.SyntaxKind.StringKeyword>;\n\n/**\n * A node that represents the super keyword.\n * @category Node Types\n */\nexport type SuperKeyword = ts.KeywordToken<ts.SyntaxKind.SuperKeyword>;\n\n/**\n * A node that represents the symbol keyword.\n * @category Node Types\n */\nexport type SymbolKeyword = ts.KeywordToken<ts.SyntaxKind.SymbolKeyword>;\n\n/**\n * A node that represents the this keyword.\n * @category Node Types\n */\nexport type ThisKeyword = ts.KeywordToken<ts.SyntaxKind.ThisKeyword>;\n\n/**\n * A node that represents the true keyword.\n * @category Node Types\n */\nexport type TrueKeyword = ts.KeywordToken<ts.SyntaxKind.TrueKeyword>;\n\n/**\n * A node that represents the undefined keyword.\n * @category Node Types\n */\nexport type UndefinedKeyword = ts.KeywordToken<ts.SyntaxKind.UndefinedKeyword>;\n\n/**\n * A node that represents the unknown keyword.\n * @category Node Types\n */\nexport type UnknownKeyword = ts.KeywordToken<ts.SyntaxKind.UnknownKeyword>;\n\n/**\n * A node that represents the void keyword.\n * @category Node Types\n */\nexport type VoidKeyword = ts.KeywordToken<ts.SyntaxKind.VoidKeyword>;\n\n/**\n * Test if a node is an `AbstractKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAbstractKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AbstractKeyword`.\n */\nexport function isAbstractKeyword(node: ts.Node): node is ts.AbstractKeyword {\n\treturn node.kind === ts.SyntaxKind.AbstractKeyword;\n}\n\n/**\n * Test if a node is an `AccessorKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAccessorKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AccessorKeyword`.\n */\nexport function isAccessorKeyword(node: ts.Node): node is ts.AccessorKeyword {\n\treturn node.kind === ts.SyntaxKind.AccessorKeyword;\n}\n\n/**\n * Test if a node is an {@link AnyKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAnyKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an {@link AnyKeyword}.\n */\nexport function isAnyKeyword(node: ts.Node): node is AnyKeyword {\n\treturn node.kind === ts.SyntaxKind.AnyKeyword;\n}\n\n/**\n * Test if a node is an `AssertKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAssertKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AssertKeyword`.\n */\nexport function isAssertKeyword(node: ts.Node): node is ts.AssertKeyword {\n\treturn node.kind === ts.SyntaxKind.AssertKeyword;\n}\n\n/**\n * Test if a node is an `AssertsKeyword`.\n * @deprecated With TypeScript v5, in favor of typescript's `isAssertsKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAssertsKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AssertsKeyword`.\n */\nexport function isAssertsKeyword(node: ts.Node): node is ts.AssertsKeyword {\n\treturn node.kind === ts.SyntaxKind.AssertsKeyword;\n}\n\n/**\n * Test if a node is an `AsyncKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAsyncKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AsyncKeyword`.\n */\nexport function isAsyncKeyword(node: ts.Node): node is ts.AsyncKeyword {\n\treturn node.kind === ts.SyntaxKind.AsyncKeyword;\n}\n\n/**\n * Test if a node is an `AwaitKeyword`.\n * @deprecated With TypeScript v5, in favor of typescript's `isAwaitKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAwaitKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AwaitKeyword`.\n */\nexport function isAwaitKeyword(node: ts.Node): node is ts.AwaitKeyword {\n\treturn node.kind === ts.SyntaxKind.AwaitKeyword;\n}\n\n/**\n * Test if a node is a {@link BigIntKeyword}.\n * @deprecated With TypeScript v5, in favor of typescript's `isBigIntKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBigIntKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link BigIntKeyword}.\n */\nexport function isBigIntKeyword(node: ts.Node): node is BigIntKeyword {\n\treturn node.kind === ts.SyntaxKind.BigIntKeyword;\n}\n\n/**\n * Test if a node is a {@link BooleanKeyword}.\n * @deprecated With TypeScript v5, in favor of typescript's `isBooleanKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBooleanKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link BooleanKeyword}.\n */\nexport function isBooleanKeyword(node: ts.Node): node is BooleanKeyword {\n\treturn node.kind === ts.SyntaxKind.BooleanKeyword;\n}\n\n/**\n * Test if a node is a `ColonToken`.\n * @deprecated With TypeScript v5, in favor of typescript's `isColonToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isColonToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ColonToken`.\n */\nexport function isColonToken(node: ts.Node): node is ts.ColonToken {\n\treturn node.kind === ts.SyntaxKind.ColonToken;\n}\n\n/**\n * Test if a node is a `ConstKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isConstKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ConstKeyword`.\n */\nexport function isConstKeyword(node: ts.Node): node is ts.ConstKeyword {\n\treturn node.kind === ts.SyntaxKind.ConstKeyword;\n}\n\n/**\n * Test if a node is a `DeclareKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDeclareKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DeclareKeyword`.\n */\nexport function isDeclareKeyword(node: ts.Node): node is ts.DeclareKeyword {\n\treturn node.kind === ts.SyntaxKind.DeclareKeyword;\n}\n\n/**\n * Test if a node is a `DefaultKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDefaultKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DefaultKeyword`.\n */\nexport function isDefaultKeyword(node: ts.Node): node is ts.DefaultKeyword {\n\treturn node.kind === ts.SyntaxKind.DefaultKeyword;\n}\n\n/**\n * Test if a node is a `DotToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDotToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DotToken`.\n */\nexport function isDotToken(node: ts.Node): node is ts.DotToken {\n\treturn node.kind === ts.SyntaxKind.DotToken;\n}\n\n/**\n * Test if a node is an `EndOfFileToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isEndOfFileToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `EndOfFileToken`.\n */\nexport function isEndOfFileToken(node: ts.Node): node is ts.EndOfFileToken {\n\treturn node.kind === ts.SyntaxKind.EndOfFileToken;\n}\n\n/**\n * Test if a node is an `EqualsGreaterThanToken`.\n * @deprecated With TypeScript v5, in favor of typescript's `isEqualsGreaterThanToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isEqualsGreaterThanToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `EqualsGreaterThanToken`.\n */\nexport function isEqualsGreaterThanToken(\n\tnode: ts.Node,\n): node is ts.EqualsGreaterThanToken {\n\treturn node.kind === ts.SyntaxKind.EqualsGreaterThanToken;\n}\n\n/**\n * Test if a node is an `EqualsToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isEqualsToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `EqualsToken`.\n */\nexport function isEqualsToken(node: ts.Node): node is ts.EqualsToken {\n\treturn node.kind === ts.SyntaxKind.EqualsToken;\n}\n\n/**\n * Test if a node is an `ExclamationToken`.\n * @deprecated With TypeScript v5, in favor of typescript's `isExclamationToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isExclamationToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ExclamationToken`.\n */\nexport function isExclamationToken(node: ts.Node): node is ts.ExclamationToken {\n\treturn node.kind === ts.SyntaxKind.ExclamationToken;\n}\n\n/**\n * Test if a node is an `ExportKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isExportKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ExportKeyword`.\n */\nexport function isExportKeyword(node: ts.Node): node is ts.ExportKeyword {\n\treturn node.kind === ts.SyntaxKind.ExportKeyword;\n}\n\n/**\n * Test if a node is a {@link FalseKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isFalseKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link FalseKeyword}.\n */\nexport function isFalseKeyword(node: ts.Node): node is FalseKeyword {\n\treturn node.kind === ts.SyntaxKind.FalseKeyword;\n}\n\n/**\n * Test if a node is a `FalseLiteral`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isFalseLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `FalseLiteral`.\n */\nexport function isFalseLiteral(node: ts.Node): node is ts.FalseLiteral {\n\treturn node.kind === ts.SyntaxKind.FalseKeyword;\n}\n\n/**\n * Test if a node is an `ImportExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isImportExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ImportExpression`.\n */\nexport function isImportExpression(node: ts.Node): node is ts.ImportExpression {\n\treturn node.kind === ts.SyntaxKind.ImportKeyword;\n}\n\n/**\n * Test if a node is an {@link ImportKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isImportKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an {@link ImportKeyword}.\n */\nexport function isImportKeyword(node: ts.Node): node is ImportKeyword {\n\treturn node.kind === ts.SyntaxKind.ImportKeyword;\n}\n\n/**\n * Test if a node is an `InKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isInKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `InKeyword`.\n */\nexport function isInKeyword(node: ts.Node): node is ts.InKeyword {\n\treturn node.kind === ts.SyntaxKind.InKeyword;\n}\n\n/* eslint-disable deprecation/deprecation */\n/**\n * Test if a node is an `InputFiles`.\n * @deprecated With TypeScript v5\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isInputFiles(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `InputFiles`.\n */\nexport function isInputFiles(node: ts.Node): node is ts.InputFiles {\n\treturn node.kind === ts.SyntaxKind.InputFiles;\n}\n/* eslint-enable deprecation/deprecation */\n\n/**\n * Test if a node is a `JSDocText`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJSDocText(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JSDocText`.\n */\nexport function isJSDocText(node: ts.Node): node is ts.JSDocText {\n\treturn node.kind === ts.SyntaxKind.JSDocText;\n}\n\n/**\n * Test if a node is a `JsonMinusNumericLiteral`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsonMinusNumericLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsonMinusNumericLiteral`.\n */\nexport function isJsonMinusNumericLiteral(\n\tnode: ts.Node,\n): node is ts.JsonMinusNumericLiteral {\n\treturn node.kind === ts.SyntaxKind.PrefixUnaryExpression;\n}\n\n/**\n * Test if a node is a {@link NeverKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNeverKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link NeverKeyword}.\n */\nexport function isNeverKeyword(node: ts.Node): node is NeverKeyword {\n\treturn node.kind === ts.SyntaxKind.NeverKeyword;\n}\n\n/**\n * Test if a node is a {@link NullKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNullKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link NullKeyword}.\n */\nexport function isNullKeyword(node: ts.Node): node is NullKeyword {\n\treturn node.kind === ts.SyntaxKind.NullKeyword;\n}\n\n/**\n * Test if a node is a `NullLiteral`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNullLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `NullLiteral`.\n */\nexport function isNullLiteral(node: ts.Node): node is ts.NullLiteral {\n\treturn node.kind === ts.SyntaxKind.NullKeyword;\n}\n\n/**\n * Test if a node is a {@link NumberKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNumberKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link NumberKeyword}.\n */\nexport function isNumberKeyword(node: ts.Node): node is NumberKeyword {\n\treturn node.kind === ts.SyntaxKind.NumberKeyword;\n}\n\n/**\n * Test if a node is an {@link ObjectKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isObjectKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an {@link ObjectKeyword}.\n */\nexport function isObjectKeyword(node: ts.Node): node is ObjectKeyword {\n\treturn node.kind === ts.SyntaxKind.ObjectKeyword;\n}\n\n/**\n * Test if a node is an `OutKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isOutKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `OutKeyword`.\n */\nexport function isOutKeyword(node: ts.Node): node is ts.OutKeyword {\n\treturn node.kind === ts.SyntaxKind.OutKeyword;\n}\n\n/**\n * Test if a node is an `OverrideKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isOverrideKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `OverrideKeyword`.\n */\nexport function isOverrideKeyword(node: ts.Node): node is ts.OverrideKeyword {\n\treturn node.kind === ts.SyntaxKind.OverrideKeyword;\n}\n\n/**\n * Test if a node is a `PrivateKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isPrivateKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `PrivateKeyword`.\n */\nexport function isPrivateKeyword(node: ts.Node): node is ts.PrivateKeyword {\n\treturn node.kind === ts.SyntaxKind.PrivateKeyword;\n}\n\n/**\n * Test if a node is a `ProtectedKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isProtectedKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ProtectedKeyword`.\n */\nexport function isProtectedKeyword(node: ts.Node): node is ts.ProtectedKeyword {\n\treturn node.kind === ts.SyntaxKind.ProtectedKeyword;\n}\n\n/**\n * Test if a node is a `PublicKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isPublicKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `PublicKeyword`.\n */\nexport function isPublicKeyword(node: ts.Node): node is ts.PublicKeyword {\n\treturn node.kind === ts.SyntaxKind.PublicKeyword;\n}\n\n/**\n * Test if a node is a `QuestionDotToken`.\n * @deprecated With TypeScript v5, in favor of typescript's `isQuestionDotToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isQuestionDotToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `QuestionDotToken`.\n */\nexport function isQuestionDotToken(node: ts.Node): node is ts.QuestionDotToken {\n\treturn node.kind === ts.SyntaxKind.QuestionDotToken;\n}\n\n/**\n * Test if a node is a `QuestionToken`.\n * @deprecated With TypeScript v5, in favor of typescript's `isQuestionToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isQuestionToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `QuestionToken`.\n */\nexport function isQuestionToken(node: ts.Node): node is ts.QuestionToken {\n\treturn node.kind === ts.SyntaxKind.QuestionToken;\n}\n\n/**\n * Test if a node is a `ReadonlyKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isReadonlyKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ReadonlyKeyword`.\n */\nexport function isReadonlyKeyword(node: ts.Node): node is ts.ReadonlyKeyword {\n\treturn node.kind === ts.SyntaxKind.ReadonlyKeyword;\n}\n\n/**\n * Test if a node is a `StaticKeyword`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isStaticKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `StaticKeyword`.\n */\nexport function isStaticKeyword(node: ts.Node): node is ts.StaticKeyword {\n\treturn node.kind === ts.SyntaxKind.StaticKeyword;\n}\n\n/**\n * Test if a node is a {@link StringKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isStringKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link StringKeyword}.\n */\nexport function isStringKeyword(node: ts.Node): node is StringKeyword {\n\treturn node.kind === ts.SyntaxKind.StringKeyword;\n}\n\n/**\n * Test if a node is a `SuperExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSuperExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `SuperExpression`.\n */\nexport function isSuperExpression(node: ts.Node): node is ts.SuperExpression {\n\treturn node.kind === ts.SyntaxKind.SuperKeyword;\n}\n\n/**\n * Test if a node is a {@link SuperKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSuperKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link SuperKeyword}.\n */\nexport function isSuperKeyword(node: ts.Node): node is SuperKeyword {\n\treturn node.kind === ts.SyntaxKind.SuperKeyword;\n}\n\n/**\n * Test if a node is a {@link SymbolKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSymbolKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link SymbolKeyword}.\n */\nexport function isSymbolKeyword(node: ts.Node): node is SymbolKeyword {\n\treturn node.kind === ts.SyntaxKind.SymbolKeyword;\n}\n\n/**\n * Test if a node is a `SyntaxList`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSyntaxList(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `SyntaxList`.\n */\nexport function isSyntaxList(node: ts.Node): node is ts.SyntaxList {\n\treturn node.kind === ts.SyntaxKind.SyntaxList;\n}\n\n/**\n * Test if a node is a `ThisExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isThisExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ThisExpression`.\n */\nexport function isThisExpression(node: ts.Node): node is ts.ThisExpression {\n\treturn node.kind === ts.SyntaxKind.ThisKeyword;\n}\n\n/**\n * Test if a node is a {@link ThisKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isThisKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link ThisKeyword}.\n */\nexport function isThisKeyword(node: ts.Node): node is ThisKeyword {\n\treturn node.kind === ts.SyntaxKind.ThisKeyword;\n}\n\n/**\n * Test if a node is a {@link TrueKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isTrueKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link TrueKeyword}.\n */\nexport function isTrueKeyword(node: ts.Node): node is TrueKeyword {\n\treturn node.kind === ts.SyntaxKind.TrueKeyword;\n}\n\n/**\n * Test if a node is a `TrueLiteral`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isTrueLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `TrueLiteral`.\n */\nexport function isTrueLiteral(node: ts.Node): node is ts.TrueLiteral {\n\treturn node.kind === ts.SyntaxKind.TrueKeyword;\n}\n\n/**\n * Test if a node is an {@link UndefinedKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isUndefinedKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an {@link UndefinedKeyword}.\n */\nexport function isUndefinedKeyword(node: ts.Node): node is UndefinedKeyword {\n\treturn node.kind === ts.SyntaxKind.UndefinedKeyword;\n}\n\n/**\n * Test if a node is an {@link UnknownKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isUnknownKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an {@link UnknownKeyword}.\n */\nexport function isUnknownKeyword(node: ts.Node): node is UnknownKeyword {\n\treturn node.kind === ts.SyntaxKind.UnknownKeyword;\n}\n\n/* eslint-disable deprecation/deprecation */\n/**\n * Test if a node is an `UnparsedPrologue`.\n * @deprecated With TypeScript v5\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isUnparsedPrologue(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `UnparsedPrologue`.\n */\nexport function isUnparsedPrologue(node: ts.Node): node is ts.UnparsedPrologue {\n\treturn node.kind === ts.SyntaxKind.UnparsedPrologue;\n}\n/* eslint-enable deprecation/deprecation */\n\n/* eslint-disable deprecation/deprecation */\n/**\n * Test if a node is an `UnparsedSyntheticReference`.\n * @deprecated With TypeScript v5\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isUnparsedSyntheticReference(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `UnparsedSyntheticReference`.\n */\nexport function isUnparsedSyntheticReference(\n\tnode: ts.Node,\n): node is ts.UnparsedSyntheticReference {\n\treturn node.kind === ts.SyntaxKind.UnparsedSyntheticReference;\n}\n/* eslint-enable deprecation/deprecation */\n\n/**\n * Test if a node is a {@link VoidKeyword}.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isVoidKeyword(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a {@link VoidKeyword}.\n */\nexport function isVoidKeyword(node: ts.Node): node is VoidKeyword {\n\treturn node.kind === ts.SyntaxKind.VoidKeyword;\n}\n", "import ts from \"typescript\";\n\nimport { isTsVersionAtLeast } from \"../../utils\";\nimport {\n\tisJSDocNamespaceDeclaration,\n\tisJsxTagNamePropertyAccess,\n\tisNamespaceDeclaration,\n\tisPropertyAccessEntityNameExpression,\n\tisSuperElementAccessExpression,\n\tisSuperPropertyAccessExpression,\n} from \"./compound\";\nimport {\n\tisAccessorKeyword,\n\tisEndOfFileToken,\n\tisFalseLiteral,\n\tisJSDocText,\n\tisJsonMinusNumericLiteral,\n\tisNullLiteral,\n\tisPrivateKeyword,\n\tisProtectedKeyword,\n\tisPublicKeyword,\n\tisReadonlyKeyword,\n\tisStaticKeyword,\n\tisThisExpression,\n\tisTrueLiteral,\n} from \"./single\";\n\n/**\n * Test if a node is an `AccessExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAccessExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AccessExpression`.\n */\nexport function isAccessExpression(node: ts.Node): node is ts.AccessExpression {\n\treturn (\n\t\tts.isPropertyAccessExpression(node) || ts.isElementAccessExpression(node)\n\t);\n}\n\n/**\n * Test if a node is an `AccessibilityModifier`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAccessibilityModifier(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AccessibilityModifier`.\n */\nexport function isAccessibilityModifier(\n\tnode: ts.Node,\n): node is ts.AccessibilityModifier {\n\treturn (\n\t\tisPublicKeyword(node) || isPrivateKeyword(node) || isProtectedKeyword(node)\n\t);\n}\n\n/**\n * Test if a node is an `AccessorDeclaration`.\n * @deprecated With TypeScript v5, in favor of typescript's `isAccessor`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAccessorDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AccessorDeclaration`.\n */\nexport function isAccessorDeclaration(\n\tnode: ts.Node,\n): node is ts.AccessorDeclaration {\n\treturn ts.isGetAccessorDeclaration(node) || ts.isSetAccessorDeclaration(node);\n}\n\n/**\n * Test if a node is an `ArrayBindingElement`.\n * @deprecated With TypeScript v5, in favor of typescript's `isArrayBindingElement`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isArrayBindingElement(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ArrayBindingElement`.\n */\nexport function isArrayBindingElement(\n\tnode: ts.Node,\n): node is ts.ArrayBindingElement {\n\treturn ts.isBindingElement(node) || ts.isOmittedExpression(node);\n}\n\n/**\n * Test if a node is an `ArrayBindingOrAssignmentPattern`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isArrayBindingOrAssignmentPattern(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ArrayBindingOrAssignmentPattern`.\n */\nexport function isArrayBindingOrAssignmentPattern(\n\tnode: ts.Node,\n): node is ts.ArrayBindingOrAssignmentPattern {\n\treturn ts.isArrayBindingPattern(node) || ts.isArrayLiteralExpression(node);\n}\n\n/**\n * Test if a node is an `AssignmentPattern`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isAssignmentPattern(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `AssignmentPattern`.\n */\nexport function isAssignmentPattern(\n\tnode: ts.Node,\n): node is ts.AssignmentPattern {\n\treturn (\n\t\tts.isObjectLiteralExpression(node) || ts.isArrayLiteralExpression(node)\n\t);\n}\n\n/**\n * Test if a node is a `BindingOrAssignmentElementRestIndicator`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBindingOrAssignmentElementRestIndicator(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `BindingOrAssignmentElementRestIndicator`.\n */\nexport function isBindingOrAssignmentElementRestIndicator(\n\tnode: ts.Node,\n): node is ts.BindingOrAssignmentElementRestIndicator {\n\tif (ts.isSpreadElement(node) || ts.isSpreadAssignment(node)) {\n\t\treturn true;\n\t}\n\n\tif (isTsVersionAtLeast(4, 4)) {\n\t\treturn ts.isDotDotDotToken(node);\n\t}\n\n\treturn false;\n}\n\n/**\n * Test if a node is a `BindingOrAssignmentElementTarget`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBindingOrAssignmentElementTarget(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `BindingOrAssignmentElementTarget`.\n */\nexport function isBindingOrAssignmentElementTarget(\n\tnode: ts.Node,\n): node is ts.BindingOrAssignmentElementTarget {\n\treturn (\n\t\tisBindingOrAssignmentPattern(node) ||\n\t\tts.isIdentifier(node) ||\n\t\tts.isPropertyAccessExpression(node) ||\n\t\tts.isElementAccessExpression(node) ||\n\t\tts.isOmittedExpression(node)\n\t);\n}\n\n/**\n * Test if a node is a `BindingOrAssignmentPattern`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBindingOrAssignmentPattern(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `BindingOrAssignmentPattern`.\n */\nexport function isBindingOrAssignmentPattern(\n\tnode: ts.Node,\n): node is ts.BindingOrAssignmentPattern {\n\treturn (\n\t\tisObjectBindingOrAssignmentPattern(node) ||\n\t\tisArrayBindingOrAssignmentPattern(node)\n\t);\n}\n\n/**\n * Test if a node is a `BindingPattern`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBindingPattern(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `BindingPattern`.\n */\nexport function isBindingPattern(node: ts.Node): node is ts.BindingPattern {\n\treturn ts.isObjectBindingPattern(node) || ts.isArrayBindingPattern(node);\n}\n\n/**\n * Test if a node is a `BlockLike`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBlockLike(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `BlockLike`.\n */\nexport function isBlockLike(node: ts.Node): node is ts.BlockLike {\n\treturn (\n\t\tts.isSourceFile(node) ||\n\t\tts.isBlock(node) ||\n\t\tts.isModuleBlock(node) ||\n\t\tts.isCaseOrDefaultClause(node)\n\t);\n}\n\n/**\n * Test if a node is a `BooleanLiteral`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isBooleanLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `BooleanLiteral`.\n */\nexport function isBooleanLiteral(node: ts.Node): node is ts.BooleanLiteral {\n\treturn isTrueLiteral(node) || isFalseLiteral(node);\n}\n\n/**\n * Test if a node is a `ClassLikeDeclaration`.\n * @deprecated With TypeScript v5, in favor of typescript's `isClassLike`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isClassLikeDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ClassLikeDeclaration`.\n */\nexport function isClassLikeDeclaration(\n\tnode: ts.Node,\n): node is ts.ClassLikeDeclaration {\n\treturn ts.isClassDeclaration(node) || ts.isClassExpression(node);\n}\n\n/**\n * Test if a node is a `ClassMemberModifier`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isClassMemberModifier(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ClassMemberModifier`.\n */\nexport function isClassMemberModifier(\n\tnode: ts.Node,\n): node is ts.ClassMemberModifier {\n\treturn (\n\t\tisAccessibilityModifier(node) ||\n\t\tisReadonlyKeyword(node) ||\n\t\tisStaticKeyword(node) ||\n\t\tisAccessorKeyword(node)\n\t);\n}\n\n/**\n * Test if a node is a `DeclarationName`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDeclarationName(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DeclarationName`.\n */\nexport function isDeclarationName(node: ts.Node): node is ts.DeclarationName {\n\treturn (\n\t\tts.isIdentifier(node) ||\n\t\tts.isPrivateIdentifier(node) ||\n\t\tts.isStringLiteralLike(node) ||\n\t\tts.isNumericLiteral(node) ||\n\t\tts.isComputedPropertyName(node) ||\n\t\tts.isElementAccessExpression(node) ||\n\t\tisBindingPattern(node) ||\n\t\tisEntityNameExpression(node)\n\t);\n}\n\n/**\n * Test if a node is a `DeclarationWithTypeParameterChildren`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDeclarationWithTypeParameterChildren(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DeclarationWithTypeParameterChildren`.\n */\nexport function isDeclarationWithTypeParameterChildren(\n\tnode: ts.Node,\n): node is ts.DeclarationWithTypeParameterChildren {\n\treturn (\n\t\tisSignatureDeclaration(node) ||\n\t\t// eslint-disable-next-line deprecation/deprecation -- Keep compatibility with ts <5\n\t\tisClassLikeDeclaration(node) ||\n\t\tts.isInterfaceDeclaration(node) ||\n\t\tts.isTypeAliasDeclaration(node) ||\n\t\tts.isJSDocTemplateTag(node)\n\t);\n}\n\n/**\n * Test if a node is a `DeclarationWithTypeParameters`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDeclarationWithTypeParameters(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DeclarationWithTypeParameters`.\n */\nexport function isDeclarationWithTypeParameters(\n\tnode: ts.Node,\n): node is ts.DeclarationWithTypeParameters {\n\treturn (\n\t\tisDeclarationWithTypeParameterChildren(node) ||\n\t\tts.isJSDocTypedefTag(node) ||\n\t\tts.isJSDocCallbackTag(node) ||\n\t\tts.isJSDocSignature(node)\n\t);\n}\n\n/**\n * Test if a node is a `DestructuringPattern`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isDestructuringPattern(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `DestructuringPattern`.\n */\nexport function isDestructuringPattern(\n\tnode: ts.Node,\n): node is ts.DestructuringPattern {\n\treturn (\n\t\tisBindingPattern(node) ||\n\t\tts.isObjectLiteralExpression(node) ||\n\t\tts.isArrayLiteralExpression(node)\n\t);\n}\n\n/**\n * Test if a node is an `EntityNameExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isEntityNameExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `EntityNameExpression`.\n */\nexport function isEntityNameExpression(\n\tnode: ts.Node,\n): node is ts.EntityNameExpression {\n\treturn ts.isIdentifier(node) || isPropertyAccessEntityNameExpression(node);\n}\n\n/**\n * Test if a node is an `EntityNameOrEntityNameExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isEntityNameOrEntityNameExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `EntityNameOrEntityNameExpression`.\n */\nexport function isEntityNameOrEntityNameExpression(\n\tnode: ts.Node,\n): node is ts.EntityNameOrEntityNameExpression {\n\treturn ts.isEntityName(node) || isEntityNameExpression(node);\n}\n\n/**\n * Test if a node is a `ForInOrOfStatement`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isForInOrOfStatement(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ForInOrOfStatement`.\n */\nexport function isForInOrOfStatement(\n\tnode: ts.Node,\n): node is ts.ForInOrOfStatement {\n\treturn ts.isForInStatement(node) || ts.isForOfStatement(node);\n}\n\n/**\n * Test if a node is a `FunctionLikeDeclaration`.\n * @deprecated With TypeScript v5, in favor of typescript's `isFunctionLike`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isFunctionLikeDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `FunctionLikeDeclaration`.\n */\nexport function isFunctionLikeDeclaration(\n\tnode: ts.Node,\n): node is ts.FunctionLikeDeclaration {\n\treturn (\n\t\tts.isFunctionDeclaration(node) ||\n\t\tts.isMethodDeclaration(node) ||\n\t\tts.isGetAccessorDeclaration(node) ||\n\t\tts.isSetAccessorDeclaration(node) ||\n\t\tts.isConstructorDeclaration(node) ||\n\t\tts.isFunctionExpression(node) ||\n\t\tts.isArrowFunction(node)\n\t);\n}\n\n/**\n * Test if a node is a `HasDecorators`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasDecorators(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasDecorators`.\n */\nexport function hasDecorators(node: ts.Node): node is ts.HasDecorators {\n\treturn (\n\t\tts.isParameter(node) ||\n\t\tts.isPropertyDeclaration(node) ||\n\t\tts.isMethodDeclaration(node) ||\n\t\tts.isGetAccessorDeclaration(node) ||\n\t\tts.isSetAccessorDeclaration(node) ||\n\t\tts.isClassExpression(node) ||\n\t\tts.isClassDeclaration(node)\n\t);\n}\n\n/**\n * Test if a node is a `HasExpressionInitializer`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasExpressionInitializer(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasExpressionInitializer`.\n */\nexport function hasExpressionInitializer(\n\tnode: ts.Node,\n): node is ts.HasExpressionInitializer {\n\treturn (\n\t\tts.isVariableDeclaration(node) ||\n\t\tts.isParameter(node) ||\n\t\tts.isBindingElement(node) ||\n\t\tts.isPropertyDeclaration(node) ||\n\t\tts.isPropertyAssignment(node) ||\n\t\tts.isEnumMember(node)\n\t);\n}\n\n/**\n * Test if a node is a `HasInitializer`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasInitializer(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasInitializer`.\n */\nexport function hasInitializer(node: ts.Node): node is ts.HasInitializer {\n\treturn (\n\t\thasExpressionInitializer(node) ||\n\t\tts.isForStatement(node) ||\n\t\tts.isForInStatement(node) ||\n\t\tts.isForOfStatement(node) ||\n\t\tts.isJsxAttribute(node)\n\t);\n}\n\n/**\n * Test if a node is a `HasJSDoc`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasJSDoc(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasJSDoc`.\n */\nexport function hasJSDoc(node: ts.Node): node is ts.HasJSDoc {\n\tif (\n\t\t// eslint-disable-next-line deprecation/deprecation -- Keep compatibility with ts <5\n\t\tisAccessorDeclaration(node) ||\n\t\tts.isArrowFunction(node) ||\n\t\tts.isBlock(node) ||\n\t\tts.isBreakStatement(node) ||\n\t\tts.isCallSignatureDeclaration(node) ||\n\t\tts.isCaseClause(node) ||\n\t\t// eslint-disable-next-line deprecation/deprecation -- Keep compatibility with ts <5\n\t\tisClassLikeDeclaration(node) ||\n\t\tts.isConstructorDeclaration(node) ||\n\t\tts.isConstructorTypeNode(node) ||\n\t\tts.isConstructSignatureDeclaration(node) ||\n\t\tts.isContinueStatement(node) ||\n\t\tts.isDebuggerStatement(node) ||\n\t\tts.isDoStatement(node) ||\n\t\tts.isEmptyStatement(node) ||\n\t\tisEndOfFileToken(node) ||\n\t\tts.isEnumDeclaration(node) ||\n\t\tts.isEnumMember(node) ||\n\t\tts.isExportAssignment(node) ||\n\t\tts.isExportDeclaration(node) ||\n\t\tts.isExportSpecifier(node) ||\n\t\tts.isExpressionStatement(node) ||\n\t\tts.isForInStatement(node) ||\n\t\tts.isForOfStatement(node) ||\n\t\tts.isForStatement(node) ||\n\t\tts.isFunctionDeclaration(node) ||\n\t\tts.isFunctionExpression(node) ||\n\t\tts.isFunctionTypeNode(node) ||\n\t\tts.isIfStatement(node) ||\n\t\tts.isImportDeclaration(node) ||\n\t\tts.isImportEqualsDeclaration(node) ||\n\t\tts.isIndexSignatureDeclaration(node) ||\n\t\tts.isInterfaceDeclaration(node) ||\n\t\tts.isJSDocFunctionType(node) ||\n\t\tts.isLabeledStatement(node) ||\n\t\tts.isMethodDeclaration(node) ||\n\t\tts.isMethodSignature(node) ||\n\t\tts.isModuleDeclaration(node) ||\n\t\tts.isNamedTupleMember(node) ||\n\t\tts.isNamespaceExportDeclaration(node) ||\n\t\tts.isParameter(node) ||\n\t\tts.isParenthesizedExpression(node) ||\n\t\tts.isPropertyAssignment(node) ||\n\t\tts.isPropertyDeclaration(node) ||\n\t\tts.isPropertySignature(node) ||\n\t\tts.isReturnStatement(node) ||\n\t\tts.isShorthandPropertyAssignment(node) ||\n\t\tts.isSpreadAssignment(node) ||\n\t\tts.isSwitchStatement(node) ||\n\t\tts.isThrowStatement(node) ||\n\t\tts.isTryStatement(node) ||\n\t\tts.isTypeAliasDeclaration(node) ||\n\t\tts.isVariableDeclaration(node) ||\n\t\tts.isVariableStatement(node) ||\n\t\tts.isWhileStatement(node) ||\n\t\tts.isWithStatement(node)\n\t) {\n\t\treturn true;\n\t}\n\n\tif (isTsVersionAtLeast(4, 4) && ts.isClassStaticBlockDeclaration(node)) {\n\t\treturn true;\n\t}\n\n\tif (\n\t\tisTsVersionAtLeast(5, 0) &&\n\t\t(ts.isBinaryExpression(node) ||\n\t\t\tts.isElementAccessExpression(node) ||\n\t\t\tts.isIdentifier(node) ||\n\t\t\tts.isJSDocSignature(node) ||\n\t\t\tts.isObjectLiteralExpression(node) ||\n\t\t\tts.isPropertyAccessExpression(node) ||\n\t\t\tts.isTypeParameterDeclaration(node))\n\t) {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\n/**\n * Test if a node is a `HasModifiers`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasModifiers(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasModifiers`.\n */\nexport function hasModifiers(node: ts.Node): node is ts.HasModifiers {\n\treturn (\n\t\tts.isTypeParameterDeclaration(node) ||\n\t\tts.isParameter(node) ||\n\t\tts.isConstructorTypeNode(node) ||\n\t\tts.isPropertySignature(node) ||\n\t\tts.isPropertyDeclaration(node) ||\n\t\tts.isMethodSignature(node) ||\n\t\tts.isMethodDeclaration(node) ||\n\t\tts.isConstructorDeclaration(node) ||\n\t\tts.isGetAccessorDeclaration(node) ||\n\t\tts.isSetAccessorDeclaration(node) ||\n\t\tts.isIndexSignatureDeclaration(node) ||\n\t\tts.isFunctionExpression(node) ||\n\t\tts.isArrowFunction(node) ||\n\t\tts.isClassExpression(node) ||\n\t\tts.isVariableStatement(node) ||\n\t\tts.isFunctionDeclaration(node) ||\n\t\tts.isClassDeclaration(node) ||\n\t\tts.isInterfaceDeclaration(node) ||\n\t\tts.isTypeAliasDeclaration(node) ||\n\t\tts.isEnumDeclaration(node) ||\n\t\tts.isModuleDeclaration(node) ||\n\t\tts.isImportEqualsDeclaration(node) ||\n\t\tts.isImportDeclaration(node) ||\n\t\tts.isExportAssignment(node) ||\n\t\tts.isExportDeclaration(node)\n\t);\n}\n\n/**\n * Test if a node is a `HasType`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasType(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasType`.\n */\nexport function hasType(node: ts.Node): node is ts.HasType {\n\treturn (\n\t\tisSignatureDeclaration(node) ||\n\t\tts.isVariableDeclaration(node) ||\n\t\tts.isParameter(node) ||\n\t\tts.isPropertySignature(node) ||\n\t\tts.isPropertyDeclaration(node) ||\n\t\tts.isTypePredicateNode(node) ||\n\t\tts.isParenthesizedTypeNode(node) ||\n\t\tts.isTypeOperatorNode(node) ||\n\t\tts.isMappedTypeNode(node) ||\n\t\tts.isAssertionExpression(node) ||\n\t\tts.isTypeAliasDeclaration(node) ||\n\t\tts.isJSDocTypeExpression(node) ||\n\t\tts.isJSDocNonNullableType(node) ||\n\t\tts.isJSDocNullableType(node) ||\n\t\tts.isJSDocOptionalType(node) ||\n\t\tts.isJSDocVariadicType(node)\n\t);\n}\n\n/**\n * Test if a node is a `HasTypeArguments`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (hasTypeArguments(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `HasTypeArguments`.\n */\nexport function hasTypeArguments(node: ts.Node): node is ts.HasTypeArguments {\n\treturn (\n\t\tts.isCallExpression(node) ||\n\t\tts.isNewExpression(node) ||\n\t\tts.isTaggedTemplateExpression(node) ||\n\t\tts.isJsxOpeningElement(node) ||\n\t\tts.isJsxSelfClosingElement(node)\n\t);\n}\n\n/**\n * Test if a node is a `JSDocComment`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJSDocComment(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JSDocComment`.\n */\nexport function isJSDocComment(node: ts.Node): node is ts.JSDocComment {\n\tif (isJSDocText(node)) {\n\t\treturn true;\n\t}\n\n\tif (isTsVersionAtLeast(4, 4)) {\n\t\treturn (\n\t\t\tts.isJSDocLink(node) ||\n\t\t\tts.isJSDocLinkCode(node) ||\n\t\t\tts.isJSDocLinkPlain(node)\n\t\t);\n\t}\n\n\treturn false;\n}\n\n/**\n * Test if a node is a `JSDocNamespaceBody`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJSDocNamespaceBody(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JSDocNamespaceBody`.\n */\nexport function isJSDocNamespaceBody(\n\tnode: ts.Node,\n): node is ts.JSDocNamespaceBody {\n\treturn ts.isIdentifier(node) || isJSDocNamespaceDeclaration(node);\n}\n\n/**\n * Test if a node is a `JSDocTypeReferencingNode`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJSDocTypeReferencingNode(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JSDocTypeReferencingNode`.\n */\nexport function isJSDocTypeReferencingNode(\n\tnode: ts.Node,\n): node is ts.JSDocTypeReferencingNode {\n\treturn (\n\t\tts.isJSDocVariadicType(node) ||\n\t\tts.isJSDocOptionalType(node) ||\n\t\tts.isJSDocNullableType(node) ||\n\t\tts.isJSDocNonNullableType(node)\n\t);\n}\n\n/**\n * Test if a node is a `JsonObjectExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsonObjectExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsonObjectExpression`.\n */\nexport function isJsonObjectExpression(\n\tnode: ts.Node,\n): node is ts.JsonObjectExpression {\n\treturn (\n\t\tts.isObjectLiteralExpression(node) ||\n\t\tts.isArrayLiteralExpression(node) ||\n\t\tisJsonMinusNumericLiteral(node) ||\n\t\tts.isNumericLiteral(node) ||\n\t\tts.isStringLiteral(node) ||\n\t\tisBooleanLiteral(node) ||\n\t\tisNullLiteral(node)\n\t);\n}\n\n/**\n * Test if a node is a `JsxAttributeLike`.\n * @deprecated With TypeScript v5, in favor of typescript's `isJsxAttributeLike`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsxAttributeLike(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsxAttributeLike`.\n */\nexport function isJsxAttributeLike(node: ts.Node): node is ts.JsxAttributeLike {\n\treturn ts.isJsxAttribute(node) || ts.isJsxSpreadAttribute(node);\n}\n\n/**\n * Test if a node is a `JsxAttributeValue`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsxAttributeValue(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsxAttributeValue`.\n */\nexport function isJsxAttributeValue(\n\tnode: ts.Node,\n): node is ts.JsxAttributeValue {\n\treturn (\n\t\tts.isStringLiteral(node) ||\n\t\tts.isJsxExpression(node) ||\n\t\tts.isJsxElement(node) ||\n\t\tts.isJsxSelfClosingElement(node) ||\n\t\tts.isJsxFragment(node)\n\t);\n}\n\n/**\n * Test if a node is a `JsxChild`.\n * @deprecated With TypeScript v5, in favor of typescript's `isJsxChild`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsxChild(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsxChild`.\n */\nexport function isJsxChild(node: ts.Node): node is ts.JsxChild {\n\treturn (\n\t\tts.isJsxText(node) ||\n\t\tts.isJsxExpression(node) ||\n\t\tts.isJsxElement(node) ||\n\t\tts.isJsxSelfClosingElement(node) ||\n\t\tts.isJsxFragment(node)\n\t);\n}\n\n/**\n * Test if a node is a `JsxTagNameExpression`.\n * @deprecated With TypeScript v5, in favor of typescript's `isJsxTagNameExpression`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isJsxTagNameExpression(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `JsxTagNameExpression`.\n */\nexport function isJsxTagNameExpression(\n\tnode: ts.Node,\n): node is ts.JsxTagNameExpression {\n\treturn (\n\t\tts.isIdentifier(node) ||\n\t\tisThisExpression(node) ||\n\t\tisJsxTagNamePropertyAccess(node)\n\t);\n}\n\n/**\n * Test if a node is a `LiteralToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isLiteralToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `LiteralToken`.\n */\nexport function isLiteralToken(node: ts.Node): node is ts.LiteralToken {\n\treturn (\n\t\tts.isNumericLiteral(node) ||\n\t\tts.isBigIntLiteral(node) ||\n\t\tts.isStringLiteral(node) ||\n\t\tts.isJsxText(node) ||\n\t\tts.isRegularExpressionLiteral(node) ||\n\t\tts.isNoSubstitutionTemplateLiteral(node)\n\t);\n}\n\n/**\n * Test if a node is a `ModuleBody`.\n * @deprecated With TypeScript v5, in favor of typescript's `isModuleBody`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isModuleBody(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ModuleBody`.\n */\nexport function isModuleBody(node: ts.Node): node is ts.ModuleBody {\n\treturn isNamespaceBody(node) || isJSDocNamespaceBody(node);\n}\n\n/**\n * Test if a node is a `ModuleName`.\n * @deprecated With TypeScript v5, in favor of typescript's `isModuleName`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isModuleName(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ModuleName`.\n */\nexport function isModuleName(node: ts.Node): node is ts.ModuleName {\n\treturn ts.isIdentifier(node) || ts.isStringLiteral(node);\n}\n\n/**\n * Test if a node is a `ModuleReference`.\n * @deprecated With TypeScript v5, in favor of typescript's `isModuleReference`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isModuleReference(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ModuleReference`.\n */\nexport function isModuleReference(node: ts.Node): node is ts.ModuleReference {\n\treturn ts.isEntityName(node) || ts.isExternalModuleReference(node);\n}\n\n/**\n * Test if a node is a `NamedImportBindings`.\n * @deprecated With TypeScript v5, in favor of typescript's `isNamedImportBindings`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNamedImportBindings(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `NamedImportBindings`.\n */\nexport function isNamedImportBindings(\n\tnode: ts.Node,\n): node is ts.NamedImportBindings {\n\treturn ts.isNamespaceImport(node) || ts.isNamedImports(node);\n}\n\n/**\n * Test if a node is a `NamedImportsOrExports`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNamedImportsOrExports(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `NamedImportsOrExports`.\n */\nexport function isNamedImportsOrExports(\n\tnode: ts.Node,\n): node is ts.NamedImportsOrExports {\n\treturn ts.isNamedImports(node) || ts.isNamedExports(node);\n}\n\n/**\n * Test if a node is a `NamespaceBody`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isNamespaceBody(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `NamespaceBody`.\n */\nexport function isNamespaceBody(node: ts.Node): node is ts.NamespaceBody {\n\treturn ts.isModuleBlock(node) || isNamespaceDeclaration(node);\n}\n\n/**\n * Test if a node is an `ObjectBindingOrAssignmentElement`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isObjectBindingOrAssignmentElement(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ObjectBindingOrAssignmentElement`.\n */\nexport function isObjectBindingOrAssignmentElement(\n\tnode: ts.Node,\n): node is ts.ObjectBindingOrAssignmentElement {\n\treturn (\n\t\tts.isBindingElement(node) ||\n\t\tts.isPropertyAssignment(node) ||\n\t\tts.isShorthandPropertyAssignment(node) ||\n\t\tts.isSpreadAssignment(node)\n\t);\n}\n\n/**\n * Test if a node is an `ObjectBindingOrAssignmentPattern`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isObjectBindingOrAssignmentPattern(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ObjectBindingOrAssignmentPattern`.\n */\nexport function isObjectBindingOrAssignmentPattern(\n\tnode: ts.Node,\n): node is ts.ObjectBindingOrAssignmentPattern {\n\treturn ts.isObjectBindingPattern(node) || ts.isObjectLiteralExpression(node);\n}\n\n/**\n * Test if a node is an `ObjectTypeDeclaration`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isObjectTypeDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `ObjectTypeDeclaration`.\n */\nexport function isObjectTypeDeclaration(\n\tnode: ts.Node,\n): node is ts.ObjectTypeDeclaration {\n\treturn (\n\t\t// eslint-disable-next-line deprecation/deprecation -- Keep compatibility with ts <5\n\t\tisClassLikeDeclaration(node) ||\n\t\tts.isInterfaceDeclaration(node) ||\n\t\tts.isTypeLiteralNode(node)\n\t);\n}\n\n/**\n * Test if a node is a `ParameterPropertyModifier`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isParameterPropertyModifier(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `ParameterPropertyModifier`.\n */\nexport function isParameterPropertyModifier(\n\tnode: ts.Node,\n): node is ts.ParameterPropertyModifier {\n\treturn isAccessibilityModifier(node) || isReadonlyKeyword(node);\n}\n\n/**\n * Test if a node is a `PropertyNameLiteral`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isPropertyNameLiteral(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `PropertyNameLiteral`.\n */\nexport function isPropertyNameLiteral(\n\tnode: ts.Node,\n): node is ts.PropertyNameLiteral {\n\treturn (\n\t\tts.isIdentifier(node) ||\n\t\tts.isStringLiteralLike(node) ||\n\t\tts.isNumericLiteral(node)\n\t);\n}\n\n/**\n * Test if a node is a `PseudoLiteralToken`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isPseudoLiteralToken(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `PseudoLiteralToken`.\n */\nexport function isPseudoLiteralToken(\n\tnode: ts.Node,\n): node is ts.PseudoLiteralToken {\n\treturn (\n\t\tts.isTemplateHead(node) ||\n\t\tts.isTemplateMiddle(node) ||\n\t\tts.isTemplateTail(node)\n\t);\n}\n\n/**\n * Test if a node is a `SignatureDeclaration`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSignatureDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `SignatureDeclaration`.\n */\nexport function isSignatureDeclaration(\n\tnode: ts.Node,\n): node is ts.SignatureDeclaration {\n\treturn (\n\t\tts.isCallSignatureDeclaration(node) ||\n\t\tts.isConstructSignatureDeclaration(node) ||\n\t\tts.isMethodSignature(node) ||\n\t\tts.isIndexSignatureDeclaration(node) ||\n\t\tts.isFunctionTypeNode(node) ||\n\t\tts.isConstructorTypeNode(node) ||\n\t\tts.isJSDocFunctionType(node) ||\n\t\tts.isFunctionDeclaration(node) ||\n\t\tts.isMethodDeclaration(node) ||\n\t\tts.isConstructorDeclaration(node) ||\n\t\t// eslint-disable-next-line deprecation/deprecation -- Keep compatibility with ts <5\n\t\tisAccessorDeclaration(node) ||\n\t\tts.isFunctionExpression(node) ||\n\t\tts.isArrowFunction(node)\n\t);\n}\n\n/**\n * Test if a node is a `SuperProperty`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isSuperProperty(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `SuperProperty`.\n */\nexport function isSuperProperty(node: ts.Node): node is ts.SuperProperty {\n\treturn (\n\t\tisSuperPropertyAccessExpression(node) ||\n\t\tisSuperElementAccessExpression(node)\n\t);\n}\n\n/**\n * Test if a node is a `TypeOnlyCompatibleAliasDeclaration`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isTypeOnlyCompatibleAliasDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `TypeOnlyCompatibleAliasDeclaration`.\n */\nexport function isTypeOnlyCompatibleAliasDeclaration(\n\tnode: ts.Node,\n): node is ts.TypeOnlyCompatibleAliasDeclaration {\n\tif (\n\t\tts.isImportClause(node) ||\n\t\tts.isImportEqualsDeclaration(node) ||\n\t\tts.isNamespaceImport(node) ||\n\t\tts.isImportOrExportSpecifier(node)\n\t) {\n\t\treturn true;\n\t}\n\n\tif (\n\t\tisTsVersionAtLeast(5, 0) &&\n\t\t(ts.isExportDeclaration(node) || ts.isNamespaceExport(node))\n\t) {\n\t\treturn true;\n\t}\n\n\treturn false;\n}\n\n/**\n * Test if a node is a `TypeReferenceType`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isTypeReferenceType(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `TypeReferenceType`.\n */\nexport function isTypeReferenceType(\n\tnode: ts.Node,\n): node is ts.TypeReferenceType {\n\treturn ts.isTypeReferenceNode(node) || ts.isExpressionWithTypeArguments(node);\n}\n\n/**\n * Test if a node is an `UnionOrIntersectionTypeNode`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isUnionOrIntersectionTypeNode(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `UnionOrIntersectionTypeNode`.\n */\nexport function isUnionOrIntersectionTypeNode(\n\tnode: ts.Node,\n): node is ts.UnionOrIntersectionTypeNode {\n\treturn ts.isUnionTypeNode(node) || ts.isIntersectionTypeNode(node);\n}\n\n/* eslint-disable deprecation/deprecation */\n/**\n * Test if a node is an `UnparsedSourceText`.\n * @deprecated With TypeScript v5\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isUnparsedSourceText(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be an `UnparsedSourceText`.\n */\nexport function isUnparsedSourceText(\n\tnode: ts.Node,\n): node is ts.UnparsedSourceText {\n\treturn ts.isUnparsedPrepend(node) || ts.isUnparsedTextLike(node);\n}\n/* eslint-enable deprecation/deprecation */\n\n/**\n * Test if a node is a `VariableLikeDeclaration`.\n * @category Nodes - Type Guards\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isVariableLikeDeclaration(node)) {\n *   // ...\n * }\n * ```\n * @returns Whether the given node appears to be a `VariableLikeDeclaration`.\n */\nexport function isVariableLikeDeclaration(\n\tnode: ts.Node,\n): node is ts.VariableLikeDeclaration {\n\treturn (\n\t\tts.isVariableDeclaration(node) ||\n\t\tts.isParameter(node) ||\n\t\tts.isBindingElement(node) ||\n\t\tts.isPropertyDeclaration(node) ||\n\t\tts.isPropertyAssignment(node) ||\n\t\tts.isPropertySignature(node) ||\n\t\tts.isJsxAttribute(node) ||\n\t\tts.isShorthandPropertyAssignment(node) ||\n\t\tts.isEnumMember(node) ||\n\t\tts.isJSDocPropertyTag(node) ||\n\t\tts.isJSDocParameterTag(node)\n\t);\n}\n", "import ts from \"typescript\";\n\nconst [tsMajor, tsMinor] = ts.versionMajorMinor\n\t.split(\".\")\n\t.map((raw) => Number.parseInt(raw, 10));\n\nexport function isTsVersionAtLeast(major: number, minor = 0): boolean {\n\treturn tsMajor > major || (tsMajor === major && tsMinor >= minor);\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\n/**\n * Is the node a scope boundary, specifically due to it being a function.\n * @category Scope Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n *\n * if (isFunctionScopeBoundary(node, ts.ObjectFlags.Anonymous)) {\n *   // ...\n * }\n * ```\n */\nexport function isFunctionScopeBoundary(node: ts.Node): boolean {\n\tswitch (node.kind) {\n\t\tcase ts.SyntaxKind.FunctionExpression:\n\t\tcase ts.SyntaxKind.ArrowFunction:\n\t\tcase ts.SyntaxKind.Constructor:\n\t\tcase ts.SyntaxKind.ModuleDeclaration:\n\t\tcase ts.SyntaxKind.ClassDeclaration:\n\t\tcase ts.SyntaxKind.ClassExpression:\n\t\tcase ts.SyntaxKind.EnumDeclaration:\n\t\tcase ts.SyntaxKind.MethodDeclaration:\n\t\tcase ts.SyntaxKind.FunctionDeclaration:\n\t\tcase ts.SyntaxKind.GetAccessor:\n\t\tcase ts.SyntaxKind.SetAccessor:\n\t\tcase ts.SyntaxKind.MethodSignature:\n\t\tcase ts.SyntaxKind.CallSignature:\n\t\tcase ts.SyntaxKind.ConstructSignature:\n\t\tcase ts.SyntaxKind.ConstructorType:\n\t\tcase ts.SyntaxKind.FunctionType:\n\t\t\treturn true;\n\t\tcase ts.SyntaxKind.SourceFile:\n\t\t\t// if SourceFile is no module, it contributes to the global scope and is therefore no scope boundary\n\t\t\treturn ts.isExternalModule(node as ts.SourceFile);\n\t\tdefault:\n\t\t\treturn false;\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { isNamedDeclarationWithName } from \"../nodes/typeGuards\";\nimport {\n\tisIntersectionType,\n\tisUnionType,\n\tisUniqueESSymbolType,\n} from \"./typeGuards\";\n\n/**\n * Get the `CallSignatures` of the given type.\n * @category Types - Getters\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * getCallSignaturesOfType(type);\n * ```\n */\nexport function getCallSignaturesOfType(\n\ttype: ts.Type,\n): readonly ts.Signature[] {\n\tif (isUnionType(type)) {\n\t\tconst signatures = [];\n\t\tfor (const subType of type.types) {\n\t\t\tsignatures.push(...getCallSignaturesOfType(subType));\n\t\t}\n\n\t\treturn signatures;\n\t}\n\n\tif (isIntersectionType(type)) {\n\t\tlet signatures: readonly ts.Signature[] | undefined;\n\t\tfor (const subType of type.types) {\n\t\t\tconst sig = getCallSignaturesOfType(subType);\n\t\t\tif (sig.length !== 0) {\n\t\t\t\t// if more than one type of the intersection has call signatures, none of them is useful for inference\n\t\t\t\tif (signatures !== undefined) {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\n\t\t\t\tsignatures = sig;\n\t\t\t}\n\t\t}\n\n\t\treturn signatures === undefined ? [] : signatures;\n\t}\n\n\treturn type.getCallSignatures();\n}\n\n/**\n * Get the property with the given name on the given type (if it exists).\n * @category Types - Getters\n * @example\n * ```ts\n * declare const property: ts.Symbol;\n * declare const type: ts.Type;\n *\n * getPropertyOfType(type, property.getEscapedName());\n * ```\n */\nexport function getPropertyOfType(\n\ttype: ts.Type,\n\tname: ts.__String,\n): ts.Symbol | undefined {\n\tif (!(name as string).startsWith(\"__\")) {\n\t\treturn type.getProperty(name as string);\n\t}\n\n\treturn type.getProperties().find((s) => s.escapedName === name);\n}\n\n/**\n * Retrieves a type symbol corresponding to a well-known string name.\n * @category Types - Getters\n * @example\n * ```ts\n * declare const type: ts.Type;\n * declare const typeChecker: ts.TypeChecker;\n *\n * getWellKnownSymbolPropertyOfType(type, \"asyncIterator\", typeChecker);\n * ```\n */\nexport function getWellKnownSymbolPropertyOfType(\n\ttype: ts.Type,\n\twellKnownSymbolName: string,\n\ttypeChecker: ts.TypeChecker,\n): ts.Symbol | undefined {\n\tconst prefix = \"__@\" + wellKnownSymbolName;\n\n\tfor (const prop of type.getProperties()) {\n\t\tif (!prop.name.startsWith(prefix)) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst declaration = prop.valueDeclaration ?? prop.getDeclarations()![0];\n\t\tif (\n\t\t\t!isNamedDeclarationWithName(declaration) ||\n\t\t\tdeclaration.name === undefined ||\n\t\t\t!ts.isComputedPropertyName(declaration.name)\n\t\t) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst globalSymbol = typeChecker.getApparentType(\n\t\t\ttypeChecker.getTypeAtLocation(declaration.name.expression),\n\t\t).symbol;\n\n\t\tif (\n\t\t\tprop.escapedName ===\n\t\t\tgetPropertyNameOfWellKnownSymbol(\n\t\t\t\ttypeChecker,\n\t\t\t\tglobalSymbol,\n\t\t\t\twellKnownSymbolName,\n\t\t\t)\n\t\t) {\n\t\t\treturn prop;\n\t\t}\n\t}\n\n\treturn undefined;\n}\n\n/**\n * @internal\n */\nfunction getPropertyNameOfWellKnownSymbol(\n\ttypeChecker: ts.TypeChecker,\n\tsymbolConstructor: ts.Symbol | undefined,\n\tsymbolName: string,\n) {\n\tconst knownSymbol =\n\t\tsymbolConstructor &&\n\t\ttypeChecker\n\t\t\t.getTypeOfSymbolAtLocation(\n\t\t\t\tsymbolConstructor,\n\t\t\t\t// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access\n\t\t\t\t(symbolConstructor as any).valueDeclaration,\n\t\t\t)\n\t\t\t.getProperty(symbolName);\n\tconst knownSymbolType =\n\t\tknownSymbol &&\n\t\ttypeChecker.getTypeOfSymbolAtLocation(\n\t\t\tknownSymbol,\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access\n\t\t\t(knownSymbol as any).valueDeclaration,\n\t\t);\n\tif (knownSymbolType && isUniqueESSymbolType(knownSymbolType)) {\n\t\treturn knownSymbolType.escapedName;\n\t}\n\n\treturn (\"__@\" + symbolName) as ts.__String;\n}\n", "import ts from \"typescript\";\n\nimport { isTypeFlagSet } from \"../../flags\";\n\n/**\n * A \"any\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicAnyType extends IntrinsicType {\n\tintrinsicName: \"any\";\n}\n\n/**\n * Determines whether the given type is the \"any\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicAnyType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicAnyType(type: ts.Type): type is IntrinsicAnyType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Any);\n}\n\n/**\n * A \"boolean\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicBooleanType extends IntrinsicType {\n\tintrinsicName: \"boolean\";\n}\n\n/**\n * Determines whether the given type is the \"boolean\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicBooleanType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicBooleanType(\n\ttype: ts.Type,\n): type is IntrinsicBooleanType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Boolean);\n}\n\n/**\n * A \"bigint\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicBigIntType extends IntrinsicType {\n\tintrinsicName: \"bigint\";\n}\n\n/**\n * Determines whether the given type is the \"bigint\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicBigIntType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicBigIntType(\n\ttype: ts.Type,\n): type is IntrinsicBigIntType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.BigInt);\n}\n\n/**\n * An \"error\" intrinsic type.\n *\n * This refers to a type generated when TypeScript encounters an error while\n * trying to resolve the type.\n * @category Type Types\n */\nexport interface IntrinsicErrorType extends IntrinsicType {\n\tintrinsicName: \"error\";\n}\n\n/**\n * Determines whether the given type is the \"error\" intrinsic type.\n *\n * The intrinsic error type occurs when TypeScript encounters an error while\n * trying to resolve the type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicErrorType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicErrorType(\n\ttype: ts.Type,\n): type is IntrinsicErrorType {\n\treturn isIntrinsicType(type) && type.intrinsicName === \"error\";\n}\n\n/**\n * A \"symbol\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicESSymbolType extends IntrinsicType {\n\tintrinsicName: \"symbol\";\n}\n\n/**\n * Determines whether the given type is the \"symbol\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicESSymbolType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicESSymbolType(\n\ttype: ts.Type,\n): type is IntrinsicESSymbolType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.ESSymbol);\n}\n\n/**\n * An intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicType extends ts.Type {\n\tintrinsicName: string;\n\tobjectFlags: ts.ObjectFlags;\n}\n\n// ts.TypeFlags.Intrinsic\nconst IntrinsicTypeFlags =\n\t(ts.TypeFlags as { Intrinsic?: number }).Intrinsic ??\n\tts.TypeFlags.Any |\n\t\tts.TypeFlags.Unknown |\n\t\tts.TypeFlags.String |\n\t\tts.TypeFlags.Number |\n\t\tts.TypeFlags.BigInt |\n\t\tts.TypeFlags.Boolean |\n\t\tts.TypeFlags.BooleanLiteral |\n\t\tts.TypeFlags.ESSymbol |\n\t\tts.TypeFlags.Void |\n\t\tts.TypeFlags.Undefined |\n\t\tts.TypeFlags.Null |\n\t\tts.TypeFlags.Never |\n\t\tts.TypeFlags.NonPrimitive;\n\n/**\n * Test if a type is an {@link IntrinsicType}.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicType(type: ts.Type): type is IntrinsicType {\n\treturn isTypeFlagSet(type, IntrinsicTypeFlags);\n}\n\n/**\n * A \"never\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicNeverType extends IntrinsicType {\n\tintrinsicName: \"never\";\n}\n\n/**\n * Determines whether the given type is the \"never\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicNeverType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicNeverType(\n\ttype: ts.Type,\n): type is IntrinsicNeverType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Never);\n}\n\n/**\n * A non-primitive intrinsic type.\n * E.g. An \"object\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicNonPrimitiveType extends IntrinsicType {\n\tintrinsicName: \"\";\n}\n\n/**\n * Determines whether the given type is a non-primitive intrinsic type.\n * E.g. An \"object\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicNonPrimitiveType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicNonPrimitiveType(\n\ttype: ts.Type,\n): type is IntrinsicNonPrimitiveType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.NonPrimitive);\n}\n\n/**\n * A \"null\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicNullType extends IntrinsicType {\n\tintrinsicName: \"null\";\n}\n\n/**\n * Determines whether the given type is the \"null\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicNullType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicNullType(type: ts.Type): type is IntrinsicNullType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Null);\n}\n\n/**\n * A \"number\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicNumberType extends IntrinsicType {\n\tintrinsicName: \"number\";\n}\n\n/**\n * Determines whether the given type is the \"number\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicNumberType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicNumberType(\n\ttype: ts.Type,\n): type is IntrinsicNumberType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Number);\n}\n\n/**\n * A \"string\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicStringType extends IntrinsicType {\n\tintrinsicName: \"string\";\n}\n\n/**\n * Determines whether the given type is the \"string\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicStringType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicStringType(\n\ttype: ts.Type,\n): type is IntrinsicStringType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.String);\n}\n\n/**\n * An \"undefined\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicUndefinedType extends IntrinsicType {\n\tintrinsicName: \"undefined\";\n}\n\n/**\n * Determines whether the given type is the \"undefined\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicUndefinedType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicUndefinedType(\n\ttype: ts.Type,\n): type is IntrinsicUndefinedType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Undefined);\n}\n\n/**\n * An \"unknown\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicUnknownType extends IntrinsicType {\n\tintrinsicName: \"unknown\";\n}\n\n/**\n * Determines whether the given type is the \"unknown\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicUnknownType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicUnknownType(\n\ttype: ts.Type,\n): type is IntrinsicUnknownType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Unknown);\n}\n\n/**\n * A \"void\" intrinsic type.\n * @category Type Types\n */\nexport interface IntrinsicVoidType extends IntrinsicType {\n\tintrinsicName: \"void\";\n}\n\n/**\n * Determines whether the given type is the \"void\" intrinsic type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntrinsicVoidType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntrinsicVoidType(type: ts.Type): type is IntrinsicVoidType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Void);\n}\n", "import ts from \"typescript\";\n\nimport { isObjectFlagSet } from \"../../flags\";\nimport { isObjectType } from \"./single\";\n\n/**\n * Test if a type is a `EvolvingArrayType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isEvolvingArrayType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isEvolvingArrayType(\n\ttype: ts.Type,\n): type is ts.EvolvingArrayType {\n\treturn (\n\t\tisObjectType(type) && isObjectFlagSet(type, ts.ObjectFlags.EvolvingArray)\n\t);\n}\n\n/**\n * Test if a type is a `TupleType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTupleType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isTupleType(type: ts.Type): type is ts.TupleType {\n\treturn isObjectType(type) && isObjectFlagSet(type, ts.ObjectFlags.Tuple);\n}\n\n/**\n * Test if a type is a `TypeReference`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTypeReference(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isTypeReference(type: ts.Type): type is ts.TypeReference {\n\treturn isObjectType(type) && isObjectFlagSet(type, ts.ObjectFlags.Reference);\n}\n", "import ts from \"typescript\";\n\nimport { isTypeFlagSet } from \"../../flags\";\n\n/**\n * Test if a type is a `ConditionalType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isConditionalType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isConditionalType(type: ts.Type): type is ts.ConditionalType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Conditional);\n}\n\n/**\n * Test if a type is a `EnumType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isEnumType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isEnumType(type: ts.Type): type is ts.EnumType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Enum);\n}\n\n/**\n * Test if a type is a `FreshableType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isFreshableType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isFreshableType(type: ts.Type): type is ts.FreshableType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Freshable);\n}\n\n/**\n * Test if a type is a `IndexType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIndexType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIndexType(type: ts.Type): type is ts.IndexType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Index);\n}\n\n/**\n * Test if a type is a `IndexedAccessType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIndexedAccessType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIndexedAccessType(\n\ttype: ts.Type,\n): type is ts.IndexedAccessType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.IndexedAccess);\n}\n\n/**\n * Test if a type is a `InstantiableType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isInstantiableType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isInstantiableType(type: ts.Type): type is ts.InstantiableType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Instantiable);\n}\n\n/**\n * Test if a type is a `IntersectionType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isIntersectionType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isIntersectionType(type: ts.Type): type is ts.IntersectionType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Intersection);\n}\n\n/**\n * Test if a type is a `ObjectType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isObjectType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isObjectType(type: ts.Type): type is ts.ObjectType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Object);\n}\n\n/**\n * Test if a type is a `StringMappingType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isStringMappingType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isStringMappingType(\n\ttype: ts.Type,\n): type is ts.StringMappingType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.StringMapping);\n}\n\n/**\n * Test if a type is a `SubstitutionType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isSubstitutionType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isSubstitutionType(type: ts.Type): type is ts.SubstitutionType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Substitution);\n}\n\n/**\n * Test if a type is a `TypeParameter`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTypeParameter(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isTypeParameter(type: ts.Type): type is ts.TypeParameter {\n\treturn isTypeFlagSet(type, ts.TypeFlags.TypeParameter);\n}\n\n/**\n * Test if a type is a `TypeVariable`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTypeVariable(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isTypeVariable(type: ts.Type): type is ts.TypeVariable {\n\treturn isTypeFlagSet(type, ts.TypeFlags.TypeVariable);\n}\n\n/**\n * Test if a type is a `UnionType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isUnionType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isUnionType(type: ts.Type): type is ts.UnionType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Union);\n}\n\n/**\n * Test if a type is a `UnionOrIntersectionType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isUnionOrIntersectionType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isUnionOrIntersectionType(\n\ttype: ts.Type,\n): type is ts.UnionOrIntersectionType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.UnionOrIntersection);\n}\n\n/**\n * Test if a type is a `UniqueESSymbolType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isUniqueESSymbolType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isUniqueESSymbolType(\n\ttype: ts.Type,\n): type is ts.UniqueESSymbolType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.UniqueESSymbol);\n}\n", "import ts from \"typescript\";\n\nimport { type IntrinsicType, isIntrinsicType } from \"./intrinsic\";\nimport { isTupleType, isTypeReference } from \"./objects\";\nimport { isFreshableType } from \"./single\";\n\n/**\n * A type that is both an {@link IntrinsicType} and a `FreshableType`\n * @category Type Types\n */\nexport interface FreshableIntrinsicType\n\textends ts.FreshableType,\n\t\tIntrinsicType {}\n\n/**\n * Test if a type is a `FreshableIntrinsicType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isFreshableIntrinsicType(type)) {\n *   // ...\n * }\n */\nexport function isFreshableIntrinsicType(\n\ttype: ts.Type,\n): type is FreshableIntrinsicType {\n\treturn isIntrinsicType(type) && isFreshableType(type);\n}\n\n/**\n * Test if a type is a `TupleTypeReference`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTupleTypeReference(type)) {\n *   // ...\n * }\n */\nexport function isTupleTypeReference(\n\ttype: ts.Type,\n): type is ts.TupleTypeReference {\n\treturn isTypeReference(type) && isTupleType(type.target);\n}\n", "import ts from \"typescript\";\n\nimport { isTypeFlagSet } from \"../../flags\";\nimport { type FreshableIntrinsicType } from \"./compound\";\n\n/**\n * A boolean literal.\n * i.e. Either a \"true\" or \"false\" literal.\n * @category Type Types\n */\nexport interface BooleanLiteralType extends UnknownLiteralType {\n\tintrinsicName: \"false\" | \"true\";\n\tvalue: boolean;\n}\n\n/**\n * Determines whether the given type is a boolean literal type.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isBooleanLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isBooleanLiteralType(\n\ttype: ts.Type,\n): type is BooleanLiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.BooleanLiteral);\n}\n\n/**\n * Test if a type is a `BigIntLiteralType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isBigIntLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isBigIntLiteralType(\n\ttype: ts.Type,\n): type is ts.BigIntLiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.BigIntLiteral);\n}\n\n/**\n * A \"false\" literal.\n * @category Type Types\n */\nexport interface FalseLiteralType extends BooleanLiteralType {\n\tintrinsicName: \"false\";\n\tvalue: false;\n}\n\n/**\n * Determines whether the given type is a boolean literal type for \"false\".\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isFalseLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isFalseLiteralType(type: ts.Type): type is FalseLiteralType {\n\treturn isBooleanLiteralType(type) && type.intrinsicName === \"false\";\n}\n\n/**\n * Test if a type is a `LiteralType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isLiteralType(type: ts.Type): type is ts.LiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Literal);\n}\n\n/**\n * Test if a type is a `NumberLiteralType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isNumberLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isNumberLiteralType(\n\ttype: ts.Type,\n): type is ts.NumberLiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.NumberLiteral);\n}\n\n/**\n * Test if a type is a `StringLiteralType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isStringLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isStringLiteralType(\n\ttype: ts.Type,\n): type is ts.StringLiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.StringLiteral);\n}\n\n/**\n * Test if a type is a `TemplateLiteralType`.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTemplateLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isTemplateLiteralType(\n\ttype: ts.Type,\n): type is ts.TemplateLiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.TemplateLiteral);\n}\n\n/**\n * A \"true\" literal.\n * @category Type Types\n */\nexport interface TrueLiteralType extends BooleanLiteralType {\n\tintrinsicName: \"true\";\n\tvalue: true;\n}\n\n/**\n * Determines whether the given type is a boolean literal type for \"true\".\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isTrueLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isTrueLiteralType(type: ts.Type): type is TrueLiteralType {\n\treturn isBooleanLiteralType(type) && type.intrinsicName === \"true\";\n}\n\n/**\n * `LiteralType` from typescript except that it allows for it to work on arbitrary types.\n * @category Type Types\n */\nexport interface UnknownLiteralType extends FreshableIntrinsicType {\n\tvalue: unknown;\n}\n\n/**\n * Test if a type is a {@link UnknownLiteralType}.\n * @category Types - Type Guards\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isUnknownLiteralType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isUnknownLiteralType(\n\ttype: ts.Type,\n): type is UnknownLiteralType {\n\treturn isTypeFlagSet(type, ts.TypeFlags.Literal);\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport {\n\tisModifierFlagSet,\n\tisNodeFlagSet,\n\tisObjectFlagSet,\n\tisSymbolFlagSet,\n\tisTypeFlagSet,\n} from \"../flags\";\nimport {\n\tisBindableObjectDefinePropertyCall,\n\tisInConstContext,\n} from \"../nodes/utilities\";\nimport { isNumericPropertyName } from \"../syntax\";\nimport { getPropertyOfType } from \"./getters\";\nimport {\n\tisFalseLiteralType,\n\tisIntersectionType,\n\tisObjectType,\n\tisTupleTypeReference,\n\tisUnionType,\n} from \"./typeGuards\";\n\n/**\n * Determines whether a type is definitely falsy. This function doesn't unwrap union types.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * if (isFalsyType(type)) {\n *   // ...\n * }\n * ```\n */\nexport function isFalsyType(type: ts.Type): boolean {\n\tif (\n\t\tisTypeFlagSet(\n\t\t\ttype,\n\t\t\tts.TypeFlags.Undefined | ts.TypeFlags.Null | ts.TypeFlags.Void,\n\t\t)\n\t) {\n\t\treturn true;\n\t}\n\n\tif (type.isLiteral()) {\n\t\treturn !type.value;\n\t}\n\n\treturn isFalseLiteralType(type);\n}\n\n/**\n * Get the intersection type parts of the given type.\n *\n * If the given type is not a intersection type, an array contain only that type will be returned.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * for (const typePart of intersectionTypeParts(type)) {\n *   // ...\n * }\n * ```\n */\nexport function intersectionTypeParts(type: ts.Type): ts.Type[] {\n\treturn isIntersectionType(type) ? type.types : [type];\n}\n\n/**\n * Get the intersection or union type parts of the given type.\n *\n * Note that this is a shallow collection: it only returns `type.types` or `[type]`.\n *\n * If the given type is not an intersection or union type, an array contain only that type will be returned.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * for (const typePart of intersectionTypeParts(type)) {\n *   // ...\n * }\n * ```\n */\nexport function typeParts(type: ts.Type): ts.Type[] {\n\treturn isIntersectionType(type) || isUnionType(type) ? type.types : [type];\n}\n\nfunction isReadonlyPropertyIntersection(\n\ttype: ts.Type,\n\tname: ts.__String,\n\ttypeChecker: ts.TypeChecker,\n) {\n\tconst typeParts = isIntersectionType(type) ? type.types : [type];\n\treturn typeParts.some((subType): boolean => {\n\t\tconst prop = getPropertyOfType(subType, name);\n\t\tif (prop === undefined) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (prop.flags & ts.SymbolFlags.Transient) {\n\t\t\tif (\n\t\t\t\t/^(?:[1-9]\\d*|0)$/.test(name as string) &&\n\t\t\t\tisTupleTypeReference(subType)\n\t\t\t) {\n\t\t\t\treturn subType.target.readonly;\n\t\t\t}\n\n\t\t\tswitch (isReadonlyPropertyFromMappedType(subType, name, typeChecker)) {\n\t\t\t\tcase true:\n\t\t\t\t\treturn true;\n\t\t\t\tcase false:\n\t\t\t\t\treturn false;\n\t\t\t\tdefault:\n\t\t\t\t// `undefined` falls through\n\t\t\t}\n\t\t}\n\n\t\treturn !!(\n\t\t\t// members of namespace import\n\t\t\t(\n\t\t\t\tisSymbolFlagSet(prop, ts.SymbolFlags.ValueModule) ||\n\t\t\t\t// we unwrapped every mapped type, now we can check the actual declarations\n\t\t\t\tsymbolHasReadonlyDeclaration(prop, typeChecker)\n\t\t\t)\n\t\t);\n\t});\n}\n\nfunction isReadonlyPropertyFromMappedType(\n\ttype: ts.Type,\n\tname: ts.__String,\n\ttypeChecker: ts.TypeChecker,\n): boolean | undefined {\n\tif (!isObjectType(type) || !isObjectFlagSet(type, ts.ObjectFlags.Mapped)) {\n\t\treturn;\n\t}\n\n\tconst declaration = type.symbol.declarations![0] as ts.MappedTypeNode;\n\t// well-known symbols are not affected by mapped types\n\tif (\n\t\tdeclaration.readonlyToken !== undefined &&\n\t\t!/^__@[^@]+$/.test(name as string)\n\t) {\n\t\treturn declaration.readonlyToken.kind !== ts.SyntaxKind.MinusToken;\n\t}\n\n\tconst { modifiersType } = type as { modifiersType?: ts.Type };\n\n\treturn (\n\t\tmodifiersType && isPropertyReadonlyInType(modifiersType, name, typeChecker)\n\t);\n}\n\nfunction isCallback(\n\ttypeChecker: ts.TypeChecker,\n\tparam: ts.Symbol,\n\tnode: ts.Node,\n): boolean {\n\tlet type: ts.Type | undefined = typeChecker.getApparentType(\n\t\ttypeChecker.getTypeOfSymbolAtLocation(param, node),\n\t);\n\tif ((param.valueDeclaration as ts.ParameterDeclaration).dotDotDotToken) {\n\t\t// unwrap array type of rest parameter\n\t\ttype = type.getNumberIndexType();\n\t\tif (type === undefined) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfor (const subType of unionTypeParts(type)) {\n\t\tif (subType.getCallSignatures().length !== 0) {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * Determines whether writing to a certain property of a given type is allowed.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const property: ts.Symbol;\n * declare const type: ts.Type;\n * declare const typeChecker: ts.TypeChecker;\n *\n * if (isPropertyReadonlyInType(type, property.getEscapedName(), typeChecker)) {\n *   // ...\n * }\n * ```\n */\nexport function isPropertyReadonlyInType(\n\ttype: ts.Type,\n\tname: ts.__String,\n\ttypeChecker: ts.TypeChecker,\n): boolean {\n\tlet seenProperty = false;\n\tlet seenReadonlySignature = false;\n\tfor (const subType of unionTypeParts(type)) {\n\t\tif (getPropertyOfType(subType, name) === undefined) {\n\t\t\t// property is not present in this part of the union -> check for readonly index signature\n\t\t\tconst index =\n\t\t\t\t(isNumericPropertyName(name)\n\t\t\t\t\t? typeChecker.getIndexInfoOfType(subType, ts.IndexKind.Number)\n\t\t\t\t\t: undefined) ??\n\t\t\t\ttypeChecker.getIndexInfoOfType(subType, ts.IndexKind.String);\n\t\t\tif (index?.isReadonly) {\n\t\t\t\tif (seenProperty) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tseenReadonlySignature = true;\n\t\t\t}\n\t\t} else if (\n\t\t\tseenReadonlySignature ||\n\t\t\tisReadonlyPropertyIntersection(subType, name, typeChecker)\n\t\t) {\n\t\t\treturn true;\n\t\t} else {\n\t\t\tseenProperty = true;\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * Returns true for `Object.defineProperty(o, 'prop', {value, writable: false})` and `Object.defineProperty(o, 'prop', {get: () => 1})`\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const node: ts.CallExpression;\n * declare const typeChecker: ts.TypeChecker;\n *\n * if (isReadonlyAssignmentDeclaration(node, typeChecker)) {\n *   // ...\n * }\n * ```\n */\nfunction isReadonlyAssignmentDeclaration(\n\tnode: ts.CallExpression,\n\ttypeChecker: ts.TypeChecker,\n) {\n\tif (!isBindableObjectDefinePropertyCall(node)) {\n\t\treturn false;\n\t}\n\n\tconst descriptorType = typeChecker.getTypeAtLocation(node.arguments[2]);\n\tif (descriptorType.getProperty(\"value\") === undefined) {\n\t\treturn descriptorType.getProperty(\"set\") === undefined;\n\t}\n\n\tconst writableProp = descriptorType.getProperty(\"writable\");\n\tif (writableProp === undefined) {\n\t\treturn false;\n\t}\n\n\tconst writableType =\n\t\twritableProp.valueDeclaration !== undefined &&\n\t\tts.isPropertyAssignment(writableProp.valueDeclaration)\n\t\t\t? typeChecker.getTypeAtLocation(writableProp.valueDeclaration.initializer)\n\t\t\t: typeChecker.getTypeOfSymbolAtLocation(writableProp, node.arguments[2]);\n\treturn isFalseLiteralType(writableType);\n}\n\n/**\n * Determines whether a type is thenable and thus can be used with `await`.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const node: ts.Node;\n * declare const type: ts.Type;\n * declare const typeChecker: ts.TypeChecker;\n *\n * if (isThenableType(typeChecker, node, type)) {\n *   // ...\n * }\n * ```\n */\nexport function isThenableType(\n\ttypeChecker: ts.TypeChecker,\n\tnode: ts.Node,\n\ttype: ts.Type,\n): boolean;\n\n/**\n * Determines whether a type is thenable and thus can be used with `await`.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const expression: ts.Expression;\n * declare const typeChecker: ts.TypeChecker;\n *\n * if (isThenableType(typeChecker, expression)) {\n *   // ...\n * }\n * ```\n * @example\n * ```ts\n * declare const expression: ts.Expression;\n * declare const typeChecker: ts.TypeChecker;\n * declare const type: ts.Type;\n *\n * if (isThenableType(typeChecker, expression, type)) {\n *   // ...\n * }\n * ```\n */\nexport function isThenableType(\n\ttypeChecker: ts.TypeChecker,\n\tnode: ts.Expression,\n\ttype?: ts.Type,\n): boolean;\n\nexport function isThenableType(\n\ttypeChecker: ts.TypeChecker,\n\tnode: ts.Node,\n\ttype = typeChecker.getTypeAtLocation(node)!,\n): boolean {\n\tfor (const typePart of unionTypeParts(typeChecker.getApparentType(type))) {\n\t\tconst then = typePart.getProperty(\"then\");\n\t\tif (then === undefined) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst thenType = typeChecker.getTypeOfSymbolAtLocation(then, node);\n\t\tfor (const subTypePart of unionTypeParts(thenType)) {\n\t\t\tfor (const signature of subTypePart.getCallSignatures()) {\n\t\t\t\tif (\n\t\t\t\t\tsignature.parameters.length !== 0 &&\n\t\t\t\t\tisCallback(typeChecker, signature.parameters[0], node)\n\t\t\t\t) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn false;\n}\n\n/**\n * Test if the given symbol has a readonly declaration.\n * @category Symbols - Utilities\n * @example\n * ```ts\n * declare const symbol: ts.Symbol;\n * declare const typeChecker: ts.TypeChecker;\n *\n * if (symbolHasReadonlyDeclaration(symbol, typeChecker)) {\n *   // ...\n * }\n * ```\n */\nexport function symbolHasReadonlyDeclaration(\n\tsymbol: ts.Symbol,\n\ttypeChecker: ts.TypeChecker,\n): boolean {\n\treturn !!(\n\t\t(symbol.flags & ts.SymbolFlags.Accessor) === ts.SymbolFlags.GetAccessor ||\n\t\tsymbol.declarations?.some(\n\t\t\t(node) =>\n\t\t\t\tisModifierFlagSet(node, ts.ModifierFlags.Readonly) ||\n\t\t\t\t(ts.isVariableDeclaration(node) &&\n\t\t\t\t\tisNodeFlagSet(node.parent, ts.NodeFlags.Const)) ||\n\t\t\t\t(ts.isCallExpression(node) &&\n\t\t\t\t\tisReadonlyAssignmentDeclaration(node, typeChecker)) ||\n\t\t\t\tts.isEnumMember(node) ||\n\t\t\t\t((ts.isPropertyAssignment(node) ||\n\t\t\t\t\tts.isShorthandPropertyAssignment(node)) &&\n\t\t\t\t\tisInConstContext(node.parent)),\n\t\t)\n\t);\n}\n\n/**\n * Get the union type parts of the given type.\n *\n * If the given type is not a union type, an array contain only that type will be returned.\n * @category Types - Utilities\n * @example\n * ```ts\n * declare const type: ts.Type;\n *\n * for (const typePart of unionTypeParts(type)) {\n *   // ...\n * }\n * ```\n */\nexport function unionTypeParts(type: ts.Type): ts.Type[] {\n\treturn isUnionType(type) ? type.types : [type];\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport {\n\tisConstAssertionExpression,\n\tisEntityNameExpression,\n\tisNumericOrStringLikeLiteral,\n} from \"./typeGuards\";\n\n/**\n * Determines whether a call to {@link Object.defineProperty} is statically analyzable.\n * @internal\n */\nexport function isBindableObjectDefinePropertyCall(\n\tnode: ts.CallExpression,\n): boolean {\n\treturn (\n\t\tnode.arguments.length === 3 &&\n\t\tisEntityNameExpression(node.arguments[0]) &&\n\t\tisNumericOrStringLikeLiteral(node.arguments[1]) &&\n\t\tts.isPropertyAccessExpression(node.expression) &&\n\t\tnode.expression.name.escapedText === \"defineProperty\" &&\n\t\tts.isIdentifier(node.expression.expression) &&\n\t\tnode.expression.expression.escapedText === \"Object\"\n\t);\n}\n\n/**\n * Detects whether an expression is affected by an enclosing `as const` assertion and therefore treated literally.\n * @internal\n */\nexport function isInConstContext(node: ts.Expression): boolean {\n\tlet current: ts.Node = node;\n\twhile (true) {\n\t\tconst parent = current.parent;\n\t\touter: switch (parent.kind) {\n\t\t\tcase ts.SyntaxKind.TypeAssertionExpression:\n\t\t\tcase ts.SyntaxKind.AsExpression:\n\t\t\t\treturn isConstAssertionExpression(parent as ts.AssertionExpression);\n\t\t\tcase ts.SyntaxKind.PrefixUnaryExpression:\n\t\t\t\tif (current.kind !== ts.SyntaxKind.NumericLiteral) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tswitch ((parent as ts.PrefixUnaryExpression).operator) {\n\t\t\t\t\tcase ts.SyntaxKind.PlusToken:\n\t\t\t\t\tcase ts.SyntaxKind.MinusToken:\n\t\t\t\t\t\tcurrent = parent;\n\t\t\t\t\t\tbreak outer;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\tcase ts.SyntaxKind.PropertyAssignment:\n\t\t\t\tif ((parent as ts.PropertyAssignment).initializer !== current) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\tcurrent = parent.parent!;\n\t\t\t\tbreak;\n\t\t\tcase ts.SyntaxKind.ShorthandPropertyAssignment:\n\t\t\t\tcurrent = parent.parent!;\n\t\t\t\tbreak;\n\t\t\tcase ts.SyntaxKind.ParenthesizedExpression:\n\t\t\tcase ts.SyntaxKind.ArrayLiteralExpression:\n\t\t\tcase ts.SyntaxKind.ObjectLiteralExpression:\n\t\t\tcase ts.SyntaxKind.TemplateExpression:\n\t\t\t\tcurrent = parent;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\treturn false;\n\t\t}\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { includesModifier } from \"../modifiers\";\nimport {\n\tScope,\n\tScopeBoundary,\n\tScopeBoundarySelector,\n\tisBlockScopeBoundary,\n} from \"./Scope\";\nimport { DeclarationDomain } from \"./declarations\";\nimport { getPropertyName } from \"./getPropertyName\";\nimport { getUsageDomain } from \"./getUsageDomain\";\nimport {\n\tBlockScope,\n\tClassExpressionScope,\n\tConditionalTypeScope,\n\tConditionalTypeScopeState,\n\tFunctionExpressionScope,\n\tFunctionScope,\n\tNonRootScope,\n\tRootScope,\n} from \"./scopes\";\nimport { UsageInfo, UsageInfoCallback } from \"./usage\";\nimport {\n\tcanHaveDecorators,\n\tgetDecorators,\n\tidentifierToKeywordKind,\n} from \"./utils\";\n\n// TODO class decorators resolve outside of class, element and parameter decorator resolve inside/at the class\n// TODO computed property name resolves inside/at the class\n// TODO this and super in all of them are resolved outside of the class\nexport class UsageWalker {\n\t#result = new Map<ts.Identifier, UsageInfo>();\n\t#scope!: Scope;\n\n\t#handleBindingName(\n\t\tname: ts.BindingName,\n\t\tblockScoped: boolean,\n\t\texported: boolean,\n\t) {\n\t\tif (name.kind === ts.SyntaxKind.Identifier) {\n\t\t\treturn this.#scope.addVariable(\n\t\t\t\tname.text,\n\t\t\t\tname,\n\t\t\t\tblockScoped\n\t\t\t\t\t? ScopeBoundarySelector.Block\n\t\t\t\t\t: ScopeBoundarySelector.Function,\n\t\t\t\texported,\n\t\t\t\tDeclarationDomain.Value,\n\t\t\t);\n\t\t}\n\n\t\tforEachDestructuringIdentifier(name, (declaration) => {\n\t\t\tthis.#scope.addVariable(\n\t\t\t\tdeclaration.name.text,\n\t\t\t\tdeclaration.name,\n\t\t\t\tblockScoped\n\t\t\t\t\t? ScopeBoundarySelector.Block\n\t\t\t\t\t: ScopeBoundarySelector.Function,\n\t\t\t\texported,\n\t\t\t\tDeclarationDomain.Value,\n\t\t\t);\n\t\t});\n\t}\n\n\t#handleConditionalType(\n\t\tnode: ts.ConditionalTypeNode,\n\t\tcb: (node: ts.Node) => void,\n\t\tvarCb: UsageInfoCallback,\n\t) {\n\t\tconst savedScope = this.#scope;\n\t\tconst scope = (this.#scope = new ConditionalTypeScope(savedScope));\n\t\tcb(node.checkType);\n\t\tscope.updateState(ConditionalTypeScopeState.Extends);\n\t\tcb(node.extendsType);\n\t\tscope.updateState(ConditionalTypeScopeState.TrueType);\n\t\tcb(node.trueType);\n\t\tscope.updateState(ConditionalTypeScopeState.FalseType);\n\t\tcb(node.falseType);\n\t\tscope.end(varCb);\n\t\tthis.#scope = savedScope;\n\t}\n\n\t#handleDeclaration(\n\t\tnode: ts.NamedDeclaration,\n\t\tblockScoped: boolean,\n\t\tdomain: DeclarationDomain,\n\t) {\n\t\tif (node.name !== undefined) {\n\t\t\tthis.#scope.addVariable(\n\t\t\t\t(node.name as ts.Identifier).text,\n\t\t\t\tnode.name as ts.Identifier,\n\t\t\t\tblockScoped\n\t\t\t\t\t? ScopeBoundarySelector.Block\n\t\t\t\t\t: ScopeBoundarySelector.Function,\n\t\t\t\tincludesModifier(\n\t\t\t\t\t(node as ts.HasModifiers).modifiers,\n\t\t\t\t\tts.SyntaxKind.ExportKeyword,\n\t\t\t\t),\n\t\t\t\tdomain,\n\t\t\t);\n\t\t}\n\t}\n\n\t#handleFunctionLikeDeclaration(\n\t\tnode: ts.FunctionLikeDeclaration,\n\t\tcb: (node: ts.Node) => void,\n\t\tvarCb: UsageInfoCallback,\n\t) {\n\t\tif (canHaveDecorators(node)) {\n\t\t\tgetDecorators(node)?.forEach(cb);\n\t\t}\n\n\t\tconst savedScope = this.#scope;\n\t\tif (node.kind === ts.SyntaxKind.FunctionDeclaration) {\n\t\t\tthis.#handleDeclaration(node, false, DeclarationDomain.Value);\n\t\t}\n\n\t\tconst scope = (this.#scope =\n\t\t\tnode.kind === ts.SyntaxKind.FunctionExpression && node.name !== undefined\n\t\t\t\t? new FunctionExpressionScope(node.name, savedScope)\n\t\t\t\t: new FunctionScope(savedScope));\n\t\tif (node.name !== undefined) {\n\t\t\tcb(node.name);\n\t\t}\n\n\t\tif (node.typeParameters !== undefined) {\n\t\t\tnode.typeParameters.forEach(cb);\n\t\t}\n\n\t\tnode.parameters.forEach(cb);\n\t\tif (node.type !== undefined) {\n\t\t\tcb(node.type);\n\t\t}\n\n\t\tif (node.body !== undefined) {\n\t\t\tscope.beginBody();\n\t\t\tcb(node.body);\n\t\t}\n\n\t\tscope.end(varCb);\n\t\tthis.#scope = savedScope;\n\t}\n\n\t#handleModule(\n\t\tnode: ts.ModuleDeclaration,\n\t\tnext: (node: ts.Node, scope: Scope) => void,\n\t) {\n\t\tif (node.flags & ts.NodeFlags.GlobalAugmentation) {\n\t\t\treturn next(\n\t\t\t\tnode,\n\t\t\t\tthis.#scope.createOrReuseNamespaceScope(\"-global\", false, true, false),\n\t\t\t);\n\t\t}\n\n\t\tif (node.name.kind === ts.SyntaxKind.Identifier) {\n\t\t\tconst exported = isNamespaceExported(node as ts.NamespaceDeclaration);\n\t\t\tthis.#scope.addVariable(\n\t\t\t\tnode.name.text,\n\t\t\t\tnode.name,\n\t\t\t\tScopeBoundarySelector.Function,\n\t\t\t\texported,\n\t\t\t\tDeclarationDomain.Namespace | DeclarationDomain.Value,\n\t\t\t);\n\t\t\tconst ambient = includesModifier(\n\t\t\t\tnode.modifiers,\n\t\t\t\tts.SyntaxKind.DeclareKeyword,\n\t\t\t);\n\t\t\treturn next(\n\t\t\t\tnode,\n\t\t\t\tthis.#scope.createOrReuseNamespaceScope(\n\t\t\t\t\tnode.name.text,\n\t\t\t\t\texported,\n\t\t\t\t\tambient,\n\t\t\t\t\tambient && namespaceHasExportStatement(node),\n\t\t\t\t),\n\t\t\t);\n\t\t}\n\n\t\treturn next(\n\t\t\tnode,\n\t\t\tthis.#scope.createOrReuseNamespaceScope(\n\t\t\t\t`\"${node.name.text}\"`,\n\t\t\t\tfalse,\n\t\t\t\ttrue,\n\t\t\t\tnamespaceHasExportStatement(node),\n\t\t\t),\n\t\t);\n\t}\n\n\t#handleVariableDeclaration(declarationList: ts.VariableDeclarationList) {\n\t\tconst blockScoped = isBlockScopedVariableDeclarationList(declarationList);\n\t\tconst exported =\n\t\t\tdeclarationList.parent.kind === ts.SyntaxKind.VariableStatement &&\n\t\t\tincludesModifier(\n\t\t\t\tdeclarationList.parent.modifiers,\n\t\t\t\tts.SyntaxKind.ExportKeyword,\n\t\t\t);\n\t\tfor (const declaration of declarationList.declarations) {\n\t\t\tthis.#handleBindingName(declaration.name, blockScoped, exported);\n\t\t}\n\t}\n\n\tgetUsage(sourceFile: ts.SourceFile): Map<ts.Identifier, UsageInfo> {\n\t\tconst variableCallback = (variable: UsageInfo, key: ts.Identifier) => {\n\t\t\tthis.#result.set(key, variable);\n\t\t};\n\n\t\tconst isModule = ts.isExternalModule(sourceFile);\n\t\tthis.#scope = new RootScope(\n\t\t\tsourceFile.isDeclarationFile &&\n\t\t\t\tisModule &&\n\t\t\t\t!containsExportStatement(sourceFile),\n\t\t\t!isModule,\n\t\t);\n\t\tconst cb = (node: ts.Node): void => {\n\t\t\tif (isBlockScopeBoundary(node)) {\n\t\t\t\treturn continueWithScope(\n\t\t\t\t\tnode,\n\t\t\t\t\tnew BlockScope(this.#scope.getFunctionScope(), this.#scope),\n\t\t\t\t\thandleBlockScope,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tswitch (node.kind) {\n\t\t\t\tcase ts.SyntaxKind.ClassExpression:\n\t\t\t\t\treturn continueWithScope(\n\t\t\t\t\t\tnode,\n\t\t\t\t\t\t(node as ts.ClassExpression).name !== undefined\n\t\t\t\t\t\t\t? new ClassExpressionScope(\n\t\t\t\t\t\t\t\t\t(node as ts.ClassExpression).name!,\n\t\t\t\t\t\t\t\t\tthis.#scope,\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t: new NonRootScope(this.#scope, ScopeBoundary.Function),\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.ClassDeclaration:\n\t\t\t\t\tthis.#handleDeclaration(\n\t\t\t\t\t\tnode as ts.ClassDeclaration,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tDeclarationDomain.Value | DeclarationDomain.Type,\n\t\t\t\t\t);\n\t\t\t\t\treturn continueWithScope(\n\t\t\t\t\t\tnode,\n\t\t\t\t\t\tnew NonRootScope(this.#scope, ScopeBoundary.Function),\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.InterfaceDeclaration:\n\t\t\t\tcase ts.SyntaxKind.TypeAliasDeclaration:\n\t\t\t\t\tthis.#handleDeclaration(\n\t\t\t\t\t\tnode as ts.InterfaceDeclaration | ts.TypeAliasDeclaration,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tDeclarationDomain.Type,\n\t\t\t\t\t);\n\t\t\t\t\treturn continueWithScope(\n\t\t\t\t\t\tnode,\n\t\t\t\t\t\tnew NonRootScope(this.#scope, ScopeBoundary.Type),\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.EnumDeclaration:\n\t\t\t\t\tthis.#handleDeclaration(\n\t\t\t\t\t\tnode as ts.EnumDeclaration,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tDeclarationDomain.Any,\n\t\t\t\t\t);\n\t\t\t\t\treturn continueWithScope(\n\t\t\t\t\t\tnode,\n\t\t\t\t\t\tthis.#scope.createOrReuseEnumScope(\n\t\t\t\t\t\t\t(node as ts.EnumDeclaration).name.text,\n\t\t\t\t\t\t\tincludesModifier(\n\t\t\t\t\t\t\t\t(node as ts.HasModifiers).modifiers,\n\t\t\t\t\t\t\t\tts.SyntaxKind.ExportKeyword,\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t),\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.ModuleDeclaration:\n\t\t\t\t\treturn this.#handleModule(\n\t\t\t\t\t\tnode as ts.ModuleDeclaration,\n\t\t\t\t\t\tcontinueWithScope,\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.MappedType:\n\t\t\t\t\treturn continueWithScope(\n\t\t\t\t\t\tnode,\n\t\t\t\t\t\tnew NonRootScope(this.#scope, ScopeBoundary.Type),\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.FunctionExpression:\n\t\t\t\tcase ts.SyntaxKind.ArrowFunction:\n\t\t\t\tcase ts.SyntaxKind.Constructor:\n\t\t\t\tcase ts.SyntaxKind.MethodDeclaration:\n\t\t\t\tcase ts.SyntaxKind.FunctionDeclaration:\n\t\t\t\tcase ts.SyntaxKind.GetAccessor:\n\t\t\t\tcase ts.SyntaxKind.SetAccessor:\n\t\t\t\tcase ts.SyntaxKind.MethodSignature:\n\t\t\t\tcase ts.SyntaxKind.CallSignature:\n\t\t\t\tcase ts.SyntaxKind.ConstructSignature:\n\t\t\t\tcase ts.SyntaxKind.ConstructorType:\n\t\t\t\tcase ts.SyntaxKind.FunctionType:\n\t\t\t\t\treturn this.#handleFunctionLikeDeclaration(\n\t\t\t\t\t\tnode as ts.FunctionLikeDeclaration,\n\t\t\t\t\t\tcb,\n\t\t\t\t\t\tvariableCallback,\n\t\t\t\t\t);\n\t\t\t\tcase ts.SyntaxKind.ConditionalType:\n\t\t\t\t\treturn this.#handleConditionalType(\n\t\t\t\t\t\tnode as ts.ConditionalTypeNode,\n\t\t\t\t\t\tcb,\n\t\t\t\t\t\tvariableCallback,\n\t\t\t\t\t);\n\t\t\t\t// End of Scope specific handling\n\t\t\t\tcase ts.SyntaxKind.VariableDeclarationList:\n\t\t\t\t\tthis.#handleVariableDeclaration(node as ts.VariableDeclarationList);\n\t\t\t\t\tbreak;\n\t\t\t\tcase ts.SyntaxKind.Parameter:\n\t\t\t\t\tif (\n\t\t\t\t\t\tnode.parent.kind !== ts.SyntaxKind.IndexSignature &&\n\t\t\t\t\t\t((node as ts.ParameterDeclaration).name.kind !==\n\t\t\t\t\t\t\tts.SyntaxKind.Identifier ||\n\t\t\t\t\t\t\tidentifierToKeywordKind(\n\t\t\t\t\t\t\t\t(node as ts.NamedDeclaration).name as ts.Identifier,\n\t\t\t\t\t\t\t) !== ts.SyntaxKind.ThisKeyword)\n\t\t\t\t\t) {\n\t\t\t\t\t\tthis.#handleBindingName(\n\t\t\t\t\t\t\t(node as ts.NamedDeclaration).name as ts.Identifier,\n\t\t\t\t\t\t\tfalse,\n\t\t\t\t\t\t\tfalse,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\tcase ts.SyntaxKind.EnumMember:\n\t\t\t\t\tthis.#scope.addVariable(\n\t\t\t\t\t\tgetPropertyName((node as ts.EnumMember).name)!,\n\t\t\t\t\t\t(node as ts.EnumMember).name,\n\t\t\t\t\t\tScopeBoundarySelector.Function,\n\t\t\t\t\t\ttrue,\n\t\t\t\t\t\tDeclarationDomain.Value,\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\t\t\t\tcase ts.SyntaxKind.ImportClause:\n\t\t\t\tcase ts.SyntaxKind.ImportSpecifier:\n\t\t\t\tcase ts.SyntaxKind.NamespaceImport:\n\t\t\t\tcase ts.SyntaxKind.ImportEqualsDeclaration:\n\t\t\t\t\tthis.#handleDeclaration(\n\t\t\t\t\t\tnode as ts.NamedDeclaration,\n\t\t\t\t\t\tfalse,\n\t\t\t\t\t\tDeclarationDomain.Any | DeclarationDomain.Import,\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\t\t\t\tcase ts.SyntaxKind.TypeParameter:\n\t\t\t\t\tthis.#scope.addVariable(\n\t\t\t\t\t\t(node as ts.TypeParameterDeclaration).name.text,\n\t\t\t\t\t\t(node as ts.TypeParameterDeclaration).name,\n\t\t\t\t\t\tnode.parent.kind === ts.SyntaxKind.InferType\n\t\t\t\t\t\t\t? ScopeBoundarySelector.InferType\n\t\t\t\t\t\t\t: ScopeBoundarySelector.Type,\n\t\t\t\t\t\tfalse,\n\t\t\t\t\t\tDeclarationDomain.Type,\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\t\t\t\tcase ts.SyntaxKind.ExportSpecifier:\n\t\t\t\t\tif ((node as ts.ExportSpecifier).propertyName !== undefined) {\n\t\t\t\t\t\treturn this.#scope.markExported(\n\t\t\t\t\t\t\t(node as ts.ExportSpecifier).propertyName!,\n\t\t\t\t\t\t\t(node as ts.ExportSpecifier).name,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn this.#scope.markExported((node as ts.ExportSpecifier).name);\n\t\t\t\tcase ts.SyntaxKind.ExportAssignment:\n\t\t\t\t\tif (\n\t\t\t\t\t\t(node as ts.ExportAssignment).expression.kind ===\n\t\t\t\t\t\tts.SyntaxKind.Identifier\n\t\t\t\t\t) {\n\t\t\t\t\t\treturn this.#scope.markExported(\n\t\t\t\t\t\t\t(node as ts.ExportAssignment).expression as ts.Identifier,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\tcase ts.SyntaxKind.Identifier: {\n\t\t\t\t\tconst domain = getUsageDomain(node as ts.Identifier);\n\t\t\t\t\tif (domain !== undefined) {\n\t\t\t\t\t\tthis.#scope.addUse({ domain, location: node as ts.Identifier });\n\t\t\t\t\t}\n\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn ts.forEachChild(node, cb);\n\t\t};\n\n\t\tconst continueWithScope = <T extends ts.Node>(\n\t\t\tnode: T,\n\t\t\tscope: Scope,\n\t\t\tnext: (node: T) => void = forEachChild,\n\t\t) => {\n\t\t\tconst savedScope = this.#scope;\n\t\t\tthis.#scope = scope;\n\t\t\tnext(node);\n\t\t\tthis.#scope.end(variableCallback);\n\t\t\tthis.#scope = savedScope;\n\t\t};\n\n\t\tconst handleBlockScope = (node: ts.Node) => {\n\t\t\tif (\n\t\t\t\tnode.kind === ts.SyntaxKind.CatchClause &&\n\t\t\t\t(node as ts.CatchClause).variableDeclaration !== undefined\n\t\t\t) {\n\t\t\t\tthis.#handleBindingName(\n\t\t\t\t\t(node as ts.CatchClause).variableDeclaration!.name,\n\t\t\t\t\ttrue,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn ts.forEachChild(node, cb);\n\t\t};\n\n\t\tts.forEachChild(sourceFile, cb);\n\t\tthis.#scope.end(variableCallback);\n\t\treturn this.#result;\n\n\t\tfunction forEachChild(node: ts.Node) {\n\t\t\treturn ts.forEachChild(node, cb);\n\t\t}\n\t}\n}\n\nfunction isNamespaceExported(node: ts.NamespaceDeclaration) {\n\treturn (\n\t\tnode.parent.kind === ts.SyntaxKind.ModuleDeclaration ||\n\t\tincludesModifier(node.modifiers, ts.SyntaxKind.ExportKeyword)\n\t);\n}\n\nfunction namespaceHasExportStatement(ns: ts.ModuleDeclaration): boolean {\n\tif (ns.body === undefined || ns.body.kind !== ts.SyntaxKind.ModuleBlock) {\n\t\treturn false;\n\t}\n\n\treturn containsExportStatement(ns.body);\n}\n\nfunction containsExportStatement(block: ts.BlockLike): boolean {\n\tfor (const statement of block.statements) {\n\t\tif (\n\t\t\tstatement.kind === ts.SyntaxKind.ExportDeclaration ||\n\t\t\tstatement.kind === ts.SyntaxKind.ExportAssignment\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n}\n\nfunction isBlockScopedVariableDeclarationList(\n\tdeclarationList: ts.VariableDeclarationList,\n): boolean {\n\treturn (declarationList.flags & ts.NodeFlags.BlockScoped) !== 0;\n}\n\nfunction forEachDestructuringIdentifier<T>(\n\tpattern: ts.BindingPattern,\n\tfn: (element: ts.BindingElement & { name: ts.Identifier }) => T,\n): T | undefined {\n\tfor (const element of pattern.elements) {\n\t\tif (element.kind !== ts.SyntaxKind.BindingElement) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet result: T | undefined;\n\t\tif (element.name.kind === ts.SyntaxKind.Identifier) {\n\t\t\tresult = fn(element as ts.BindingElement & { name: ts.Identifier });\n\t\t} else {\n\t\t\tresult = forEachDestructuringIdentifier(element.name, fn);\n\t\t}\n\n\t\tif (result) {\n\t\t\treturn result;\n\t\t}\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport type { EnumScope, NamespaceScope } from \"./scopes\";\n\nimport { isFunctionScopeBoundary } from \"../scopes\";\nimport { DeclarationDomain } from \"./declarations\";\nimport { InternalUsageInfo, Usage, UsageInfoCallback } from \"./usage\";\n\nexport enum ScopeBoundary {\n\tBlock = 2,\n\tConditionalType = 8,\n\tFunction = 1,\n\tNone = 0,\n\tType = 4,\n}\n\nexport enum ScopeBoundarySelector {\n\tFunction = ScopeBoundary.Function,\n\t// eslint-disable-next-line perfectionist/sort-enums\n\tBlock = ScopeBoundarySelector.Function | ScopeBoundary.Block,\n\tInferType = ScopeBoundary.ConditionalType,\n\tType = ScopeBoundarySelector.Block | ScopeBoundary.Type,\n}\n\nexport interface Scope {\n\taddUse(use: Usage, scope?: Scope): void;\n\taddVariable(\n\t\tidentifier: string,\n\t\tname: ts.PropertyName,\n\t\tselector: ScopeBoundarySelector,\n\t\texported: boolean,\n\t\tdomain: DeclarationDomain,\n\t): void;\n\tcreateOrReuseEnumScope(name: string, exported: boolean): EnumScope;\n\tcreateOrReuseNamespaceScope(\n\t\tname: string,\n\t\texported: boolean,\n\t\tambient: boolean,\n\t\thasExportStatement: boolean,\n\t): NamespaceScope;\n\tend(cb: UsageInfoCallback): void;\n\tgetDestinationScope(selector: ScopeBoundarySelector): Scope;\n\tgetFunctionScope(): Scope;\n\tgetVariables(): Map<string, InternalUsageInfo>;\n\tmarkExported(name: ts.Identifier, as?: ts.Identifier): void;\n}\n\nexport function isBlockScopeBoundary(node: ts.Node): ScopeBoundary {\n\tswitch (node.kind) {\n\t\tcase ts.SyntaxKind.Block: {\n\t\t\tconst parent = node.parent;\n\t\t\treturn parent.kind !== ts.SyntaxKind.CatchClause &&\n\t\t\t\t// blocks inside SourceFile are block scope boundaries\n\t\t\t\t(parent.kind === ts.SyntaxKind.SourceFile ||\n\t\t\t\t\t// blocks that are direct children of a function scope boundary are no scope boundary\n\t\t\t\t\t// for example the FunctionBlock is part of the function scope of the containing function\n\t\t\t\t\t!isFunctionScopeBoundary(parent))\n\t\t\t\t? ScopeBoundary.Block\n\t\t\t\t: ScopeBoundary.None;\n\t\t}\n\n\t\tcase ts.SyntaxKind.ForStatement:\n\t\tcase ts.SyntaxKind.ForInStatement:\n\t\tcase ts.SyntaxKind.ForOfStatement:\n\t\tcase ts.SyntaxKind.CaseBlock:\n\t\tcase ts.SyntaxKind.CatchClause:\n\t\tcase ts.SyntaxKind.WithStatement:\n\t\t\treturn ScopeBoundary.Block;\n\t\tdefault:\n\t\t\treturn ScopeBoundary.None;\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { identifierToKeywordKind } from \"./utils\";\n\n/**\n * Metadata for how a declaration was declared and/or referenced.\n */\nexport interface DeclarationInfo {\n\tdeclaration: ts.PropertyName;\n\tdomain: DeclarationDomain;\n\texported: boolean;\n}\n\n/**\n * Which \"domain\"(s) (most commonly, type or value space) a declaration is within.\n */\nexport enum DeclarationDomain {\n\tImport = 8,\n\tNamespace = 1,\n\tType = 2,\n\tValue = 4,\n\t// eslint-disable-next-line perfectionist/sort-enums\n\tAny = Namespace | Type | Value,\n}\n\nexport function getDeclarationDomain(\n\tnode: ts.Identifier,\n): DeclarationDomain | undefined {\n\tswitch (node.parent.kind) {\n\t\tcase ts.SyntaxKind.TypeParameter:\n\t\tcase ts.SyntaxKind.InterfaceDeclaration:\n\t\tcase ts.SyntaxKind.TypeAliasDeclaration:\n\t\t\treturn DeclarationDomain.Type;\n\t\tcase ts.SyntaxKind.ClassDeclaration:\n\t\tcase ts.SyntaxKind.ClassExpression:\n\t\t\treturn DeclarationDomain.Type | DeclarationDomain.Value;\n\t\tcase ts.SyntaxKind.EnumDeclaration:\n\t\t\treturn DeclarationDomain.Any;\n\t\tcase ts.SyntaxKind.NamespaceImport:\n\t\tcase ts.SyntaxKind.ImportClause:\n\t\t\treturn DeclarationDomain.Any | DeclarationDomain.Import; // TODO handle type-only imports\n\t\tcase ts.SyntaxKind.ImportEqualsDeclaration:\n\t\tcase ts.SyntaxKind.ImportSpecifier:\n\t\t\treturn (node.parent as ts.ImportEqualsDeclaration | ts.ImportSpecifier)\n\t\t\t\t.name === node\n\t\t\t\t? DeclarationDomain.Any | DeclarationDomain.Import // TODO handle type-only imports\n\t\t\t\t: undefined;\n\t\tcase ts.SyntaxKind.ModuleDeclaration:\n\t\t\treturn DeclarationDomain.Namespace;\n\t\tcase ts.SyntaxKind.Parameter:\n\t\t\tif (\n\t\t\t\tnode.parent.parent.kind === ts.SyntaxKind.IndexSignature ||\n\t\t\t\tidentifierToKeywordKind(node) === ts.SyntaxKind.ThisKeyword\n\t\t\t) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t// falls through\n\t\tcase ts.SyntaxKind.BindingElement:\n\t\tcase ts.SyntaxKind.VariableDeclaration:\n\t\t\treturn (node.parent as ts.VariableLikeDeclaration).name === node\n\t\t\t\t? DeclarationDomain.Value\n\t\t\t\t: undefined;\n\t\tcase ts.SyntaxKind.FunctionDeclaration:\n\t\tcase ts.SyntaxKind.FunctionExpression:\n\t\t\treturn DeclarationDomain.Value;\n\t}\n}\n", "import ts from \"typescript\";\n\n/**\n * Supports TypeScript<5 versions that don't have identifierToKeywordKind.\n */\nexport function identifierTo<PERSON><PERSON>wordKind(\n\tnode: ts.Identifier,\n): ts.SyntaxKind | undefined {\n\treturn \"identifierToKeywordKind\" in ts\n\t\t? ts.identifierTo<PERSON>eywordKind(node)\n\t\t: // eslint-disable-next-line deprecation/deprecation\n\t\t\tnode.originalKeywordKind;\n}\n\n/**\n * Supports TypeScript<4.8 versions that don't have canHaveDecorators.\n */\nexport function canHaveDecorators(node: ts.Node): node is ts.HasDecorators {\n\treturn \"canHaveDecorators\" in ts\n\t\t? ts.canHaveDecorators(node)\n\t\t: \"decorators\" in node;\n}\n\ntype NodeWithDecorators = ts.HasDecorators & {\n\tdecorators: readonly ts.Decorator[] | undefined;\n};\n\n/**\n * Supports TypeScript<4.8 versions that don't have getDecorators.\n */\nexport function getDecorators(\n\tnode: ts.HasDecorators,\n): readonly ts.Decorator[] | undefined {\n\treturn \"getDecorators\" in ts\n\t\t? ts.getDecorators(node)\n\t\t: (node as NodeWithDecorators).decorators;\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { isNumericOrStringLikeLiteral } from \"../nodes/typeGuards/compound\";\n\nfunction unwrapParentheses(node: ts.Expression) {\n\twhile (node.kind === ts.SyntaxKind.ParenthesizedExpression) {\n\t\tnode = (node as ts.ParenthesizedExpression).expression;\n\t}\n\n\treturn node;\n}\n\nexport function getPropertyName(\n\tpropertyName: ts.PropertyName,\n): string | undefined {\n\tif (propertyName.kind === ts.SyntaxKind.ComputedPropertyName) {\n\t\tconst expression = unwrapParentheses(propertyName.expression);\n\t\tif (ts.isPrefixUnaryExpression(expression)) {\n\t\t\tlet negate = false;\n\t\t\tswitch (expression.operator) {\n\t\t\t\tcase ts.SyntaxKind.MinusToken:\n\t\t\t\t\tnegate = true;\n\t\t\t\t// falls through\n\t\t\t\tcase ts.SyntaxKind.PlusToken:\n\t\t\t\t\treturn ts.isNumericLiteral(expression.operand)\n\t\t\t\t\t\t? `${negate ? \"-\" : \"\"}${expression.operand.text}`\n\t\t\t\t\t\t: ts.isBigIntLiteral(expression.operand)\n\t\t\t\t\t\t\t? `${negate ? \"-\" : \"\"}${expression.operand.text.slice(0, -1)}`\n\t\t\t\t\t\t\t: undefined;\n\t\t\t\tdefault:\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\tif (ts.isBigIntLiteral(expression)) {\n\t\t\treturn expression.text.slice(0, -1);\n\t\t}\n\n\t\tif (isNumericOrStringLikeLiteral(expression)) {\n\t\t\treturn expression.text;\n\t\t}\n\n\t\treturn;\n\t}\n\n\treturn propertyName.kind === ts.SyntaxKind.PrivateIdentifier\n\t\t? undefined\n\t\t: propertyName.text;\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { identifierToKeywordKind } from \"./utils\";\n\n/**\n * Which \"domain\"(s) (most commonly, type or value space) a usage is within.\n */\nexport enum UsageDomain {\n\tNamespace = 1,\n\tType = 2,\n\tTypeQuery = 8,\n\tValue = 4,\n\tValueOrNamespace = Value | Namespace,\n\t// eslint-disable-next-line perfectionist/sort-enums\n\tAny = Namespace | Type | Value,\n}\n\n// TODO handle cases where values are used only for their types, e.g. `declare [propSymbol]: number`\nexport function getUsageDomain(node: ts.Identifier): UsageDomain | undefined {\n\tconst parent = node.parent;\n\tswitch (parent.kind) {\n\t\tcase ts.SyntaxKind.TypeReference:\n\t\t\treturn identifierToKeywordKind(node) !== ts.SyntaxKind.ConstKeyword\n\t\t\t\t? UsageDomain.Type\n\t\t\t\t: undefined;\n\t\tcase ts.SyntaxKind.ExpressionWithTypeArguments:\n\t\t\treturn (parent.parent as ts.HeritageClause).token ===\n\t\t\t\tts.SyntaxKind.ImplementsKeyword ||\n\t\t\t\tparent.parent.parent.kind === ts.SyntaxKind.InterfaceDeclaration\n\t\t\t\t? UsageDomain.Type\n\t\t\t\t: UsageDomain.Value;\n\t\tcase ts.SyntaxKind.TypeQuery:\n\t\t\treturn UsageDomain.ValueOrNamespace | UsageDomain.TypeQuery;\n\t\tcase ts.SyntaxKind.QualifiedName:\n\t\t\tif ((parent as ts.QualifiedName).left === node) {\n\t\t\t\tif (\n\t\t\t\t\tgetEntityNameParent(parent as ts.QualifiedName).kind ===\n\t\t\t\t\tts.SyntaxKind.TypeQuery\n\t\t\t\t) {\n\t\t\t\t\treturn UsageDomain.Namespace | UsageDomain.TypeQuery;\n\t\t\t\t}\n\n\t\t\t\treturn UsageDomain.Namespace;\n\t\t\t}\n\n\t\t\tbreak;\n\t\tcase ts.SyntaxKind.ExportSpecifier:\n\t\t\t// either {name} or {propertyName as name}\n\t\t\tif (\n\t\t\t\t(parent as ts.ExportSpecifier).propertyName === undefined ||\n\t\t\t\t(parent as ts.ExportSpecifier).propertyName === node\n\t\t\t) {\n\t\t\t\treturn UsageDomain.Any;\n\t\t\t} // TODO handle type-only exports\n\n\t\t\tbreak;\n\t\tcase ts.SyntaxKind.ExportAssignment:\n\t\t\treturn UsageDomain.Any;\n\t\t// Value\n\t\tcase ts.SyntaxKind.BindingElement:\n\t\t\tif ((parent as ts.BindingElement).initializer === node) {\n\t\t\t\treturn UsageDomain.ValueOrNamespace;\n\t\t\t}\n\n\t\t\tbreak;\n\t\tcase ts.SyntaxKind.Parameter:\n\t\tcase ts.SyntaxKind.EnumMember:\n\t\tcase ts.SyntaxKind.PropertyDeclaration:\n\t\tcase ts.SyntaxKind.VariableDeclaration:\n\t\tcase ts.SyntaxKind.PropertyAssignment:\n\t\tcase ts.SyntaxKind.PropertyAccessExpression:\n\t\tcase ts.SyntaxKind.ImportEqualsDeclaration:\n\t\t\tif ((parent as ts.NamedDeclaration).name !== node) {\n\t\t\t\treturn UsageDomain.ValueOrNamespace;\n\t\t\t} // TODO handle type-only imports\n\n\t\t\tbreak;\n\t\tcase ts.SyntaxKind.JsxAttribute:\n\t\tcase ts.SyntaxKind.FunctionDeclaration:\n\t\tcase ts.SyntaxKind.FunctionExpression:\n\t\tcase ts.SyntaxKind.NamespaceImport:\n\t\tcase ts.SyntaxKind.ClassDeclaration:\n\t\tcase ts.SyntaxKind.ClassExpression:\n\t\tcase ts.SyntaxKind.ModuleDeclaration:\n\t\tcase ts.SyntaxKind.MethodDeclaration:\n\t\tcase ts.SyntaxKind.EnumDeclaration:\n\t\tcase ts.SyntaxKind.GetAccessor:\n\t\tcase ts.SyntaxKind.SetAccessor:\n\t\tcase ts.SyntaxKind.LabeledStatement:\n\t\tcase ts.SyntaxKind.BreakStatement:\n\t\tcase ts.SyntaxKind.ContinueStatement:\n\t\tcase ts.SyntaxKind.ImportClause:\n\t\tcase ts.SyntaxKind.ImportSpecifier:\n\t\tcase ts.SyntaxKind.TypePredicate: // TODO this actually references a parameter\n\t\tcase ts.SyntaxKind.MethodSignature:\n\t\tcase ts.SyntaxKind.PropertySignature:\n\t\tcase ts.SyntaxKind.NamespaceExportDeclaration:\n\t\tcase ts.SyntaxKind.NamespaceExport:\n\t\tcase ts.SyntaxKind.InterfaceDeclaration:\n\t\tcase ts.SyntaxKind.TypeAliasDeclaration:\n\t\tcase ts.SyntaxKind.TypeParameter:\n\t\tcase ts.SyntaxKind.NamedTupleMember:\n\t\t\tbreak;\n\t\tdefault:\n\t\t\treturn UsageDomain.ValueOrNamespace;\n\t}\n}\n\nfunction getEntityNameParent(name: ts.EntityName) {\n\tlet parent = name.parent;\n\twhile (parent.kind === ts.SyntaxKind.QualifiedName) {\n\t\tparent = parent.parent!;\n\t}\n\n\treturn parent;\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { Scope, ScopeBoundary, ScopeBoundarySelector } from \"./Scope\";\nimport {\n\tDeclarationDomain,\n\tDeclarationInfo,\n\tgetDeclarationDomain,\n} from \"./declarations\";\nimport {\n\tInternalUsageInfo,\n\tUsage,\n\tUsageInfo,\n\tUsageInfoCallback,\n} from \"./usage\";\n\nabstract class AbstractScope implements Scope {\n\t#enumScopes: Map<string, EnumScope> | undefined = undefined;\n\tprotected namespaceScopes: Map<string, NamespaceScope> | undefined =\n\t\tundefined;\n\tprotected uses: Usage[] = [];\n\tprotected variables = new Map<string, InternalUsageInfo>();\n\n\tconstructor(protected global: boolean) {}\n\n\taddUse(use: Usage): void {\n\t\tthis.uses.push(use);\n\t}\n\n\t// eslint-disable-next-line @typescript-eslint/no-empty-function\n\tprotected addUseToParent(_use: Usage): void {}\n\n\taddVariable(\n\t\tidentifier: string,\n\t\tname: ts.PropertyName,\n\t\tselector: ScopeBoundarySelector,\n\t\texported: boolean,\n\t\tdomain: DeclarationDomain,\n\t): void {\n\t\tconst variables = this.getDestinationScope(selector).getVariables();\n\t\tconst declaration: DeclarationInfo = {\n\t\t\tdeclaration: name,\n\t\t\tdomain,\n\t\t\texported,\n\t\t};\n\t\tconst variable = variables.get(identifier);\n\t\tif (variable === undefined) {\n\t\t\tvariables.set(identifier, {\n\t\t\t\tdeclarations: [declaration],\n\t\t\t\tdomain,\n\t\t\t\tuses: [],\n\t\t\t});\n\t\t} else {\n\t\t\tvariable.domain |= domain;\n\t\t\tvariable.declarations.push(declaration);\n\t\t}\n\t}\n\n\tprotected applyUse(use: Usage, variables = this.variables): boolean {\n\t\tconst variable = variables.get(use.location.text);\n\t\tif (variable === undefined || (variable.domain & use.domain) === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tvariable.uses.push(use);\n\t\treturn true;\n\t}\n\n\tprotected applyUses(): void {\n\t\tfor (const use of this.uses) {\n\t\t\tif (!this.applyUse(use)) {\n\t\t\t\tthis.addUseToParent(use);\n\t\t\t}\n\t\t}\n\n\t\tthis.uses = [];\n\t}\n\n\tcreateOrReuseEnumScope(name: string, _exported: boolean): EnumScope {\n\t\tlet scope: EnumScope | undefined;\n\t\tif (this.#enumScopes === undefined) {\n\t\t\tthis.#enumScopes = new Map();\n\t\t} else {\n\t\t\tscope = this.#enumScopes.get(name);\n\t\t}\n\n\t\tif (scope === undefined) {\n\t\t\tscope = new EnumScope(this);\n\t\t\tthis.#enumScopes.set(name, scope);\n\t\t}\n\n\t\treturn scope;\n\t} // only relevant for the root scope\n\n\tcreateOrReuseNamespaceScope(\n\t\tname: string,\n\t\t_exported: boolean,\n\t\tambient: boolean,\n\t\thasExportStatement: boolean,\n\t): NamespaceScope {\n\t\tlet scope: NamespaceScope | undefined;\n\t\tif (this.namespaceScopes === undefined) {\n\t\t\tthis.namespaceScopes = new Map();\n\t\t} else {\n\t\t\tscope = this.namespaceScopes.get(name);\n\t\t}\n\n\t\tif (scope === undefined) {\n\t\t\tscope = new NamespaceScope(ambient, hasExportStatement, this);\n\t\t\tthis.namespaceScopes.set(name, scope);\n\t\t} else {\n\t\t\tscope.refresh(ambient, hasExportStatement);\n\t\t}\n\n\t\treturn scope;\n\t}\n\n\tend(cb: UsageInfoCallback): void {\n\t\tif (this.namespaceScopes !== undefined) {\n\t\t\tthis.namespaceScopes.forEach((value) => value.finish(cb));\n\t\t}\n\n\t\tthis.namespaceScopes = this.#enumScopes = undefined;\n\t\tthis.applyUses();\n\t\tthis.variables.forEach((variable) => {\n\t\t\tfor (const declaration of variable.declarations) {\n\t\t\t\tconst result: UsageInfo = {\n\t\t\t\t\tdeclarations: [],\n\t\t\t\t\tdomain: declaration.domain,\n\t\t\t\t\texported: declaration.exported,\n\t\t\t\t\tinGlobalScope: this.global,\n\t\t\t\t\tuses: [],\n\t\t\t\t};\n\t\t\t\tfor (const other of variable.declarations) {\n\t\t\t\t\tif (other.domain & declaration.domain) {\n\t\t\t\t\t\tresult.declarations.push(other.declaration as ts.Identifier);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tfor (const use of variable.uses) {\n\t\t\t\t\tif (use.domain & declaration.domain) {\n\t\t\t\t\t\tresult.uses.push(use);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tcb(result, declaration.declaration as ts.Identifier, this);\n\t\t\t}\n\t\t});\n\t}\n\n\tgetFunctionScope(): Scope {\n\t\treturn this;\n\t}\n\n\tgetVariables(): Map<string, InternalUsageInfo> {\n\t\treturn this.variables;\n\t}\n\n\t// eslint-disable-next-line @typescript-eslint/no-empty-function\n\tmarkExported(_name: ts.Identifier): void {}\n\n\tabstract getDestinationScope(selector: ScopeBoundarySelector): Scope;\n}\n\nexport class NonRootScope extends AbstractScope {\n\tconstructor(\n\t\tprotected parent: Scope,\n\t\tprotected boundary: ScopeBoundary,\n\t) {\n\t\tsuper(false);\n\t}\n\n\tprotected addUseToParent(use: Usage): void {\n\t\treturn this.parent.addUse(use, this);\n\t}\n\n\tgetDestinationScope(selector: ScopeBoundarySelector): Scope {\n\t\treturn this.boundary & selector\n\t\t\t? this\n\t\t\t: this.parent.getDestinationScope(selector);\n\t}\n}\n\nexport class EnumScope extends NonRootScope {\n\tconstructor(parent: Scope) {\n\t\tsuper(parent, ScopeBoundary.Function);\n\t}\n\n\tend(): void {\n\t\tthis.applyUses();\n\t}\n}\n\nexport class RootScope extends AbstractScope {\n\t#exportAll: boolean;\n\t#exports: string[] | undefined = undefined;\n\t#innerScope = new NonRootScope(this, ScopeBoundary.Function);\n\n\tconstructor(exportAll: boolean, global: boolean) {\n\t\tsuper(global);\n\t\tthis.#exportAll = exportAll;\n\t}\n\n\taddUse(use: Usage, origin?: Scope): void {\n\t\tif (origin === this.#innerScope) {\n\t\t\treturn super.addUse(use);\n\t\t}\n\n\t\treturn this.#innerScope.addUse(use);\n\t}\n\n\taddVariable(\n\t\tidentifier: string,\n\t\tname: ts.PropertyName,\n\t\tselector: ScopeBoundarySelector,\n\t\texported: boolean,\n\t\tdomain: DeclarationDomain,\n\t): void {\n\t\tif (domain & DeclarationDomain.Import) {\n\t\t\treturn super.addVariable(identifier, name, selector, exported, domain);\n\t\t}\n\n\t\treturn this.#innerScope.addVariable(\n\t\t\tidentifier,\n\t\t\tname,\n\t\t\tselector,\n\t\t\texported,\n\t\t\tdomain,\n\t\t);\n\t}\n\n\tend(cb: UsageInfoCallback): void {\n\t\tthis.#innerScope.end((value, key) => {\n\t\t\tvalue.exported ||=\n\t\t\t\tthis.#exportAll ||\n\t\t\t\t(this.#exports !== undefined && this.#exports.includes(key.text));\n\t\t\tvalue.inGlobalScope = this.global;\n\t\t\treturn cb(value, key, this);\n\t\t});\n\t\treturn super.end((value, key, scope) => {\n\t\t\tvalue.exported ||=\n\t\t\t\tscope === this &&\n\t\t\t\tthis.#exports !== undefined &&\n\t\t\t\tthis.#exports.includes(key.text);\n\t\t\treturn cb(value, key, scope);\n\t\t});\n\t}\n\n\tgetDestinationScope(): this {\n\t\treturn this;\n\t}\n\n\tmarkExported(id: ts.Identifier): void {\n\t\tif (this.#exports === undefined) {\n\t\t\tthis.#exports = [id.text];\n\t\t} else {\n\t\t\tthis.#exports.push(id.text);\n\t\t}\n\t}\n}\n\nexport class NamespaceScope extends NonRootScope {\n\t#ambient: boolean;\n\t#exports: Set<string> | undefined = undefined;\n\t#hasExport: boolean;\n\t#innerScope = new NonRootScope(this, ScopeBoundary.Function);\n\n\tconstructor(ambient: boolean, hasExport: boolean, parent: Scope) {\n\t\tsuper(parent, ScopeBoundary.Function);\n\t\tthis.#ambient = ambient;\n\t\tthis.#hasExport = hasExport;\n\t}\n\n\taddUse(use: Usage, source?: Scope): void {\n\t\tif (source !== this.#innerScope) {\n\t\t\treturn this.#innerScope.addUse(use);\n\t\t}\n\n\t\tthis.uses.push(use);\n\t}\n\n\tcreateOrReuseEnumScope(name: string, exported: boolean): EnumScope {\n\t\tif (!exported && (!this.#ambient || this.#hasExport)) {\n\t\t\treturn this.#innerScope.createOrReuseEnumScope(name, exported);\n\t\t}\n\n\t\treturn super.createOrReuseEnumScope(name, exported);\n\t}\n\n\tcreateOrReuseNamespaceScope(\n\t\tname: string,\n\t\texported: boolean,\n\t\tambient: boolean,\n\t\thasExportStatement: boolean,\n\t): NamespaceScope {\n\t\tif (!exported && (!this.#ambient || this.#hasExport)) {\n\t\t\treturn this.#innerScope.createOrReuseNamespaceScope(\n\t\t\t\tname,\n\t\t\t\texported,\n\t\t\t\tambient || this.#ambient,\n\t\t\t\thasExportStatement,\n\t\t\t);\n\t\t}\n\n\t\treturn super.createOrReuseNamespaceScope(\n\t\t\tname,\n\t\t\texported,\n\t\t\tambient || this.#ambient,\n\t\t\thasExportStatement,\n\t\t);\n\t}\n\n\tend(cb: UsageInfoCallback): void {\n\t\tthis.#innerScope.end((variable, key, scope) => {\n\t\t\tif (\n\t\t\t\tscope !== this.#innerScope ||\n\t\t\t\t(!variable.exported &&\n\t\t\t\t\t(!this.#ambient ||\n\t\t\t\t\t\t(this.#exports !== undefined && !this.#exports.has(key.text))))\n\t\t\t) {\n\t\t\t\treturn cb(variable, key, scope);\n\t\t\t}\n\n\t\t\tconst namespaceVar = this.variables.get(key.text);\n\t\t\tif (namespaceVar === undefined) {\n\t\t\t\tthis.variables.set(key.text, {\n\t\t\t\t\tdeclarations: variable.declarations.map(mapDeclaration),\n\t\t\t\t\tdomain: variable.domain,\n\t\t\t\t\tuses: [...variable.uses],\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\touter: for (const declaration of variable.declarations) {\n\t\t\t\t\tfor (const existing of namespaceVar.declarations) {\n\t\t\t\t\t\tif (existing.declaration === declaration) {\n\t\t\t\t\t\t\tcontinue outer;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tnamespaceVar.declarations.push(mapDeclaration(declaration));\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tnamespaceVar.domain |= variable.domain;\n\t\t\t\tfor (const use of variable.uses) {\n\t\t\t\t\tif (namespaceVar.uses.includes(use)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tnamespaceVar.uses.push(use);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\tthis.applyUses();\n\t\tthis.#innerScope = new NonRootScope(this, ScopeBoundary.Function);\n\t}\n\n\tfinish(cb: UsageInfoCallback): void {\n\t\treturn super.end(cb);\n\t}\n\n\tgetDestinationScope(): Scope {\n\t\treturn this.#innerScope;\n\t}\n\n\tmarkExported(name: ts.Identifier): void {\n\t\tif (this.#exports === undefined) {\n\t\t\tthis.#exports = new Set();\n\t\t}\n\n\t\tthis.#exports.add(name.text);\n\t}\n\n\trefresh(ambient: boolean, hasExport: boolean): void {\n\t\tthis.#ambient = ambient;\n\t\tthis.#hasExport = hasExport;\n\t}\n}\n\nfunction mapDeclaration(declaration: ts.Identifier): DeclarationInfo {\n\treturn {\n\t\tdeclaration,\n\t\tdomain: getDeclarationDomain(declaration)!,\n\t\texported: true,\n\t};\n}\n\nexport class FunctionScope extends NonRootScope {\n\tconstructor(parent: Scope) {\n\t\tsuper(parent, ScopeBoundary.Function);\n\t}\n\n\tbeginBody(): void {\n\t\tthis.applyUses();\n\t}\n}\n\nabstract class AbstractNamedExpressionScope<\n\tInnerScope extends NonRootScope,\n> extends NonRootScope {\n\t#domain: DeclarationDomain;\n\n\t#name: ts.Identifier;\n\tconstructor(name: ts.Identifier, domain: DeclarationDomain, parent: Scope) {\n\t\tsuper(parent, ScopeBoundary.Function);\n\t\tthis.#name = name;\n\t\tthis.#domain = domain;\n\t}\n\n\taddUse(use: Usage, source?: Scope): void {\n\t\tif (source !== this.innerScope) {\n\t\t\treturn this.innerScope.addUse(use);\n\t\t}\n\n\t\tif (use.domain & this.#domain && use.location.text === this.#name.text) {\n\t\t\tthis.uses.push(use);\n\t\t} else {\n\t\t\treturn this.parent.addUse(use, this);\n\t\t}\n\t}\n\n\tend(cb: UsageInfoCallback): void {\n\t\tthis.innerScope.end(cb);\n\t\treturn cb(\n\t\t\t{\n\t\t\t\tdeclarations: [this.#name],\n\t\t\t\tdomain: this.#domain,\n\t\t\t\texported: false,\n\t\t\t\tinGlobalScope: false,\n\t\t\t\tuses: this.uses,\n\t\t\t},\n\t\t\tthis.#name,\n\t\t\tthis,\n\t\t);\n\t}\n\n\tgetDestinationScope(): InnerScope {\n\t\treturn this.innerScope;\n\t}\n\n\tgetFunctionScope(): InnerScope {\n\t\treturn this.innerScope;\n\t}\n\n\tprotected abstract get innerScope(): InnerScope;\n}\n\nexport class FunctionExpressionScope extends AbstractNamedExpressionScope<FunctionScope> {\n\tprotected innerScope = new FunctionScope(this);\n\n\tconstructor(name: ts.Identifier, parent: Scope) {\n\t\tsuper(name, DeclarationDomain.Value, parent);\n\t}\n\n\tbeginBody(): void {\n\t\treturn this.innerScope.beginBody();\n\t}\n}\n\nexport class BlockScope extends NonRootScope {\n\t#functionScope: Scope;\n\n\tconstructor(functionScope: Scope, parent: Scope) {\n\t\tsuper(parent, ScopeBoundary.Block);\n\t\tthis.#functionScope = functionScope;\n\t}\n\n\tgetFunctionScope(): Scope {\n\t\treturn this.#functionScope;\n\t}\n}\n\nexport class ClassExpressionScope extends AbstractNamedExpressionScope<NonRootScope> {\n\tprotected innerScope = new NonRootScope(this, ScopeBoundary.Function);\n\n\tconstructor(name: ts.Identifier, parent: Scope) {\n\t\tsuper(name, DeclarationDomain.Value | DeclarationDomain.Type, parent);\n\t}\n}\n\nexport enum ConditionalTypeScopeState {\n\tInitial,\n\tExtends,\n\tTrueType,\n\tFalseType,\n}\n\nexport class ConditionalTypeScope extends NonRootScope {\n\t#state = ConditionalTypeScopeState.Initial;\n\n\tconstructor(parent: Scope) {\n\t\tsuper(parent, ScopeBoundary.ConditionalType);\n\t}\n\n\taddUse(use: Usage): void {\n\t\tif (this.#state === ConditionalTypeScopeState.TrueType) {\n\t\t\treturn void this.uses.push(use);\n\t\t}\n\n\t\treturn this.parent.addUse(use, this);\n\t}\n\n\tupdateState(newState: ConditionalTypeScopeState): void {\n\t\tthis.#state = newState;\n\t}\n}\n", "// Code largely based on https://github.com/ajafff/tsutils\n// Original license: https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE\n\nimport ts from \"typescript\";\n\nimport { UsageWalker } from \"./UsageWalker\";\nimport { UsageInfo } from \"./usage\";\n\n/**\n * Creates a mapping of each declared type and value to its type information.\n * @category Nodes - Other Utilities\n * @example\n * ```ts\n * declare const sourceFile: ts.SourceFile;\n *\n * const usage = collectVariableUsage(sourceFile);\n *\n * for (const [identifier, information] of usage) {\n * \tconsole.log(`${identifier.getText()} is used ${information.uses.length} time(s).`);\n * }\n * ```\n */\nexport function collectVariableUsage(\n\tsourceFile: ts.SourceFile,\n): Map<ts.Identifier, UsageInfo> {\n\treturn new UsageWalker().getUsage(sourceFile);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACGA,IAAAA,qBAAe;;;ACEf,wBAAe;AAsBR,SAAS,aACf,MACA,UACA,aAA4B,KAAK,cAAc,GACxC;AACP,QAAM,QAAQ,CAAC;AACf,SAAO,MAAM;AACZ,QAAI,kBAAAC,QAAG,YAAY,KAAK,IAAI,GAAG;AAC9B,eAAS,IAAI;AAAA,IACd;AAAA;AAAA,MAEC,KAAK,SAAS,kBAAAA,QAAG,WAAW;AAAA,MAC3B;AACD,YAAM,WAAW,KAAK,YAAY,UAAU;AAC5C,UAAI,SAAS,WAAW,GAAG;AAC1B,eAAO,SAAS,CAAC;AACjB;AAAA,MACD;AAGA,eAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC9C,cAAM,KAAK,SAAS,CAAC,CAAC;AAAA,MACvB;AAAA,IACD;AAEA,QAAI,MAAM,WAAW,GAAG;AACvB;AAAA,IACD;AAEA,WAAO,MAAM,IAAI;AAAA,EAClB;AACD;;;AD/CA,SAAS,sBAAsB,OAAyB;AACvD,UAAQ,MAAM,MAAM;AAAA,IACnB,KAAK,mBAAAC,QAAG,WAAW;AAElB,aACC,MAAM,OAAO,SAAS,mBAAAA,QAAG,WAAW,iBACpC,CAAC,uBAAuB,MAAM,OAAO,MAAM;AAAA,IAE7C,KAAK,mBAAAA,QAAG,WAAW;AAClB,cAAQ,MAAM,OAAO,MAAM;AAAA,QAC1B,KAAK,mBAAAA,QAAG,WAAW;AAElB,iBAAO,MAAM,QAAQ,MAAM,OAAO;AAAA,QACnC,KAAK,mBAAAA,QAAG,WAAW;AAClB,iBAAO;AAAA,QACR,KAAK,mBAAAA,QAAG,WAAW;AAClB,iBACC,MAAM,QAAQ,MAAM,OAAO;AAAA,UAC3B,CAAC,uBAAuB,MAAM,OAAO,MAAM;AAAA,QAE7C,KAAK,mBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,mBAAAA,QAAG,WAAW;AAElB,iBAAO,CAAC,uBAAuB,MAAM,OAAO,OAAO,MAAM;AAAA,MAC3D;AAAA,EACF;AAEA,SAAO;AACR;AAMA,SAAS,uBACR,MACyC;AACzC,SACC,KAAK,SAAS,mBAAAA,QAAG,WAAW,cAC5B,KAAK,SAAS,mBAAAA,QAAG,WAAW;AAE9B;AAuBO,SAAS,eACf,MACA,UACA,aAA4B,KAAK,cAAc,GACxC;AAMP,QAAM,WAAW,WAAW;AAC5B,QAAM,SAAS,WAAW,oBAAoB,mBAAAA,QAAG,gBAAgB;AACjE,SAAO;AAAA,IACN;AAAA,IACA,CAAC,UAAU;AACV,UAAI,MAAM,QAAQ,MAAM,KAAK;AAC5B;AAAA,MACD;AAEA,UAAI,MAAM,SAAS,mBAAAA,QAAG,WAAW,SAAS;AACzC,2BAAAA,QAAG;AAAA,UACF;AAAA;AAAA,UAEA,MAAM,QAAQ,KAAK,mBAAAA,QAAG,WAAW,QAAQ,KAAK,IAAI,SAAS,MAAM;AAAA,UACjE;AAAA,QACD;AAAA,MACD;AAEA,UAAI,UAAU,sBAAsB,KAAK,GAAG;AAC3C,eAAO,mBAAAA,QAAG;AAAA,UACT;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IACA;AAAA,EACD;AACA,WAAS,gBAAgB,KAAa,KAAa,MAAsB;AACxE,aAAS,UAAU,EAAE,KAAK,MAAM,IAAI,CAAC;AAAA,EACtC;AACD;;;AEjHA,IAAAC,qBAAe;AA8BR,SAAS,wBACf,SACA,QACU;AACV,UAAQ,QAAQ;AAAA,IACf,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACJ,aACC,QAAQ,MAAM,MAAM,QACpB,wBAAwB,SAAS,aAAa;AAAA,IAEhD,KAAK;AACJ,aACC,QAAQ,eAAe,wBAAwB,SAAS,WAAW;AAAA,IAErE,KAAK;AACJ,aAAO,QAAQ,gBAAgB,SAC5B,wBAAwB,SAAS,WAAW,IAC5C,QAAQ;AAAA,IACZ,KAAK;AACJ,aACC,QAAQ,uBACR,wBAAwB,SAAS,cAAc;AAAA,IAEjD,KAAK;AACJ,aACC,QAAQ,mCAAmC,QAC3C,wBAAwB,SAAS,eAAe;AAAA,IAElD,KAAK;AACJ,aAAO,QAAQ,iCAAiC,SAC7C,QAAQ,+BACR,wBAAwB,SAAS,iBAAiB,KAClD,QAAQ,WAAW,mBAAAC,QAAG,WAAW;AAAA,IACrC,KAAK;AACJ,aACC,QAAQ,6BAA6B,QACrC,wBAAwB,SAAS,kBAAkB;AAAA,IAErD,KAAK;AACJ,aAAO,QAAQ,YAAY,SACxB,wBAAwB,SAAS,SAAS,IAC1C,QAAQ;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAEJ,aAAO;AAAA,QACN;AAAA,QACA;AAAA,MACD;AAAA,EACF;AAEA,SAAO,QAAQ,MAAM,MAAM;AAC5B;AAuCO,SAAS,8BACf,SACA,QACU;AACV,UACE,QAAQ,SAAS,QAAQ,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,UACjE,WAAW,kCACX,8BAA8B,SAAS,kBAAkB;AAE5D;;;ACzIA,IAAAC,qBAAe;AAMf,SAAS,UAAU,UAAkB,MAAuB;AAC3D,UAAQ,WAAW,UAAU;AAC9B;AAMA,SAAS,kBAAkB,KAAwB,MAAuB;AACzE,SAAO,UAAU,IAAI,OAAO,IAAI;AACjC;AAcO,SAAS,kBACf,MACA,MACU;AACV,SAAO,UAAU,mBAAAC,QAAG,yBAAyB,IAAI,GAAG,IAAI;AACzD;AAcO,IAAM,gBACZ;AAcM,SAAS,gBACf,YACA,MACU;AACV,SAAO,UAAU,WAAW,aAAa,IAAI;AAC9C;AAcO,IAAM,kBAGE;AAcR,IAAM,gBACZ;;;ACzFM,SAAS,iBACf,cACG,OACO;AACV,MAAI,cAAc,QAAW;AAC5B,WAAO;AAAA,EACR;AAEA,aAAW,YAAY,WAAW;AACjC,QAAI,MAAM,SAAS,SAAS,IAA6B,GAAG;AAC3D,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;;;AC3BA,IAAAC,qBAAe;;;ACAf,IAAAC,qBAAe;AAYR,SAAS,iBAAiB,MAA8B;AAC9D,SACC,QAAQ,mBAAAC,QAAG,WAAW,mBACtB,QAAQ,mBAAAA,QAAG,WAAW;AAExB;AAWO,SAAS,sBAAsB,MAAqC;AAC1E,SAAO,OAAO,CAAC,IAAI,MAAM;AAC1B;AAEA,SAAS,SAAS,IAAY;AAC7B,SAAO,MAAM,QAAU,IAAI;AAC5B;AAWO,SAAS,sBACf,MACA,kBAAkB,mBAAAA,QAAG,aAAa,QACxB;AACV,MAAI,KAAK,WAAW,GAAG;AACtB,WAAO;AAAA,EACR;AAEA,MAAI,KAAK,KAAK,YAAY,CAAC;AAC3B,MAAI,CAAC,mBAAAA,QAAG,kBAAkB,IAAI,eAAe,GAAG;AAC/C,WAAO;AAAA,EACR;AAEA,WAAS,IAAI,SAAS,EAAE,GAAG,IAAI,KAAK,QAAQ,KAAK,SAAS,EAAE,GAAG;AAC9D,SAAK,KAAK,YAAY,CAAC;AACvB,QAAI,CAAC,mBAAAA,QAAG,iBAAiB,IAAI,eAAe,GAAG;AAC9C,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;;;AD1DO,IAAK,aAAL,kBAAKC,gBAAL;AACN,EAAAA,wBAAA,UAAO,KAAP;AACA,EAAAA,wBAAA,UAAO,KAAP;AACA,EAAAA,wBAAA,WAAQ,KAAR;AACA,EAAAA,wBAAA,YAAS,KAAT;AACA,EAAAA,wBAAA,eAAY,KAAZ;AALW,SAAAA;AAAA,GAAA;AAoBL,SAAS,cAAc,MAAiC;AAC9D,QAAM,SAAS,KAAK;AACpB,UAAQ,OAAO,MAAM;AAAA,IACpB,KAAK,mBAAAC,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAAoC,aAC3C,mBAAAA,QAAG,WAAW,iBACb,OAAoC,aACpC,mBAAAA,QAAG,WAAW,kBACb,oBACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAA+B,UAAU,OAC9C,eACA,CAAC,iBAAkB,OAA+B,cAAc,IAAI,IACnE,eACC,OAA+B,cAAc,SAC7C,mBAAAA,QAAG,WAAW,cACd,gBACA;AAAA,IACN,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OACN,gCAAgC,OAC/B,eACA,4BAA4B,MAAwC,IACnE,gBACA;AAAA,IACL,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAAiC,SAAS,OAC/C,eACA,4BAA4B,MAA+B,IAC1D,gBACA;AAAA,IACL,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,QACN;AAAA,MAID,IACG,gBACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAElB,aAAO,cAAc,MAAuB;AAAA,IAC7C,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAAiC,gBAAgB,OACtD,gBACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAClB,aACE,OAA0C,OAC1C,UAAU,mBAAAA,QAAG,WAAW,kBACzB,OAAO,OAAO,OAAO,SAAS,mBAAAA,QAAG,WAAW,uBAC1C,eACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAA4B,SAAS,OAC1C,eACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAA2B,gBAAgB,OAChD,eACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAAuC,eAAe,OAC3D,eACA;AAAA,IACJ,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAQ,OAA+B,iBACpC,eACA;AAAA,EACL;AAEA,SAAO;AACR;AAEA,SAAS,4BACR,MAOU;AACV,UAAQ,KAAK,MAAM;AAAA,IAClB,KAAK,mBAAAA,QAAG,WAAW;AAClB,UAAI,KAAK,gCAAgC,QAAW;AACnD,eAAO;AAAA,MACR;AAAA,IAGD,KAAK,mBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,aAAO,KAAK;AAGZ;AAAA,IACD,KAAK,mBAAAA,QAAG,WAAW;AAClB,UAAI,KAAK,OAAO,SAAS,mBAAAA,QAAG,WAAW,wBAAwB;AAC9D,eAAO;AAAA,MACR;AAEA,aAAO,KAAK;AAAA,EACd;AAEA,SAAO,MAAM;AACZ,YAAQ,KAAK,OAAO,MAAM;AAAA,MACzB,KAAK,mBAAAA,QAAG,WAAW;AAClB,eACE,KAAK,OAA+B,SAAS,QAC7C,KAAK,OAA+B,cAAc,SAClD,mBAAAA,QAAG,WAAW;AAAA,MAEjB,KAAK,mBAAAA,QAAG,WAAW;AAClB,eAAQ,KAAK,OAA6B,gBAAgB;AAAA,MAC3D,KAAK,mBAAAA,QAAG,WAAW;AAAA,MACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,eAAO,KAAK;AAGZ;AAAA,MACD,KAAK,mBAAAA,QAAG,WAAW;AAAA,MACnB,KAAK,mBAAAA,QAAG,WAAW;AAClB,eAAO,KAAK,OAAO;AACnB;AAAA,MACD,KAAK,mBAAAA,QAAG,WAAW;AAClB,YAAI,KAAK,OAAO,OAAO,SAAS,mBAAAA,QAAG,WAAW,wBAAwB;AACrE,iBAAO;AAAA,QACR;AAEA,eAAO,KAAK,OAAO;AACnB;AAAA,MACD;AACC,eAAO;AAAA,IACT;AAAA,EACD;AACD;;;AEnNA,IAAAC,sBAAe;;;ACAf,IAAAC,qBAAe;AAqHR,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,KAAK,SAAS,mBAAAC,QAAG,WAAW;AACpC;AAeO,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,aAAa,MAAmC;AAC/D,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,eAAe,MAAwC;AACtE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,eAAe,MAAwC;AACtE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,gBAAgB,MAAsC;AACrE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,iBAAiB,MAAuC;AACvE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,aAAa,MAAsC;AAClE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,eAAe,MAAwC;AACtE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,WAAW,MAAoC;AAC9D,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,yBACf,MACoC;AACpC,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,cAAc,MAAuC;AACpE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,eAAe,MAAqC;AACnE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,eAAe,MAAwC;AACtE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAsC;AACrE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,YAAY,MAAqC;AAChE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAiBO,SAAS,aAAa,MAAsC;AAClE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,YAAY,MAAqC;AAChE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,0BACf,MACqC;AACrC,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,eAAe,MAAqC;AACnE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,cAAc,MAAoC;AACjE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,cAAc,MAAuC;AACpE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAsC;AACrE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAsC;AACrE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,aAAa,MAAsC;AAClE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAsC;AACrE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,eAAe,MAAqC;AACnE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,gBAAgB,MAAsC;AACrE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,aAAa,MAAsC;AAClE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,cAAc,MAAoC;AACjE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,cAAc,MAAoC;AACjE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,cAAc,MAAuC;AACpE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,mBAAmB,MAAyC;AAC3E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAeO,SAAS,iBAAiB,MAAuC;AACvE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAiBO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAkBO,SAAS,6BACf,MACwC;AACxC,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;AAgBO,SAAS,cAAc,MAAoC;AACjE,SAAO,KAAK,SAAS,mBAAAA,QAAG,WAAW;AACpC;;;ACriCA,IAAAC,qBAAe;;;ACAf,IAAAC,qBAAe;AAEf,IAAM,CAAC,SAAS,OAAO,IAAI,mBAAAC,QAAG,kBAC5B,MAAM,GAAG,EACT,IAAI,CAAC,QAAQ,OAAO,SAAS,KAAK,EAAE,CAAC;AAEhC,SAAS,mBAAmB,OAAe,QAAQ,GAAY;AACrE,SAAO,UAAU,SAAU,YAAY,SAAS,WAAW;AAC5D;;;ADgCO,SAAS,mBAAmB,MAA4C;AAC9E,SACC,mBAAAC,QAAG,2BAA2B,IAAI,KAAK,mBAAAA,QAAG,0BAA0B,IAAI;AAE1E;AAeO,SAAS,wBACf,MACmC;AACnC,SACC,gBAAgB,IAAI,KAAK,iBAAiB,IAAI,KAAK,mBAAmB,IAAI;AAE5E;AAgBO,SAAS,sBACf,MACiC;AACjC,SAAO,mBAAAA,QAAG,yBAAyB,IAAI,KAAK,mBAAAA,QAAG,yBAAyB,IAAI;AAC7E;AAgBO,SAAS,sBACf,MACiC;AACjC,SAAO,mBAAAA,QAAG,iBAAiB,IAAI,KAAK,mBAAAA,QAAG,oBAAoB,IAAI;AAChE;AAeO,SAAS,kCACf,MAC6C;AAC7C,SAAO,mBAAAA,QAAG,sBAAsB,IAAI,KAAK,mBAAAA,QAAG,yBAAyB,IAAI;AAC1E;AAeO,SAAS,oBACf,MAC+B;AAC/B,SACC,mBAAAA,QAAG,0BAA0B,IAAI,KAAK,mBAAAA,QAAG,yBAAyB,IAAI;AAExE;AAeO,SAAS,0CACf,MACqD;AACrD,MAAI,mBAAAA,QAAG,gBAAgB,IAAI,KAAK,mBAAAA,QAAG,mBAAmB,IAAI,GAAG;AAC5D,WAAO;AAAA,EACR;AAEA,MAAI,mBAAmB,GAAG,CAAC,GAAG;AAC7B,WAAO,mBAAAA,QAAG,iBAAiB,IAAI;AAAA,EAChC;AAEA,SAAO;AACR;AAeO,SAAS,mCACf,MAC8C;AAC9C,SACC,6BAA6B,IAAI,KACjC,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,oBAAoB,IAAI;AAE7B;AAeO,SAAS,6BACf,MACwC;AACxC,SACC,mCAAmC,IAAI,KACvC,kCAAkC,IAAI;AAExC;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,mBAAAA,QAAG,uBAAuB,IAAI,KAAK,mBAAAA,QAAG,sBAAsB,IAAI;AACxE;AAeO,SAAS,YAAY,MAAqC;AAChE,SACC,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,QAAQ,IAAI,KACf,mBAAAA,QAAG,cAAc,IAAI,KACrB,mBAAAA,QAAG,sBAAsB,IAAI;AAE/B;AAeO,SAAS,iBAAiB,MAA0C;AAC1E,SAAO,cAAc,IAAI,KAAK,eAAe,IAAI;AAClD;AAgBO,SAAS,uBACf,MACkC;AAClC,SAAO,mBAAAA,QAAG,mBAAmB,IAAI,KAAK,mBAAAA,QAAG,kBAAkB,IAAI;AAChE;AAeO,SAAS,sBACf,MACiC;AACjC,SACC,wBAAwB,IAAI,KAC5B,kBAAkB,IAAI,KACtB,gBAAgB,IAAI,KACpB,kBAAkB,IAAI;AAExB;AAeO,SAAS,kBAAkB,MAA2C;AAC5E,SACC,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,iBAAiB,IAAI,KACrB,uBAAuB,IAAI;AAE7B;AAeO,SAAS,uCACf,MACkD;AAClD,SACC,uBAAuB,IAAI;AAAA,EAE3B,uBAAuB,IAAI,KAC3B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,mBAAmB,IAAI;AAE5B;AAeO,SAAS,gCACf,MAC2C;AAC3C,SACC,uCAAuC,IAAI,KAC3C,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,iBAAiB,IAAI;AAE1B;AAeO,SAAS,uBACf,MACkC;AAClC,SACC,iBAAiB,IAAI,KACrB,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,yBAAyB,IAAI;AAElC;AAeO,SAAS,uBACf,MACkC;AAClC,SAAO,mBAAAA,QAAG,aAAa,IAAI,KAAK,qCAAqC,IAAI;AAC1E;AAeO,SAAS,mCACf,MAC8C;AAC9C,SAAO,mBAAAA,QAAG,aAAa,IAAI,KAAK,uBAAuB,IAAI;AAC5D;AAeO,SAAS,qBACf,MACgC;AAChC,SAAO,mBAAAA,QAAG,iBAAiB,IAAI,KAAK,mBAAAA,QAAG,iBAAiB,IAAI;AAC7D;AAgBO,SAAS,0BACf,MACqC;AACrC,SACC,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,gBAAgB,IAAI;AAEzB;AAeO,SAAS,cAAc,MAAyC;AACtE,SACC,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,mBAAmB,IAAI;AAE5B;AAeO,SAAS,yBACf,MACsC;AACtC,SACC,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,aAAa,IAAI;AAEtB;AAeO,SAAS,eAAe,MAA0C;AACxE,SACC,yBAAyB,IAAI,KAC7B,mBAAAA,QAAG,eAAe,IAAI,KACtB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,eAAe,IAAI;AAExB;AAeO,SAAS,SAAS,MAAoC;AAC5D;AAAA;AAAA,IAEC,sBAAsB,IAAI,KAC1B,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,QAAQ,IAAI,KACf,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,aAAa,IAAI;AAAA,IAEpB,uBAAuB,IAAI,KAC3B,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,gCAAgC,IAAI,KACvC,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,cAAc,IAAI,KACrB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,iBAAiB,IAAI,KACrB,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,eAAe,IAAI,KACtB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,cAAc,IAAI,KACrB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,4BAA4B,IAAI,KACnC,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,6BAA6B,IAAI,KACpC,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,8BAA8B,IAAI,KACrC,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,eAAe,IAAI,KACtB,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,gBAAgB,IAAI;AAAA,IACtB;AACD,WAAO;AAAA,EACR;AAEA,MAAI,mBAAmB,GAAG,CAAC,KAAK,mBAAAA,QAAG,8BAA8B,IAAI,GAAG;AACvE,WAAO;AAAA,EACR;AAEA,MACC,mBAAmB,GAAG,CAAC,MACtB,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,2BAA2B,IAAI,IAClC;AACD,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAeO,SAAS,aAAa,MAAwC;AACpE,SACC,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,mBAAAA,QAAG,4BAA4B,IAAI,KACnC,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,oBAAoB,IAAI;AAE7B;AAeO,SAAS,QAAQ,MAAmC;AAC1D,SACC,uBAAuB,IAAI,KAC3B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,wBAAwB,IAAI,KAC/B,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,oBAAoB,IAAI;AAE7B;AAeO,SAAS,iBAAiB,MAA4C;AAC5E,SACC,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,wBAAwB,IAAI;AAEjC;AAeO,SAAS,eAAe,MAAwC;AACtE,MAAI,YAAY,IAAI,GAAG;AACtB,WAAO;AAAA,EACR;AAEA,MAAI,mBAAmB,GAAG,CAAC,GAAG;AAC7B,WACC,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,iBAAiB,IAAI;AAAA,EAE1B;AAEA,SAAO;AACR;AAeO,SAAS,qBACf,MACgC;AAChC,SAAO,mBAAAA,QAAG,aAAa,IAAI,KAAK,4BAA4B,IAAI;AACjE;AAeO,SAAS,2BACf,MACsC;AACtC,SACC,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,uBAAuB,IAAI;AAEhC;AAeO,SAAS,uBACf,MACkC;AAClC,SACC,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,yBAAyB,IAAI,KAChC,0BAA0B,IAAI,KAC9B,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,iBAAiB,IAAI,KACrB,cAAc,IAAI;AAEpB;AAgBO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,mBAAAA,QAAG,eAAe,IAAI,KAAK,mBAAAA,QAAG,qBAAqB,IAAI;AAC/D;AAeO,SAAS,oBACf,MAC+B;AAC/B,SACC,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,wBAAwB,IAAI,KAC/B,mBAAAA,QAAG,cAAc,IAAI;AAEvB;AAgBO,SAAS,WAAW,MAAoC;AAC9D,SACC,mBAAAA,QAAG,UAAU,IAAI,KACjB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,wBAAwB,IAAI,KAC/B,mBAAAA,QAAG,cAAc,IAAI;AAEvB;AAgBO,SAAS,uBACf,MACkC;AAClC,SACC,mBAAAA,QAAG,aAAa,IAAI,KACpB,iBAAiB,IAAI,KACrB,2BAA2B,IAAI;AAEjC;AAeO,SAAS,eAAe,MAAwC;AACtE,SACC,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,gBAAgB,IAAI,KACvB,mBAAAA,QAAG,UAAU,IAAI,KACjB,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,gCAAgC,IAAI;AAEzC;AAgBO,SAAS,aAAa,MAAsC;AAClE,SAAO,gBAAgB,IAAI,KAAK,qBAAqB,IAAI;AAC1D;AAgBO,SAAS,aAAa,MAAsC;AAClE,SAAO,mBAAAA,QAAG,aAAa,IAAI,KAAK,mBAAAA,QAAG,gBAAgB,IAAI;AACxD;AAgBO,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,mBAAAA,QAAG,aAAa,IAAI,KAAK,mBAAAA,QAAG,0BAA0B,IAAI;AAClE;AAgBO,SAAS,sBACf,MACiC;AACjC,SAAO,mBAAAA,QAAG,kBAAkB,IAAI,KAAK,mBAAAA,QAAG,eAAe,IAAI;AAC5D;AAeO,SAAS,wBACf,MACmC;AACnC,SAAO,mBAAAA,QAAG,eAAe,IAAI,KAAK,mBAAAA,QAAG,eAAe,IAAI;AACzD;AAeO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,mBAAAA,QAAG,cAAc,IAAI,KAAK,uBAAuB,IAAI;AAC7D;AAeO,SAAS,mCACf,MAC8C;AAC9C,SACC,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,8BAA8B,IAAI,KACrC,mBAAAA,QAAG,mBAAmB,IAAI;AAE5B;AAeO,SAAS,mCACf,MAC8C;AAC9C,SAAO,mBAAAA,QAAG,uBAAuB,IAAI,KAAK,mBAAAA,QAAG,0BAA0B,IAAI;AAC5E;AAeO,SAAS,wBACf,MACmC;AACnC;AAAA;AAAA,IAEC,uBAAuB,IAAI,KAC3B,mBAAAA,QAAG,uBAAuB,IAAI,KAC9B,mBAAAA,QAAG,kBAAkB,IAAI;AAAA;AAE3B;AAeO,SAAS,4BACf,MACuC;AACvC,SAAO,wBAAwB,IAAI,KAAK,kBAAkB,IAAI;AAC/D;AAeO,SAAS,sBACf,MACiC;AACjC,SACC,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,iBAAiB,IAAI;AAE1B;AAeO,SAAS,qBACf,MACgC;AAChC,SACC,mBAAAA,QAAG,eAAe,IAAI,KACtB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,eAAe,IAAI;AAExB;AAeO,SAAS,uBACf,MACkC;AAClC,SACC,mBAAAA,QAAG,2BAA2B,IAAI,KAClC,mBAAAA,QAAG,gCAAgC,IAAI,KACvC,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,4BAA4B,IAAI,KACnC,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,yBAAyB,IAAI;AAAA,EAEhC,sBAAsB,IAAI,KAC1B,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,gBAAgB,IAAI;AAEzB;AAeO,SAAS,gBAAgB,MAAyC;AACxE,SACC,gCAAgC,IAAI,KACpC,+BAA+B,IAAI;AAErC;AAeO,SAAS,qCACf,MACgD;AAChD,MACC,mBAAAA,QAAG,eAAe,IAAI,KACtB,mBAAAA,QAAG,0BAA0B,IAAI,KACjC,mBAAAA,QAAG,kBAAkB,IAAI,KACzB,mBAAAA,QAAG,0BAA0B,IAAI,GAChC;AACD,WAAO;AAAA,EACR;AAEA,MACC,mBAAmB,GAAG,CAAC,MACtB,mBAAAA,QAAG,oBAAoB,IAAI,KAAK,mBAAAA,QAAG,kBAAkB,IAAI,IACzD;AACD,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAeO,SAAS,oBACf,MAC+B;AAC/B,SAAO,mBAAAA,QAAG,oBAAoB,IAAI,KAAK,mBAAAA,QAAG,8BAA8B,IAAI;AAC7E;AAeO,SAAS,8BACf,MACyC;AACzC,SAAO,mBAAAA,QAAG,gBAAgB,IAAI,KAAK,mBAAAA,QAAG,uBAAuB,IAAI;AAClE;AAiBO,SAAS,qBACf,MACgC;AAChC,SAAO,mBAAAA,QAAG,kBAAkB,IAAI,KAAK,mBAAAA,QAAG,mBAAmB,IAAI;AAChE;AAgBO,SAAS,0BACf,MACqC;AACrC,SACC,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,YAAY,IAAI,KACnB,mBAAAA,QAAG,iBAAiB,IAAI,KACxB,mBAAAA,QAAG,sBAAsB,IAAI,KAC7B,mBAAAA,QAAG,qBAAqB,IAAI,KAC5B,mBAAAA,QAAG,oBAAoB,IAAI,KAC3B,mBAAAA,QAAG,eAAe,IAAI,KACtB,mBAAAA,QAAG,8BAA8B,IAAI,KACrC,mBAAAA,QAAG,aAAa,IAAI,KACpB,mBAAAA,QAAG,mBAAmB,IAAI,KAC1B,mBAAAA,QAAG,oBAAoB,IAAI;AAE7B;;;AFx1CO,SAAS,2BACf,MACmC;AACnC,SACC,oBAAAC,QAAG,oBAAoB,KAAK,IAAI,KAChC,oBAAAA,QAAG,aAAa,KAAK,KAAK,QAAQ,KAClC,KAAK,KAAK,SAAS,gBAAgB;AAErC;AAeO,SAAS,qBACf,MACgC;AAChC,UAAQ,KAAK,MAAM;AAAA,IAClB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR;AACC,aAAO;AAAA,EACT;AACD;AAeO,SAAS,4BACf,MACuC;AACvC,SACC,oBAAAA,QAAG,oBAAoB,IAAI,KAC3B,oBAAAA,QAAG,aAAa,KAAK,IAAI,MACxB,KAAK,SAAS,UAAa,qBAAqB,KAAK,IAAI;AAE5D;AAeO,SAAS,2BACf,MACsC;AACtC,SACC,oBAAAA,QAAG,2BAA2B,IAAI;AAAA,EAElC,uBAAuB,KAAK,UAAU;AAExC;AAuBO,SAAS,2BACf,MACmC;AACnC,SACC,UAAU,QACV,KAAK,SAAS,UACd,KAAK,SAAS,QACd,kBAAkB,KAAK,IAAe;AAExC;AAeO,SAAS,uBACf,MACkC;AAClC,SACC,oBAAAA,QAAG,oBAAoB,IAAI,KAC3B,oBAAAA,QAAG,aAAa,KAAK,IAAI,KACzB,KAAK,SAAS,UACd,gBAAgB,KAAK,IAAI;AAE3B;AAuBO,SAAS,6BACf,MACqC;AACrC,UAAQ,KAAK,MAAM;AAAA,IAClB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR;AACC,aAAO;AAAA,EACT;AACD;AAeO,SAAS,qCACf,MACgD;AAChD,SACC,oBAAAA,QAAG,2BAA2B,IAAI,KAClC,oBAAAA,QAAG,aAAa,KAAK,IAAI,KACzB,uBAAuB,KAAK,UAAU;AAExC;AAeO,SAAS,+BACf,MAC0C;AAC1C,SACC,oBAAAA,QAAG,0BAA0B,IAAI,KAAK,kBAAkB,KAAK,UAAU;AAEzE;AAeO,SAAS,gCACf,MAC2C;AAC3C,SACC,oBAAAA,QAAG,2BAA2B,IAAI,KAAK,kBAAkB,KAAK,UAAU;AAE1E;;;AInRA,IAAAC,sBAAe;AAcR,SAAS,wBAAwB,MAAwB;AAC/D,UAAQ,KAAK,MAAM;AAAA,IAClB,KAAK,oBAAAC,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,oBAAAA,QAAG,WAAW;AAElB,aAAO,oBAAAA,QAAG,iBAAiB,IAAqB;AAAA,IACjD;AACC,aAAO;AAAA,EACT;AACD;;;ACvCA,IAAAC,sBAAe;;;ACHf,IAAAC,sBAAe;AAwBR,SAAS,mBAAmB,MAAyC;AAC3E,SAAO,cAAc,MAAM,oBAAAC,QAAG,UAAU,GAAG;AAC5C;AAsBO,SAAS,uBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,OAAO;AAChD;AAsBO,SAAS,sBACf,MAC8B;AAC9B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,MAAM;AAC/C;AA4BO,SAAS,qBACf,MAC6B;AAC7B,SAAO,gBAAgB,IAAI,KAAK,KAAK,kBAAkB;AACxD;AAsBO,SAAS,wBACf,MACgC;AAChC,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,QAAQ;AACjD;AAYA,IAAM,qBACJ,oBAAAA,QAAG,UAAqC,aACzC,oBAAAA,QAAG,UAAU,MACZ,oBAAAA,QAAG,UAAU,UACb,oBAAAA,QAAG,UAAU,SACb,oBAAAA,QAAG,UAAU,SACb,oBAAAA,QAAG,UAAU,SACb,oBAAAA,QAAG,UAAU,UACb,oBAAAA,QAAG,UAAU,iBACb,oBAAAA,QAAG,UAAU,WACb,oBAAAA,QAAG,UAAU,OACb,oBAAAA,QAAG,UAAU,YACb,oBAAAA,QAAG,UAAU,OACb,oBAAAA,QAAG,UAAU,QACb,oBAAAA,QAAG,UAAU;AAcR,SAAS,gBAAgB,MAAsC;AACrE,SAAO,cAAc,MAAM,kBAAkB;AAC9C;AAsBO,SAAS,qBACf,MAC6B;AAC7B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,KAAK;AAC9C;AAwBO,SAAS,4BACf,MACoC;AACpC,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,YAAY;AACrD;AAsBO,SAAS,oBAAoB,MAA0C;AAC7E,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,IAAI;AAC7C;AAsBO,SAAS,sBACf,MAC8B;AAC9B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,MAAM;AAC/C;AAsBO,SAAS,sBACf,MAC8B;AAC9B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,MAAM;AAC/C;AAsBO,SAAS,yBACf,MACiC;AACjC,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,SAAS;AAClD;AAsBO,SAAS,uBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,OAAO;AAChD;AAsBO,SAAS,oBAAoB,MAA0C;AAC7E,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,IAAI;AAC7C;;;AChYA,IAAAC,sBAAe;;;ACAf,IAAAC,sBAAe;AAgBR,SAAS,kBAAkB,MAA2C;AAC5E,SAAO,cAAc,MAAM,oBAAAC,QAAG,UAAU,WAAW;AACpD;AAcO,SAAS,WAAW,MAAoC;AAC9D,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,IAAI;AAC7C;AAcO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,SAAS;AAClD;AAcO,SAAS,YAAY,MAAqC;AAChE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,KAAK;AAC9C;AAcO,SAAS,oBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,aAAa;AACtD;AAcO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,YAAY;AACrD;AAcO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,YAAY;AACrD;AAcO,SAAS,aAAa,MAAsC;AAClE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,MAAM;AAC/C;AAcO,SAAS,oBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,aAAa;AACtD;AAcO,SAAS,mBAAmB,MAA4C;AAC9E,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,YAAY;AACrD;AAcO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,aAAa;AACtD;AAcO,SAAS,eAAe,MAAwC;AACtE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,YAAY;AACrD;AAcO,SAAS,YAAY,MAAqC;AAChE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,KAAK;AAC9C;AAcO,SAAS,0BACf,MACqC;AACrC,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,mBAAmB;AAC5D;AAcO,SAAS,qBACf,MACgC;AAChC,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,cAAc;AACvD;;;ADzOO,SAAS,oBACf,MAC+B;AAC/B,SACC,aAAa,IAAI,KAAK,gBAAgB,MAAM,oBAAAC,QAAG,YAAY,aAAa;AAE1E;AAcO,SAAS,YAAY,MAAqC;AAChE,SAAO,aAAa,IAAI,KAAK,gBAAgB,MAAM,oBAAAA,QAAG,YAAY,KAAK;AACxE;AAcO,SAAS,gBAAgB,MAAyC;AACxE,SAAO,aAAa,IAAI,KAAK,gBAAgB,MAAM,oBAAAA,QAAG,YAAY,SAAS;AAC5E;;;AE9BO,SAAS,yBACf,MACiC;AACjC,SAAO,gBAAgB,IAAI,KAAK,gBAAgB,IAAI;AACrD;AAaO,SAAS,qBACf,MACgC;AAChC,SAAO,gBAAgB,IAAI,KAAK,YAAY,KAAK,MAAM;AACxD;;;AC9CA,IAAAC,sBAAe;AA2BR,SAAS,qBACf,MAC6B;AAC7B,SAAO,cAAc,MAAM,oBAAAC,QAAG,UAAU,cAAc;AACvD;AAcO,SAAS,oBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,aAAa;AACtD;AAuBO,SAAS,mBAAmB,MAAyC;AAC3E,SAAO,qBAAqB,IAAI,KAAK,KAAK,kBAAkB;AAC7D;AAcO,SAAS,cAAc,MAAuC;AACpE,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,OAAO;AAChD;AAcO,SAAS,oBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,aAAa;AACtD;AAcO,SAAS,oBACf,MAC+B;AAC/B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,aAAa;AACtD;AAcO,SAAS,sBACf,MACiC;AACjC,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,eAAe;AACxD;AAuBO,SAAS,kBAAkB,MAAwC;AACzE,SAAO,qBAAqB,IAAI,KAAK,KAAK,kBAAkB;AAC7D;AAsBO,SAAS,qBACf,MAC6B;AAC7B,SAAO,cAAc,MAAM,oBAAAA,QAAG,UAAU,OAAO;AAChD;;;AL7KO,SAAS,wBACf,MAC0B;AAC1B,MAAI,YAAY,IAAI,GAAG;AACtB,UAAM,aAAa,CAAC;AACpB,eAAW,WAAW,KAAK,OAAO;AACjC,iBAAW,KAAK,GAAG,wBAAwB,OAAO,CAAC;AAAA,IACpD;AAEA,WAAO;AAAA,EACR;AAEA,MAAI,mBAAmB,IAAI,GAAG;AAC7B,QAAI;AACJ,eAAW,WAAW,KAAK,OAAO;AACjC,YAAM,MAAM,wBAAwB,OAAO;AAC3C,UAAI,IAAI,WAAW,GAAG;AAErB,YAAI,eAAe,QAAW;AAC7B,iBAAO,CAAC;AAAA,QACT;AAEA,qBAAa;AAAA,MACd;AAAA,IACD;AAEA,WAAO,eAAe,SAAY,CAAC,IAAI;AAAA,EACxC;AAEA,SAAO,KAAK,kBAAkB;AAC/B;AAaO,SAAS,kBACf,MACA,MACwB;AACxB,MAAI,CAAE,KAAgB,WAAW,IAAI,GAAG;AACvC,WAAO,KAAK,YAAY,IAAc;AAAA,EACvC;AAEA,SAAO,KAAK,cAAc,EAAE,KAAK,CAAC,MAAM,EAAE,gBAAgB,IAAI;AAC/D;AAaO,SAAS,iCACf,MACA,qBACA,aACwB;AACxB,QAAM,SAAS,QAAQ;AAEvB,aAAW,QAAQ,KAAK,cAAc,GAAG;AACxC,QAAI,CAAC,KAAK,KAAK,WAAW,MAAM,GAAG;AAClC;AAAA,IACD;AAEA,UAAM,cAAc,KAAK,oBAAoB,KAAK,gBAAgB,EAAG,CAAC;AACtE,QACC,CAAC,2BAA2B,WAAW,KACvC,YAAY,SAAS,UACrB,CAAC,oBAAAC,QAAG,uBAAuB,YAAY,IAAI,GAC1C;AACD;AAAA,IACD;AAEA,UAAM,eAAe,YAAY;AAAA,MAChC,YAAY,kBAAkB,YAAY,KAAK,UAAU;AAAA,IAC1D,EAAE;AAEF,QACC,KAAK,gBACL;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,IACD,GACC;AACD,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAKA,SAAS,iCACR,aACA,mBACA,YACC;AACD,QAAM,cACL,qBACA,YACE;AAAA,IACA;AAAA;AAAA,IAEC,kBAA0B;AAAA,EAC5B,EACC,YAAY,UAAU;AACzB,QAAM,kBACL,eACA,YAAY;AAAA,IACX;AAAA;AAAA,IAEC,YAAoB;AAAA,EACtB;AACD,MAAI,mBAAmB,qBAAqB,eAAe,GAAG;AAC7D,WAAO,gBAAgB;AAAA,EACxB;AAEA,SAAQ,QAAQ;AACjB;;;AMzJA,IAAAC,sBAAe;;;ACAf,IAAAC,sBAAe;AAYR,SAAS,mCACf,MACU;AACV,SACC,KAAK,UAAU,WAAW,KAC1B,uBAAuB,KAAK,UAAU,CAAC,CAAC,KACxC,6BAA6B,KAAK,UAAU,CAAC,CAAC,KAC9C,oBAAAC,QAAG,2BAA2B,KAAK,UAAU,KAC7C,KAAK,WAAW,KAAK,gBAAgB,oBACrC,oBAAAA,QAAG,aAAa,KAAK,WAAW,UAAU,KAC1C,KAAK,WAAW,WAAW,gBAAgB;AAE7C;AAMO,SAAS,iBAAiB,MAA8B;AAC9D,MAAI,UAAmB;AACvB,SAAO,MAAM;AACZ,UAAM,SAAS,QAAQ;AACvB;AAAO,cAAQ,OAAO,MAAM;AAAA,QAC3B,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO,2BAA2B,MAAgC;AAAA,QACnE,KAAK,oBAAAA,QAAG,WAAW;AAClB,cAAI,QAAQ,SAAS,oBAAAA,QAAG,WAAW,gBAAgB;AAClD,mBAAO;AAAA,UACR;AAEA,kBAAS,OAAoC,UAAU;AAAA,YACtD,KAAK,oBAAAA,QAAG,WAAW;AAAA,YACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,wBAAU;AACV,oBAAM;AAAA,YACP;AACC,qBAAO;AAAA,UACT;AAAA,QAED,KAAK,oBAAAA,QAAG,WAAW;AAClB,cAAK,OAAiC,gBAAgB,SAAS;AAC9D,mBAAO;AAAA,UACR;AAEA,oBAAU,OAAO;AACjB;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,oBAAU,OAAO;AACjB;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,oBAAU;AACV;AAAA,QACD;AACC,iBAAO;AAAA,MACT;AAAA,EACD;AACD;;;ADrCO,SAAS,YAAY,MAAwB;AACnD,MACC;AAAA,IACC;AAAA,IACA,oBAAAC,QAAG,UAAU,YAAY,oBAAAA,QAAG,UAAU,OAAO,oBAAAA,QAAG,UAAU;AAAA,EAC3D,GACC;AACD,WAAO;AAAA,EACR;AAEA,MAAI,KAAK,UAAU,GAAG;AACrB,WAAO,CAAC,KAAK;AAAA,EACd;AAEA,SAAO,mBAAmB,IAAI;AAC/B;AAgBO,SAAS,sBAAsB,MAA0B;AAC/D,SAAO,mBAAmB,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI;AACrD;AAkBO,SAAS,UAAU,MAA0B;AACnD,SAAO,mBAAmB,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC1E;AAEA,SAAS,+BACR,MACA,MACA,aACC;AACD,QAAMC,aAAY,mBAAmB,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC/D,SAAOA,WAAU,KAAK,CAAC,YAAqB;AAC3C,UAAM,OAAO,kBAAkB,SAAS,IAAI;AAC5C,QAAI,SAAS,QAAW;AACvB,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,QAAQ,oBAAAD,QAAG,YAAY,WAAW;AAC1C,UACC,mBAAmB,KAAK,IAAc,KACtC,qBAAqB,OAAO,GAC3B;AACD,eAAO,QAAQ,OAAO;AAAA,MACvB;AAEA,cAAQ,iCAAiC,SAAS,MAAM,WAAW,GAAG;AAAA,QACrE,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AAAA,MAED;AAAA,IACD;AAEA,WAAO,CAAC;AAAA,KAGN,gBAAgB,MAAM,oBAAAA,QAAG,YAAY,WAAW;AAAA,IAEhD,6BAA6B,MAAM,WAAW;AAAA,EAGjD,CAAC;AACF;AAEA,SAAS,iCACR,MACA,MACA,aACsB;AACtB,MAAI,CAAC,aAAa,IAAI,KAAK,CAAC,gBAAgB,MAAM,oBAAAA,QAAG,YAAY,MAAM,GAAG;AACzE;AAAA,EACD;AAEA,QAAM,cAAc,KAAK,OAAO,aAAc,CAAC;AAE/C,MACC,YAAY,kBAAkB,UAC9B,CAAC,aAAa,KAAK,IAAc,GAChC;AACD,WAAO,YAAY,cAAc,SAAS,oBAAAA,QAAG,WAAW;AAAA,EACzD;AAEA,QAAM,EAAE,cAAc,IAAI;AAE1B,SACC,iBAAiB,yBAAyB,eAAe,MAAM,WAAW;AAE5E;AAEA,SAAS,WACR,aACA,OACA,MACU;AACV,MAAI,OAA4B,YAAY;AAAA,IAC3C,YAAY,0BAA0B,OAAO,IAAI;AAAA,EAClD;AACA,MAAK,MAAM,iBAA6C,gBAAgB;AAEvE,WAAO,KAAK,mBAAmB;AAC/B,QAAI,SAAS,QAAW;AACvB,aAAO;AAAA,IACR;AAAA,EACD;AAEA,aAAW,WAAW,eAAe,IAAI,GAAG;AAC3C,QAAI,QAAQ,kBAAkB,EAAE,WAAW,GAAG;AAC7C,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAgBO,SAAS,yBACf,MACA,MACA,aACU;AACV,MAAI,eAAe;AACnB,MAAI,wBAAwB;AAC5B,aAAW,WAAW,eAAe,IAAI,GAAG;AAC3C,QAAI,kBAAkB,SAAS,IAAI,MAAM,QAAW;AAEnD,YAAM,SACJ,sBAAsB,IAAI,IACxB,YAAY,mBAAmB,SAAS,oBAAAA,QAAG,UAAU,MAAM,IAC3D,WACH,YAAY,mBAAmB,SAAS,oBAAAA,QAAG,UAAU,MAAM;AAC5D,UAAI,OAAO,YAAY;AACtB,YAAI,cAAc;AACjB,iBAAO;AAAA,QACR;AAEA,gCAAwB;AAAA,MACzB;AAAA,IACD,WACC,yBACA,+BAA+B,SAAS,MAAM,WAAW,GACxD;AACD,aAAO;AAAA,IACR,OAAO;AACN,qBAAe;AAAA,IAChB;AAAA,EACD;AAEA,SAAO;AACR;AAeA,SAAS,gCACR,MACA,aACC;AACD,MAAI,CAAC,mCAAmC,IAAI,GAAG;AAC9C,WAAO;AAAA,EACR;AAEA,QAAM,iBAAiB,YAAY,kBAAkB,KAAK,UAAU,CAAC,CAAC;AACtE,MAAI,eAAe,YAAY,OAAO,MAAM,QAAW;AACtD,WAAO,eAAe,YAAY,KAAK,MAAM;AAAA,EAC9C;AAEA,QAAM,eAAe,eAAe,YAAY,UAAU;AAC1D,MAAI,iBAAiB,QAAW;AAC/B,WAAO;AAAA,EACR;AAEA,QAAM,eACL,aAAa,qBAAqB,UAClC,oBAAAA,QAAG,qBAAqB,aAAa,gBAAgB,IAClD,YAAY,kBAAkB,aAAa,iBAAiB,WAAW,IACvE,YAAY,0BAA0B,cAAc,KAAK,UAAU,CAAC,CAAC;AACzE,SAAO,mBAAmB,YAAY;AACvC;AAmDO,SAAS,eACf,aACA,MACA,OAAO,YAAY,kBAAkB,IAAI,GAC/B;AACV,aAAW,YAAY,eAAe,YAAY,gBAAgB,IAAI,CAAC,GAAG;AACzE,UAAM,OAAO,SAAS,YAAY,MAAM;AACxC,QAAI,SAAS,QAAW;AACvB;AAAA,IACD;AAEA,UAAM,WAAW,YAAY,0BAA0B,MAAM,IAAI;AACjE,eAAW,eAAe,eAAe,QAAQ,GAAG;AACnD,iBAAW,aAAa,YAAY,kBAAkB,GAAG;AACxD,YACC,UAAU,WAAW,WAAW,KAChC,WAAW,aAAa,UAAU,WAAW,CAAC,GAAG,IAAI,GACpD;AACD,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAeO,SAAS,6BACf,QACA,aACU;AACV,SAAO,CAAC,GACN,OAAO,QAAQ,oBAAAA,QAAG,YAAY,cAAc,oBAAAA,QAAG,YAAY,eAC5D,OAAO,cAAc;AAAA,IACpB,CAAC,SACA,kBAAkB,MAAM,oBAAAA,QAAG,cAAc,QAAQ,KAChD,oBAAAA,QAAG,sBAAsB,IAAI,KAC7B,cAAc,KAAK,QAAQ,oBAAAA,QAAG,UAAU,KAAK,KAC7C,oBAAAA,QAAG,iBAAiB,IAAI,KACxB,gCAAgC,MAAM,WAAW,KAClD,oBAAAA,QAAG,aAAa,IAAI,MAClB,oBAAAA,QAAG,qBAAqB,IAAI,KAC7B,oBAAAA,QAAG,8BAA8B,IAAI,MACrC,iBAAiB,KAAK,MAAM;AAAA,EAC/B;AAEF;AAgBO,SAAS,eAAe,MAA0B;AACxD,SAAO,YAAY,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI;AAC9C;;;AE3YA,IAAAE,sBAAe;;;ACAf,IAAAC,sBAAe;AA+CR,SAAS,qBAAqB,MAA8B;AAClE,UAAQ,KAAK,MAAM;AAAA,IAClB,KAAK,oBAAAC,QAAG,WAAW,OAAO;AACzB,YAAM,SAAS,KAAK;AACpB,aAAO,OAAO,SAAS,oBAAAA,QAAG,WAAW;AAAA,OAEnC,OAAO,SAAS,oBAAAA,QAAG,WAAW;AAAA;AAAA,MAG9B,CAAC,wBAAwB,MAAM,KAC9B,gBACA;AAAA,IACJ;AAAA,IAEA,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR;AACC,aAAO;AAAA,EACT;AACD;;;ACvEA,IAAAC,sBAAe;;;ACHf,IAAAC,sBAAe;AAKR,SAAS,wBACf,MAC4B;AAC5B,SAAO,6BAA6B,oBAAAC,UACjC,oBAAAA,QAAG,wBAAwB,IAAI;AAAA;AAAA,IAEhC,KAAK;AAAA;AACR;AAKO,SAAS,kBAAkB,MAAyC;AAC1E,SAAO,uBAAuB,oBAAAA,UAC3B,oBAAAA,QAAG,kBAAkB,IAAI,IACzB,gBAAgB;AACpB;AASO,SAAS,cACf,MACsC;AACtC,SAAO,mBAAmB,oBAAAA,UACvB,oBAAAA,QAAG,cAAc,IAAI,IACpB,KAA4B;AACjC;;;ADjBO,IAAK,oBAAL,kBAAKC,uBAAL;AACN,EAAAA,sCAAA,YAAS,KAAT;AACA,EAAAA,sCAAA,eAAY,KAAZ;AACA,EAAAA,sCAAA,UAAO,KAAP;AACA,EAAAA,sCAAA,WAAQ,KAAR;AAEA,EAAAA,sCAAA,SAAM,KAAN;AANW,SAAAA;AAAA,GAAA;AASL,SAAS,qBACf,MACgC;AAChC,UAAQ,KAAK,OAAO,MAAM;AAAA,IACzB,KAAK,oBAAAC,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO,eAAyB;AAAA,IACjC,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO,cAAwB;AAAA,IAChC,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAQ,KAAK,OACX,SAAS,OACR,cAAwB,iBACxB;AAAA,IACJ,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IACR,KAAK,oBAAAA,QAAG,WAAW;AAClB,UACC,KAAK,OAAO,OAAO,SAAS,oBAAAA,QAAG,WAAW,kBAC1C,wBAAwB,IAAI,MAAM,oBAAAA,QAAG,WAAW,aAC/C;AACD;AAAA,MACD;AAAA,IAGD,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAQ,KAAK,OAAsC,SAAS,OACzD,gBACA;AAAA,IACJ,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,EACT;AACD;;;AEnEA,IAAAC,sBAAe;AAIf,SAAS,kBAAkB,MAAqB;AAC/C,SAAO,KAAK,SAAS,oBAAAC,QAAG,WAAW,yBAAyB;AAC3D,WAAQ,KAAoC;AAAA,EAC7C;AAEA,SAAO;AACR;AAEO,SAAS,gBACf,cACqB;AACrB,MAAI,aAAa,SAAS,oBAAAA,QAAG,WAAW,sBAAsB;AAC7D,UAAM,aAAa,kBAAkB,aAAa,UAAU;AAC5D,QAAI,oBAAAA,QAAG,wBAAwB,UAAU,GAAG;AAC3C,UAAI,SAAS;AACb,cAAQ,WAAW,UAAU;AAAA,QAC5B,KAAK,oBAAAA,QAAG,WAAW;AAClB,mBAAS;AAAA,QAEV,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO,oBAAAA,QAAG,iBAAiB,WAAW,OAAO,IAC1C,GAAG,SAAS,MAAM,EAAE,GAAG,WAAW,QAAQ,IAAI,KAC9C,oBAAAA,QAAG,gBAAgB,WAAW,OAAO,IACpC,GAAG,SAAS,MAAM,EAAE,GAAG,WAAW,QAAQ,KAAK,MAAM,GAAG,EAAE,CAAC,KAC3D;AAAA,QACL;AACC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,oBAAAA,QAAG,gBAAgB,UAAU,GAAG;AACnC,aAAO,WAAW,KAAK,MAAM,GAAG,EAAE;AAAA,IACnC;AAEA,QAAI,6BAA6B,UAAU,GAAG;AAC7C,aAAO,WAAW;AAAA,IACnB;AAEA;AAAA,EACD;AAEA,SAAO,aAAa,SAAS,oBAAAA,QAAG,WAAW,oBACxC,SACA,aAAa;AACjB;;;AChDA,IAAAC,sBAAe;AAOR,IAAK,cAAL,kBAAKC,iBAAL;AACN,EAAAA,0BAAA,eAAY,KAAZ;AACA,EAAAA,0BAAA,UAAO,KAAP;AACA,EAAAA,0BAAA,eAAY,KAAZ;AACA,EAAAA,0BAAA,WAAQ,KAAR;AACA,EAAAA,0BAAA,sBAAmB,KAAnB;AAEA,EAAAA,0BAAA,SAAM,KAAN;AAPW,SAAAA;AAAA,GAAA;AAWL,SAAS,eAAe,MAA8C;AAC5E,QAAM,SAAS,KAAK;AACpB,UAAQ,OAAO,MAAM;AAAA,IACpB,KAAK,oBAAAC,QAAG,WAAW;AAClB,aAAO,wBAAwB,IAAI,MAAM,oBAAAA,QAAG,WAAW,eACpD,eACA;AAAA,IACJ,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAQ,OAAO,OAA6B,UAC3C,oBAAAA,QAAG,WAAW,qBACd,OAAO,OAAO,OAAO,SAAS,oBAAAA,QAAG,WAAW,uBAC1C,eACA;AAAA,IACJ,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO,2BAA+B;AAAA,IACvC,KAAK,oBAAAA,QAAG,WAAW;AAClB,UAAK,OAA4B,SAAS,MAAM;AAC/C,YACC,oBAAoB,MAA0B,EAAE,SAChD,oBAAAA,QAAG,WAAW,WACb;AACD,iBAAO,oBAAwB;AAAA,QAChC;AAEA,eAAO;AAAA,MACR;AAEA;AAAA,IACD,KAAK,oBAAAA,QAAG,WAAW;AAElB,UACE,OAA8B,iBAAiB,UAC/C,OAA8B,iBAAiB,MAC/C;AACD,eAAO;AAAA,MACR;AAEA;AAAA,IACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,aAAO;AAAA,IAER,KAAK,oBAAAA,QAAG,WAAW;AAClB,UAAK,OAA6B,gBAAgB,MAAM;AACvD,eAAO;AAAA,MACR;AAEA;AAAA,IACD,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,UAAK,OAA+B,SAAS,MAAM;AAClD,eAAO;AAAA,MACR;AAEA;AAAA,IACD,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,IACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB;AAAA,IACD;AACC,aAAO;AAAA,EACT;AACD;AAEA,SAAS,oBAAoB,MAAqB;AACjD,MAAI,SAAS,KAAK;AAClB,SAAO,OAAO,SAAS,oBAAAA,QAAG,WAAW,eAAe;AACnD,aAAS,OAAO;AAAA,EACjB;AAEA,SAAO;AACR;;;ACtHA;AAkBA,IAAe,gBAAf,MAA8C;AAAA,EAO7C,YAAsB,QAAiB;AAAjB;AANtB,oCAAkD;AAClD,SAAU,kBACT;AACD,SAAU,OAAgB,CAAC;AAC3B,SAAU,YAAY,oBAAI,IAA+B;AAAA,EAEjB;AAAA,EAExC,OAAO,KAAkB;AACxB,SAAK,KAAK,KAAK,GAAG;AAAA,EACnB;AAAA;AAAA,EAGU,eAAe,MAAmB;AAAA,EAAC;AAAA,EAE7C,YACC,YACA,MACA,UACA,UACA,QACO;AACP,UAAM,YAAY,KAAK,oBAAoB,QAAQ,EAAE,aAAa;AAClE,UAAM,cAA+B;AAAA,MACpC,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACD;AACA,UAAM,WAAW,UAAU,IAAI,UAAU;AACzC,QAAI,aAAa,QAAW;AAC3B,gBAAU,IAAI,YAAY;AAAA,QACzB,cAAc,CAAC,WAAW;AAAA,QAC1B;AAAA,QACA,MAAM,CAAC;AAAA,MACR,CAAC;AAAA,IACF,OAAO;AACN,eAAS,UAAU;AACnB,eAAS,aAAa,KAAK,WAAW;AAAA,IACvC;AAAA,EACD;AAAA,EAEU,SAAS,KAAY,YAAY,KAAK,WAAoB;AACnE,UAAM,WAAW,UAAU,IAAI,IAAI,SAAS,IAAI;AAChD,QAAI,aAAa,WAAc,SAAS,SAAS,IAAI,YAAY,GAAG;AACnE,aAAO;AAAA,IACR;AAEA,aAAS,KAAK,KAAK,GAAG;AACtB,WAAO;AAAA,EACR;AAAA,EAEU,YAAkB;AAC3B,eAAW,OAAO,KAAK,MAAM;AAC5B,UAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,aAAK,eAAe,GAAG;AAAA,MACxB;AAAA,IACD;AAEA,SAAK,OAAO,CAAC;AAAA,EACd;AAAA,EAEA,uBAAuB,MAAc,WAA+B;AACnE,QAAI;AACJ,QAAI,mBAAK,iBAAgB,QAAW;AACnC,yBAAK,aAAc,oBAAI,IAAI;AAAA,IAC5B,OAAO;AACN,cAAQ,mBAAK,aAAY,IAAI,IAAI;AAAA,IAClC;AAEA,QAAI,UAAU,QAAW;AACxB,cAAQ,IAAI,UAAU,IAAI;AAC1B,yBAAK,aAAY,IAAI,MAAM,KAAK;AAAA,IACjC;AAEA,WAAO;AAAA,EACR;AAAA;AAAA,EAEA,4BACC,MACA,WACA,SACA,oBACiB;AACjB,QAAI;AACJ,QAAI,KAAK,oBAAoB,QAAW;AACvC,WAAK,kBAAkB,oBAAI,IAAI;AAAA,IAChC,OAAO;AACN,cAAQ,KAAK,gBAAgB,IAAI,IAAI;AAAA,IACtC;AAEA,QAAI,UAAU,QAAW;AACxB,cAAQ,IAAI,eAAe,SAAS,oBAAoB,IAAI;AAC5D,WAAK,gBAAgB,IAAI,MAAM,KAAK;AAAA,IACrC,OAAO;AACN,YAAM,QAAQ,SAAS,kBAAkB;AAAA,IAC1C;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,IAAI,IAA6B;AAChC,QAAI,KAAK,oBAAoB,QAAW;AACvC,WAAK,gBAAgB,QAAQ,CAAC,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,IACzD;AAEA,SAAK,kBAAkB,mBAAK,aAAc;AAC1C,SAAK,UAAU;AACf,SAAK,UAAU,QAAQ,CAAC,aAAa;AACpC,iBAAW,eAAe,SAAS,cAAc;AAChD,cAAM,SAAoB;AAAA,UACzB,cAAc,CAAC;AAAA,UACf,QAAQ,YAAY;AAAA,UACpB,UAAU,YAAY;AAAA,UACtB,eAAe,KAAK;AAAA,UACpB,MAAM,CAAC;AAAA,QACR;AACA,mBAAW,SAAS,SAAS,cAAc;AAC1C,cAAI,MAAM,SAAS,YAAY,QAAQ;AACtC,mBAAO,aAAa,KAAK,MAAM,WAA4B;AAAA,UAC5D;AAAA,QACD;AAEA,mBAAW,OAAO,SAAS,MAAM;AAChC,cAAI,IAAI,SAAS,YAAY,QAAQ;AACpC,mBAAO,KAAK,KAAK,GAAG;AAAA,UACrB;AAAA,QACD;AAEA,WAAG,QAAQ,YAAY,aAA8B,IAAI;AAAA,MAC1D;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,mBAA0B;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,eAA+C;AAC9C,WAAO,KAAK;AAAA,EACb;AAAA;AAAA,EAGA,aAAaC,QAA4B;AAAA,EAAC;AAG3C;AAjJC;AAmJM,IAAM,eAAN,cAA2B,cAAc;AAAA,EAC/C,YACW,QACA,UACT;AACD,UAAM,KAAK;AAHD;AACA;AAAA,EAGX;AAAA,EAEU,eAAe,KAAkB;AAC1C,WAAO,KAAK,OAAO,OAAO,KAAK,IAAI;AAAA,EACpC;AAAA,EAEA,oBAAoB,UAAwC;AAC3D,WAAO,KAAK,WAAW,WACpB,OACA,KAAK,OAAO,oBAAoB,QAAQ;AAAA,EAC5C;AACD;AAEO,IAAM,YAAN,cAAwB,aAAa;AAAA,EAC3C,YAAY,QAAe;AAC1B,UAAM,wBAA8B;AAAA,EACrC;AAAA,EAEA,MAAY;AACX,SAAK,UAAU;AAAA,EAChB;AACD;AAjMA;AAmMO,IAAM,YAAN,cAAwB,cAAc;AAAA,EAK5C,YAAY,WAAoB,QAAiB;AAChD,UAAM,MAAM;AALb;AACA,iCAAiC;AACjC,oCAAc,IAAI,aAAa,sBAA4B;AAI1D,uBAAK,YAAa;AAAA,EACnB;AAAA,EAEA,OAAO,KAAY,QAAsB;AACxC,QAAI,WAAW,mBAAK,cAAa;AAChC,aAAO,MAAM,OAAO,GAAG;AAAA,IACxB;AAEA,WAAO,mBAAK,aAAY,OAAO,GAAG;AAAA,EACnC;AAAA,EAEA,YACC,YACA,MACA,UACA,UACA,QACO;AACP,QAAI,yBAAmC;AACtC,aAAO,MAAM,YAAY,YAAY,MAAM,UAAU,UAAU,MAAM;AAAA,IACtE;AAEA,WAAO,mBAAK,aAAY;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,IAAI,IAA6B;AAChC,uBAAK,aAAY,IAAI,CAAC,OAAO,QAAQ;AACpC,YAAM,aACL,mBAAK,eACJ,mBAAK,cAAa,UAAa,mBAAK,UAAS,SAAS,IAAI,IAAI;AAChE,YAAM,gBAAgB,KAAK;AAC3B,aAAO,GAAG,OAAO,KAAK,IAAI;AAAA,IAC3B,CAAC;AACD,WAAO,MAAM,IAAI,CAAC,OAAO,KAAK,UAAU;AACvC,YAAM,aACL,UAAU,QACV,mBAAK,cAAa,UAClB,mBAAK,UAAS,SAAS,IAAI,IAAI;AAChC,aAAO,GAAG,OAAO,KAAK,KAAK;AAAA,IAC5B,CAAC;AAAA,EACF;AAAA,EAEA,sBAA4B;AAC3B,WAAO;AAAA,EACR;AAAA,EAEA,aAAa,IAAyB;AACrC,QAAI,mBAAK,cAAa,QAAW;AAChC,yBAAK,UAAW,CAAC,GAAG,IAAI;AAAA,IACzB,OAAO;AACN,yBAAK,UAAS,KAAK,GAAG,IAAI;AAAA,IAC3B;AAAA,EACD;AACD;AAjEC;AACA;AACA;AAtMD,cAAAC,WAAA,YAAAC;AAuQO,IAAM,iBAAN,cAA6B,aAAa;AAAA,EAMhD,YAAY,SAAkB,WAAoB,QAAe;AAChE,UAAM,wBAA8B;AANrC;AACA,uBAAAD,WAAoC;AACpC;AACA,uBAAAC,cAAc,IAAI,aAAa,sBAA4B;AAI1D,uBAAK,UAAW;AAChB,uBAAK,YAAa;AAAA,EACnB;AAAA,EAEA,OAAO,KAAY,QAAsB;AACxC,QAAI,WAAW,mBAAKA,eAAa;AAChC,aAAO,mBAAKA,cAAY,OAAO,GAAG;AAAA,IACnC;AAEA,SAAK,KAAK,KAAK,GAAG;AAAA,EACnB;AAAA,EAEA,uBAAuB,MAAc,UAA8B;AAClE,QAAI,CAAC,aAAa,CAAC,mBAAK,aAAY,mBAAK,cAAa;AACrD,aAAO,mBAAKA,cAAY,uBAAuB,MAAM,QAAQ;AAAA,IAC9D;AAEA,WAAO,MAAM,uBAAuB,MAAM,QAAQ;AAAA,EACnD;AAAA,EAEA,4BACC,MACA,UACA,SACA,oBACiB;AACjB,QAAI,CAAC,aAAa,CAAC,mBAAK,aAAY,mBAAK,cAAa;AACrD,aAAO,mBAAKA,cAAY;AAAA,QACvB;AAAA,QACA;AAAA,QACA,WAAW,mBAAK;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAEA,WAAO,MAAM;AAAA,MACZ;AAAA,MACA;AAAA,MACA,WAAW,mBAAK;AAAA,MAChB;AAAA,IACD;AAAA,EACD;AAAA,EAEA,IAAI,IAA6B;AAChC,uBAAKA,cAAY,IAAI,CAAC,UAAU,KAAK,UAAU;AAC9C,UACC,UAAU,mBAAKA,iBACd,CAAC,SAAS,aACT,CAAC,mBAAK,aACL,mBAAKD,eAAa,UAAa,CAAC,mBAAKA,WAAS,IAAI,IAAI,IAAI,IAC5D;AACD,eAAO,GAAG,UAAU,KAAK,KAAK;AAAA,MAC/B;AAEA,YAAM,eAAe,KAAK,UAAU,IAAI,IAAI,IAAI;AAChD,UAAI,iBAAiB,QAAW;AAC/B,aAAK,UAAU,IAAI,IAAI,MAAM;AAAA,UAC5B,cAAc,SAAS,aAAa,IAAI,cAAc;AAAA,UACtD,QAAQ,SAAS;AAAA,UACjB,MAAM,CAAC,GAAG,SAAS,IAAI;AAAA,QACxB,CAAC;AAAA,MACF,OAAO;AACN;AAAO,qBAAW,eAAe,SAAS,cAAc;AACvD,uBAAW,YAAY,aAAa,cAAc;AACjD,kBAAI,SAAS,gBAAgB,aAAa;AACzC,yBAAS;AAAA,cACV;AAEA,2BAAa,aAAa,KAAK,eAAe,WAAW,CAAC;AAAA,YAC3D;AAAA,UACD;AAEA,qBAAa,UAAU,SAAS;AAChC,mBAAW,OAAO,SAAS,MAAM;AAChC,cAAI,aAAa,KAAK,SAAS,GAAG,GAAG;AACpC;AAAA,UACD;AAEA,uBAAa,KAAK,KAAK,GAAG;AAAA,QAC3B;AAAA,MACD;AAAA,IACD,CAAC;AACD,SAAK,UAAU;AACf,uBAAKC,cAAc,IAAI,aAAa,sBAA4B;AAAA,EACjE;AAAA,EAEA,OAAO,IAA6B;AACnC,WAAO,MAAM,IAAI,EAAE;AAAA,EACpB;AAAA,EAEA,sBAA6B;AAC5B,WAAO,mBAAKA;AAAA,EACb;AAAA,EAEA,aAAa,MAA2B;AACvC,QAAI,mBAAKD,eAAa,QAAW;AAChC,yBAAKA,WAAW,oBAAI,IAAI;AAAA,IACzB;AAEA,uBAAKA,WAAS,IAAI,KAAK,IAAI;AAAA,EAC5B;AAAA,EAEA,QAAQ,SAAkB,WAA0B;AACnD,uBAAK,UAAW;AAChB,uBAAK,YAAa;AAAA,EACnB;AACD;AAjHC;AACAA,YAAA;AACA;AACAC,eAAA;AAgHD,SAAS,eAAe,aAA6C;AACpE,SAAO;AAAA,IACN;AAAA,IACA,QAAQ,qBAAqB,WAAW;AAAA,IACxC,UAAU;AAAA,EACX;AACD;AAEO,IAAM,gBAAN,cAA4B,aAAa;AAAA,EAC/C,YAAY,QAAe;AAC1B,UAAM,wBAA8B;AAAA,EACrC;AAAA,EAEA,YAAkB;AACjB,SAAK,UAAU;AAAA,EAChB;AACD;AA3YA;AA6YA,IAAe,+BAAf,cAEU,aAAa;AAAA,EAItB,YAAY,MAAqB,QAA2B,QAAe;AAC1E,UAAM,wBAA8B;AAJrC;AAEA;AAGC,uBAAK,OAAQ;AACb,uBAAK,SAAU;AAAA,EAChB;AAAA,EAEA,OAAO,KAAY,QAAsB;AACxC,QAAI,WAAW,KAAK,YAAY;AAC/B,aAAO,KAAK,WAAW,OAAO,GAAG;AAAA,IAClC;AAEA,QAAI,IAAI,SAAS,mBAAK,YAAW,IAAI,SAAS,SAAS,mBAAK,OAAM,MAAM;AACvE,WAAK,KAAK,KAAK,GAAG;AAAA,IACnB,OAAO;AACN,aAAO,KAAK,OAAO,OAAO,KAAK,IAAI;AAAA,IACpC;AAAA,EACD;AAAA,EAEA,IAAI,IAA6B;AAChC,SAAK,WAAW,IAAI,EAAE;AACtB,WAAO;AAAA,MACN;AAAA,QACC,cAAc,CAAC,mBAAK,MAAK;AAAA,QACzB,QAAQ,mBAAK;AAAA,QACb,UAAU;AAAA,QACV,eAAe;AAAA,QACf,MAAM,KAAK;AAAA,MACZ;AAAA,MACA,mBAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AAAA,EAEA,sBAAkC;AACjC,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,mBAA+B;AAC9B,WAAO,KAAK;AAAA,EACb;AAGD;AA7CC;AAEA;AA6CM,IAAM,0BAAN,cAAsC,6BAA4C;AAAA,EAGxF,YAAY,MAAqB,QAAe;AAC/C,UAAM,qBAA+B,MAAM;AAH5C,SAAU,aAAa,IAAI,cAAc,IAAI;AAAA,EAI7C;AAAA,EAEA,YAAkB;AACjB,WAAO,KAAK,WAAW,UAAU;AAAA,EAClC;AACD;AAzcA;AA2cO,IAAM,aAAN,cAAyB,aAAa;AAAA,EAG5C,YAAY,eAAsB,QAAe;AAChD,UAAM,qBAA2B;AAHlC;AAIC,uBAAK,gBAAiB;AAAA,EACvB;AAAA,EAEA,mBAA0B;AACzB,WAAO,mBAAK;AAAA,EACb;AACD;AAVC;AAYM,IAAM,uBAAN,cAAmC,6BAA2C;AAAA,EAGpF,YAAY,MAAqB,QAAe;AAC/C,UAAM,oCAAwD,MAAM;AAHrE,SAAU,aAAa,IAAI,aAAa,sBAA4B;AAAA,EAIpE;AACD;AA9dA;AAueO,IAAM,uBAAN,cAAmC,aAAa;AAAA,EAGtD,YAAY,QAAe;AAC1B,UAAM,+BAAqC;AAH5C,+BAAS;AAAA,EAIT;AAAA,EAEA,OAAO,KAAkB;AACxB,QAAI,mBAAK,YAAW,kBAAoC;AACvD,aAAO,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IAC/B;AAEA,WAAO,KAAK,OAAO,OAAO,KAAK,IAAI;AAAA,EACpC;AAAA,EAEA,YAAY,UAA2C;AACtD,uBAAK,QAAS;AAAA,EACf;AACD;AAjBC;;;ANxeD;AAmCO,IAAM,cAAN,MAAkB;AAAA,EAAlB;AAIN;AA8BA;AAkBA;AAqBA;AAwCA;AA8CA;AA9JA,gCAAU,oBAAI,IAA8B;AAC5C;AAAA;AAAA,EA0KA,SAAS,YAA0D;AAClE,UAAM,mBAAmB,CAAC,UAAqB,QAAuB;AACrE,yBAAK,SAAQ,IAAI,KAAK,QAAQ;AAAA,IAC/B;AAEA,UAAM,WAAW,oBAAAC,QAAG,iBAAiB,UAAU;AAC/C,uBAAK,QAAS,IAAI;AAAA,MACjB,WAAW,qBACV,YACA,CAAC,wBAAwB,UAAU;AAAA,MACpC,CAAC;AAAA,IACF;AACA,UAAM,KAAK,CAAC,SAAwB;AACnC,UAAI,qBAAqB,IAAI,GAAG;AAC/B,eAAO;AAAA,UACN;AAAA,UACA,IAAI,WAAW,mBAAK,QAAO,iBAAiB,GAAG,mBAAK,OAAM;AAAA,UAC1D;AAAA,QACD;AAAA,MACD;AAEA,cAAQ,KAAK,MAAM;AAAA,QAClB,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO;AAAA,YACN;AAAA,YACC,KAA4B,SAAS,SACnC,IAAI;AAAA,cACH,KAA4B;AAAA,cAC7B,mBAAK;AAAA,YACN,IACC,IAAI,aAAa,mBAAK,yBAA8B;AAAA,UACxD;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,gCAAK,0CAAL,WACC,MACA;AAGD,iBAAO;AAAA,YACN;AAAA,YACA,IAAI,aAAa,mBAAK,yBAA8B;AAAA,UACrD;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,gCAAK,0CAAL,WACC,MACA;AAGD,iBAAO;AAAA,YACN;AAAA,YACA,IAAI,aAAa,mBAAK,qBAA0B;AAAA,UACjD;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,gCAAK,0CAAL,WACC,MACA;AAGD,iBAAO;AAAA,YACN;AAAA,YACA,mBAAK,QAAO;AAAA,cACV,KAA4B,KAAK;AAAA,cAClC;AAAA,gBACE,KAAyB;AAAA,gBAC1B,oBAAAA,QAAG,WAAW;AAAA,cACf;AAAA,YACD;AAAA,UACD;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO,sBAAK,gCAAL,WACN,MACA;AAAA,QAEF,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO;AAAA,YACN;AAAA,YACA,IAAI,aAAa,mBAAK,qBAA0B;AAAA,UACjD;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO,sBAAK,kEAAL,WACN,MACA,IACA;AAAA,QAEF,KAAK,oBAAAA,QAAG,WAAW;AAClB,iBAAO,sBAAK,kDAAL,WACN,MACA,IACA;AAAA,QAGF,KAAK,oBAAAA,QAAG,WAAW;AAClB,gCAAK,0DAAL,WAAgC;AAChC;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,cACC,KAAK,OAAO,SAAS,oBAAAA,QAAG,WAAW,mBACjC,KAAiC,KAAK,SACvC,oBAAAA,QAAG,WAAW,cACd;AAAA,YACE,KAA6B;AAAA,UAC/B,MAAM,oBAAAA,QAAG,WAAW,cACpB;AACD,kCAAK,0CAAL,WACE,KAA6B,MAC9B,OACA;AAAA,UAEF;AAEA;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,6BAAK,QAAO;AAAA,YACX,gBAAiB,KAAuB,IAAI;AAAA,YAC3C,KAAuB;AAAA;AAAA,YAExB;AAAA;AAAA,UAED;AACA;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAAA,QACnB,KAAK,oBAAAA,QAAG,WAAW;AAClB,gCAAK,0CAAL,WACC,MACA;AAGD;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,6BAAK,QAAO;AAAA,YACV,KAAqC,KAAK;AAAA,YAC1C,KAAqC;AAAA,YACtC,KAAK,OAAO,SAAS,oBAAAA,QAAG,WAAW;AAAA,YAGnC;AAAA;AAAA,UAED;AACA;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW;AAClB,cAAK,KAA4B,iBAAiB,QAAW;AAC5D,mBAAO,mBAAK,QAAO;AAAA,cACjB,KAA4B;AAAA,cAC5B,KAA4B;AAAA,YAC9B;AAAA,UACD;AAEA,iBAAO,mBAAK,QAAO,aAAc,KAA4B,IAAI;AAAA,QAClE,KAAK,oBAAAA,QAAG,WAAW;AAClB,cACE,KAA6B,WAAW,SACzC,oBAAAA,QAAG,WAAW,YACb;AACD,mBAAO,mBAAK,QAAO;AAAA,cACjB,KAA6B;AAAA,YAC/B;AAAA,UACD;AAEA;AAAA,QACD,KAAK,oBAAAA,QAAG,WAAW,YAAY;AAC9B,gBAAM,SAAS,eAAe,IAAqB;AACnD,cAAI,WAAW,QAAW;AACzB,+BAAK,QAAO,OAAO,EAAE,QAAQ,UAAU,KAAsB,CAAC;AAAA,UAC/D;AAEA;AAAA,QACD;AAAA,MACD;AAEA,aAAO,oBAAAA,QAAG,aAAa,MAAM,EAAE;AAAA,IAChC;AAEA,UAAM,oBAAoB,CACzB,MACA,OACA,OAA0B,iBACtB;AACJ,YAAM,aAAa,mBAAK;AACxB,yBAAK,QAAS;AACd,WAAK,IAAI;AACT,yBAAK,QAAO,IAAI,gBAAgB;AAChC,yBAAK,QAAS;AAAA,IACf;AAEA,UAAM,mBAAmB,CAAC,SAAkB;AAC3C,UACC,KAAK,SAAS,oBAAAA,QAAG,WAAW,eAC3B,KAAwB,wBAAwB,QAChD;AACD,8BAAK,0CAAL,WACE,KAAwB,oBAAqB,MAC9C,MACA;AAAA,MAEF;AAEA,aAAO,oBAAAA,QAAG,aAAa,MAAM,EAAE;AAAA,IAChC;AAEA,wBAAAA,QAAG,aAAa,YAAY,EAAE;AAC9B,uBAAK,QAAO,IAAI,gBAAgB;AAChC,WAAO,mBAAK;AAEZ,aAAS,aAAa,MAAe;AACpC,aAAO,oBAAAA,QAAG,aAAa,MAAM,EAAE;AAAA,IAChC;AAAA,EACD;AACD;AAxYC;AACA;AAEA;AAAA,uBAAkB,SACjB,MACA,aACA,UACC;AACD,MAAI,KAAK,SAAS,oBAAAA,QAAG,WAAW,YAAY;AAC3C,WAAO,mBAAK,QAAO;AAAA,MAClB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MAGA;AAAA;AAAA,IAED;AAAA,EACD;AAEA,iCAA+B,MAAM,CAAC,gBAAgB;AACrD,uBAAK,QAAO;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,YAAY;AAAA,MACZ;AAAA,MAGA;AAAA;AAAA,IAED;AAAA,EACD,CAAC;AACF;AAEA;AAAA,2BAAsB,SACrB,MACA,IACA,OACC;AACD,QAAM,aAAa,mBAAK;AACxB,QAAM,QAAS,mBAAK,QAAS,IAAI,qBAAqB,UAAU;AAChE,KAAG,KAAK,SAAS;AACjB,QAAM,2BAA6C;AACnD,KAAG,KAAK,WAAW;AACnB,QAAM,4BAA8C;AACpD,KAAG,KAAK,QAAQ;AAChB,QAAM,6BAA+C;AACrD,KAAG,KAAK,SAAS;AACjB,QAAM,IAAI,KAAK;AACf,qBAAK,QAAS;AACf;AAEA;AAAA,uBAAkB,SACjB,MACA,aACA,QACC;AACD,MAAI,KAAK,SAAS,QAAW;AAC5B,uBAAK,QAAO;AAAA,MACV,KAAK,KAAuB;AAAA,MAC7B,KAAK;AAAA,MACL;AAAA,MAGA;AAAA,QACE,KAAyB;AAAA,QAC1B,oBAAAA,QAAG,WAAW;AAAA,MACf;AAAA,MACA;AAAA,IACD;AAAA,EACD;AACD;AAEA;AAAA,mCAA8B,SAC7B,MACA,IACA,OACC;AACD,MAAI,kBAAkB,IAAI,GAAG;AAC5B,kBAAc,IAAI,GAAG,QAAQ,EAAE;AAAA,EAChC;AAEA,QAAM,aAAa,mBAAK;AACxB,MAAI,KAAK,SAAS,oBAAAA,QAAG,WAAW,qBAAqB;AACpD,0BAAK,0CAAL,WAAwB,MAAM;AAAA,EAC/B;AAEA,QAAM,QAAS,mBAAK,QACnB,KAAK,SAAS,oBAAAA,QAAG,WAAW,sBAAsB,KAAK,SAAS,SAC7D,IAAI,wBAAwB,KAAK,MAAM,UAAU,IACjD,IAAI,cAAc,UAAU;AAChC,MAAI,KAAK,SAAS,QAAW;AAC5B,OAAG,KAAK,IAAI;AAAA,EACb;AAEA,MAAI,KAAK,mBAAmB,QAAW;AACtC,SAAK,eAAe,QAAQ,EAAE;AAAA,EAC/B;AAEA,OAAK,WAAW,QAAQ,EAAE;AAC1B,MAAI,KAAK,SAAS,QAAW;AAC5B,OAAG,KAAK,IAAI;AAAA,EACb;AAEA,MAAI,KAAK,SAAS,QAAW;AAC5B,UAAM,UAAU;AAChB,OAAG,KAAK,IAAI;AAAA,EACb;AAEA,QAAM,IAAI,KAAK;AACf,qBAAK,QAAS;AACf;AAEA;AAAA,kBAAa,SACZ,MACA,MACC;AACD,MAAI,KAAK,QAAQ,oBAAAA,QAAG,UAAU,oBAAoB;AACjD,WAAO;AAAA,MACN;AAAA,MACA,mBAAK,QAAO,4BAA4B,WAAW,OAAO,MAAM,KAAK;AAAA,IACtE;AAAA,EACD;AAEA,MAAI,KAAK,KAAK,SAAS,oBAAAA,QAAG,WAAW,YAAY;AAChD,UAAM,WAAW,oBAAoB,IAA+B;AACpE,uBAAK,QAAO;AAAA,MACX,KAAK,KAAK;AAAA,MACV,KAAK;AAAA;AAAA,MAEL;AAAA;AAAA,IAED;AACA,UAAM,UAAU;AAAA,MACf,KAAK;AAAA,MACL,oBAAAA,QAAG,WAAW;AAAA,IACf;AACA,WAAO;AAAA,MACN;AAAA,MACA,mBAAK,QAAO;AAAA,QACX,KAAK,KAAK;AAAA,QACV;AAAA,QACA;AAAA,QACA,WAAW,4BAA4B,IAAI;AAAA,MAC5C;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA,mBAAK,QAAO;AAAA,MACX,IAAI,KAAK,KAAK,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,MACA,4BAA4B,IAAI;AAAA,IACjC;AAAA,EACD;AACD;AAEA;AAAA,+BAA0B,SAAC,iBAA6C;AACvE,QAAM,cAAc,qCAAqC,eAAe;AACxE,QAAM,WACL,gBAAgB,OAAO,SAAS,oBAAAA,QAAG,WAAW,qBAC9C;AAAA,IACC,gBAAgB,OAAO;AAAA,IACvB,oBAAAA,QAAG,WAAW;AAAA,EACf;AACD,aAAW,eAAe,gBAAgB,cAAc;AACvD,0BAAK,0CAAL,WAAwB,YAAY,MAAM,aAAa;AAAA,EACxD;AACD;AAiOD,SAAS,oBAAoB,MAA+B;AAC3D,SACC,KAAK,OAAO,SAAS,oBAAAA,QAAG,WAAW,qBACnC,iBAAiB,KAAK,WAAW,oBAAAA,QAAG,WAAW,aAAa;AAE9D;AAEA,SAAS,4BAA4B,IAAmC;AACvE,MAAI,GAAG,SAAS,UAAa,GAAG,KAAK,SAAS,oBAAAA,QAAG,WAAW,aAAa;AACxE,WAAO;AAAA,EACR;AAEA,SAAO,wBAAwB,GAAG,IAAI;AACvC;AAEA,SAAS,wBAAwB,OAA8B;AAC9D,aAAW,aAAa,MAAM,YAAY;AACzC,QACC,UAAU,SAAS,oBAAAA,QAAG,WAAW,qBACjC,UAAU,SAAS,oBAAAA,QAAG,WAAW,kBAChC;AACD,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAEA,SAAS,qCACR,iBACU;AACV,UAAQ,gBAAgB,QAAQ,oBAAAA,QAAG,UAAU,iBAAiB;AAC/D;AAEA,SAAS,+BACR,SACA,IACgB;AAChB,aAAW,WAAW,QAAQ,UAAU;AACvC,QAAI,QAAQ,SAAS,oBAAAA,QAAG,WAAW,gBAAgB;AAClD;AAAA,IACD;AAEA,QAAI;AACJ,QAAI,QAAQ,KAAK,SAAS,oBAAAA,QAAG,WAAW,YAAY;AACnD,eAAS,GAAG,OAAsD;AAAA,IACnE,OAAO;AACN,eAAS,+BAA+B,QAAQ,MAAM,EAAE;AAAA,IACzD;AAEA,QAAI,QAAQ;AACX,aAAO;AAAA,IACR;AAAA,EACD;AACD;;;AO9cO,SAAS,qBACf,YACgC;AAChC,SAAO,IAAI,YAAY,EAAE,SAAS,UAAU;AAC7C;", "names": ["import_typescript", "ts", "ts", "import_typescript", "ts", "import_typescript", "ts", "import_typescript", "import_typescript", "ts", "AccessKind", "ts", "import_typescript", "import_typescript", "ts", "import_typescript", "import_typescript", "ts", "ts", "ts", "import_typescript", "ts", "import_typescript", "import_typescript", "ts", "import_typescript", "import_typescript", "ts", "ts", "import_typescript", "ts", "ts", "import_typescript", "import_typescript", "ts", "ts", "typeParts", "import_typescript", "import_typescript", "ts", "import_typescript", "import_typescript", "ts", "DeclarationDomain", "ts", "import_typescript", "ts", "import_typescript", "UsageDomain", "ts", "_name", "_exports", "_innerScope", "ts"]}