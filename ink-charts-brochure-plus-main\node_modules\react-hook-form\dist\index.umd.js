!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactHookForm={},e.React)}(this,(function(e,t){"use strict";var r=e=>"checkbox"===e.type,s=e=>e instanceof Date,a=e=>null==e;const n=e=>"object"==typeof e;var i=e=>!a(e)&&!Array.isArray(e)&&n(e)&&!s(e),o=e=>i(e)&&e.target?r(e.target)?e.target.checked:e.target.value:e,u=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),l=e=>{const t=e.constructor&&e.constructor.prototype;return i(t)&&t.hasOwnProperty("isPrototypeOf")},d="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function c(e){let t;const r=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(d&&(e instanceof Blob||e instanceof FileList)||!r&&!i(e))return e;if(t=r?[]:{},r||l(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=c(e[r]));else t=e}return t}var f=e=>Array.isArray(e)?e.filter(Boolean):[],m=e=>void 0===e,y=(e,t,r)=>{if(!t||!i(e))return r;const s=f(t.split(/[,[\].]+?/)).reduce(((e,t)=>a(e)?e:e[t]),e);return m(s)||s===e?m(e[t])?r:e[t]:s},g=e=>"boolean"==typeof e,_=e=>/^\w*$/.test(e),p=e=>f(e.replace(/["|']|\]/g,"").split(/\.|\[/)),v=(e,t,r)=>{let s=-1;const a=_(t)?[t]:p(t),n=a.length,o=n-1;for(;++s<n;){const t=a[s];let n=r;if(s!==o){const r=e[t];n=i(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t)return;e[t]=n,e=e[t]}return e};const h={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},b={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},A="max",F="min",V="maxLength",x="minLength",S="pattern",w="required",k="validate",D=t.createContext(null),C=()=>t.useContext(D);var E=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const n in e)Object.defineProperty(a,n,{get:()=>{const a=n;return t._proxyFormState[a]!==b.all&&(t._proxyFormState[a]=!s||b.all),r&&(r[a]=!0),e[a]}});return a},O=e=>i(e)&&!Object.keys(e).length,j=(e,t,r,s)=>{r(e);const{name:a,...n}=e;return O(n)||Object.keys(n).length>=Object.keys(t).length||Object.keys(n).find((e=>t[e]===(!s||b.all)))},T=e=>Array.isArray(e)?e:[e],U=(e,t,r)=>!e||!t||e===t||T(e).some((e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))));function B(e){const r=t.useRef(e);r.current=e,t.useEffect((()=>{const t=!e.disabled&&r.current.subject&&r.current.subject.subscribe({next:r.current.next});return()=>{t&&t.unsubscribe()}}),[e.disabled])}function N(e){const r=C(),{control:s=r.control,disabled:a,name:n,exact:i}=e||{},[o,u]=t.useState(s._formState),l=t.useRef(!0),d=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=t.useRef(n);return c.current=n,B({disabled:a,next:e=>l.current&&U(c.current,e.name,i)&&j(e,d.current,s._updateFormState)&&u({...s._formState,...e}),subject:s._subjects.state}),t.useEffect((()=>(l.current=!0,d.current.isValid&&s._updateValid(!0),()=>{l.current=!1})),[s]),E(o,s,d.current,!1)}var L=e=>"string"==typeof e,R=(e,t,r,s,a)=>L(e)?(s&&t.watch.add(e),y(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),y(r,e)))):(s&&(t.watchAll=!0),r);function M(e){const r=C(),{control:s=r.control,name:a,defaultValue:n,disabled:i,exact:o}=e||{},u=t.useRef(a);u.current=a,B({disabled:i,subject:s._subjects.values,next:e=>{U(u.current,e.name,o)&&d(c(R(u.current,s._names,e.values||s._formValues,!1,n)))}});const[l,d]=t.useState(s._getWatch(a,n));return t.useEffect((()=>s._removeUnmounted())),l}function P(e){const r=C(),{name:s,disabled:a,control:n=r.control,shouldUnregister:i}=e,l=u(n._names.array,s),d=M({control:n,name:s,defaultValue:y(n._formValues,s,y(n._defaultValues,s,e.defaultValue)),exact:!0}),f=N({control:n,name:s,exact:!0}),_=t.useRef(n.register(s,{...e.rules,value:d,...g(e.disabled)?{disabled:e.disabled}:{}}));return t.useEffect((()=>{const e=n._options.shouldUnregister||i,t=(e,t)=>{const r=y(n._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=c(y(n._options.defaultValues,s));v(n._defaultValues,s,e),m(y(n._formValues,s))&&v(n._formValues,s,e)}return()=>{(l?e&&!n._state.action:e)?n.unregister(s):t(s,!1)}}),[s,n,l,i]),t.useEffect((()=>{y(n._fields,s)&&n._updateDisabledField({disabled:a,fields:n._fields,name:s,value:y(n._fields,s)._f.value})}),[a,s,n]),{field:{name:s,value:d,...g(a)||f.disabled?{disabled:f.disabled||a}:{},onChange:t.useCallback((e=>_.current.onChange({target:{value:o(e),name:s},type:h.CHANGE})),[s]),onBlur:t.useCallback((()=>_.current.onBlur({target:{value:y(n._formValues,s),name:s},type:h.BLUR})),[s,n]),ref:t.useCallback((e=>{const t=y(n._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[n._fields,s])},formState:f,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!y(f.errors,s)},isDirty:{enumerable:!0,get:()=>!!y(f.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!y(f.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!y(f.validatingFields,s)},error:{enumerable:!0,get:()=>y(f.errors,s)}})}}const q=e=>{const t={};for(const r of Object.keys(e))if(n(e[r])){const s=q(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},W="post";var $=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},I=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},H=(e,t,r={})=>r.shouldFocus||m(r.shouldFocus)?r.focusName||`${e}.${m(r.focusIndex)?t:r.focusIndex}.`:"",G=e=>({isOnSubmit:!e||e===b.onSubmit,isOnBlur:e===b.onBlur,isOnChange:e===b.onChange,isOnAll:e===b.all,isOnTouch:e===b.onTouched}),J=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const z=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=y(e,a);if(r){const{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(z(n,t))break}else if(i(n)&&z(n,t))break}}};var K=(e,t,r)=>{const s=T(y(e,r));return v(s,"root",t[r]),v(e,r,s),e},Q=e=>"file"===e.type,X=e=>"function"==typeof e,Y=e=>{if(!d)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Z=e=>L(e),ee=e=>"radio"===e.type,te=e=>e instanceof RegExp;const re={value:!1,isValid:!1},se={value:!0,isValid:!0};var ae=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!m(e[0].attributes.value)?m(e[0].value)||""===e[0].value?se:{value:e[0].value,isValid:!0}:se:re}return re};const ne={isValid:!1,value:null};var ie=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),ne):ne;function oe(e,t,r="validate"){if(Z(e)||Array.isArray(e)&&e.every(Z)||g(e)&&!e)return{type:r,message:Z(e)?e:"",ref:t}}var ue=e=>i(e)&&!te(e)?e:{value:e,message:""},le=async(e,t,s,n,o)=>{const{ref:u,refs:l,required:d,maxLength:c,minLength:f,min:_,max:p,pattern:v,validate:h,name:b,valueAsNumber:D,mount:C,disabled:E}=e._f,j=y(t,b);if(!C||E)return{};const T=l?l[0]:u,U=e=>{n&&T.reportValidity&&(T.setCustomValidity(g(e)?"":e||""),T.reportValidity())},B={},N=ee(u),R=r(u),M=N||R,P=(D||Q(u))&&m(u.value)&&m(j)||Y(u)&&""===u.value||""===j||Array.isArray(j)&&!j.length,q=$.bind(null,b,s,B),W=(e,t,r,s=V,a=x)=>{const n=e?t:r;B[b]={type:e?s:a,message:n,ref:u,...q(e?s:a,n)}};if(o?!Array.isArray(j)||!j.length:d&&(!M&&(P||a(j))||g(j)&&!j||R&&!ae(l).isValid||N&&!ie(l).isValid)){const{value:e,message:t}=Z(d)?{value:!!d,message:d}:ue(d);if(e&&(B[b]={type:w,message:t,ref:T,...q(w,t)},!s))return U(t),B}if(!(P||a(_)&&a(p))){let e,t;const r=ue(p),n=ue(_);if(a(j)||isNaN(j)){const s=u.valueAsDate||new Date(j),a=e=>new Date((new Date).toDateString()+" "+e),i="time"==u.type,o="week"==u.type;L(r.value)&&j&&(e=i?a(j)>a(r.value):o?j>r.value:s>new Date(r.value)),L(n.value)&&j&&(t=i?a(j)<a(n.value):o?j<n.value:s<new Date(n.value))}else{const s=u.valueAsNumber||(j?+j:j);a(r.value)||(e=s>r.value),a(n.value)||(t=s<n.value)}if((e||t)&&(W(!!e,r.message,n.message,A,F),!s))return U(B[b].message),B}if((c||f)&&!P&&(L(j)||o&&Array.isArray(j))){const e=ue(c),t=ue(f),r=!a(e.value)&&j.length>+e.value,n=!a(t.value)&&j.length<+t.value;if((r||n)&&(W(r,e.message,t.message),!s))return U(B[b].message),B}if(v&&!P&&L(j)){const{value:e,message:t}=ue(v);if(te(e)&&!j.match(e)&&(B[b]={type:S,message:t,ref:u,...q(S,t)},!s))return U(t),B}if(h)if(X(h)){const e=oe(await h(j,t),T);if(e&&(B[b]={...e,...q(k,e.message)},!s))return U(e.message),B}else if(i(h)){let e={};for(const r in h){if(!O(e)&&!s)break;const a=oe(await h[r](j,t),T,r);a&&(e={...a,...q(r,a.message)},U(a.message),s&&(B[b]=e))}if(!O(e)&&(B[b]={ref:T,...e},!s))return B}return U(!0),B},de=(e,t)=>[...e,...T(t)],ce=e=>Array.isArray(e)?e.map((()=>{})):void 0;function fe(e,t,r){return[...e.slice(0,t),...T(r),...e.slice(t)]}var me=(e,t,r)=>Array.isArray(e)?(m(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],ye=(e,t)=>[...T(t),...T(e)];var ge=(e,t)=>m(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return f(s).length?s:[]}(e,T(t).sort(((e,t)=>e-t))),_e=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]};function pe(e,t){const r=Array.isArray(t)?t:_(t)?[t]:p(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=m(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,n=r[a];return s&&delete s[n],0!==a&&(i(s)&&O(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!m(e[t]))return!1;return!0}(s))&&pe(e,r.slice(0,-1)),e}var ve=(e,t,r)=>(e[t]=r,e);var he=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},be=e=>a(e)||!n(e);function Ae(e,t){if(be(e)||be(t))return e===t;if(s(e)&&s(t))return e.getTime()===t.getTime();const r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(const n of r){const r=e[n];if(!a.includes(n))return!1;if("ref"!==n){const e=t[n];if(s(r)&&s(e)||i(r)&&i(e)||Array.isArray(r)&&Array.isArray(e)?!Ae(r,e):r!==e)return!1}}return!0}var Fe=e=>"select-multiple"===e.type,Ve=e=>ee(e)||r(e),xe=e=>Y(e)&&e.isConnected,Se=e=>{for(const t in e)if(X(e[t]))return!0;return!1};function we(e,t={}){const r=Array.isArray(e);if(i(e)||r)for(const r in e)Array.isArray(e[r])||i(e[r])&&!Se(e[r])?(t[r]=Array.isArray(e[r])?[]:{},we(e[r],t[r])):a(e[r])||(t[r]=!0);return t}function ke(e,t,r){const s=Array.isArray(e);if(i(e)||s)for(const s in e)Array.isArray(e[s])||i(e[s])&&!Se(e[s])?m(t)||be(r[s])?r[s]=Array.isArray(e[s])?we(e[s],[]):{...we(e[s])}:ke(e[s],a(t)?{}:t[s],r[s]):r[s]=!Ae(e[s],t[s]);return r}var De=(e,t)=>ke(e,t,we(t)),Ce=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>m(e)?e:t?""===e?NaN:e?+e:e:r&&L(e)?new Date(e):s?s(e):e;function Ee(e){const t=e.ref;if(!(e.refs?e.refs.every((e=>e.disabled)):t.disabled))return Q(t)?t.files:ee(t)?ie(e.refs).value:Fe(t)?[...t.selectedOptions].map((({value:e})=>e)):r(t)?ae(e.refs).value:Ce(m(t.value)?e.ref.value:t.value,e)}var Oe=(e,t,r,s)=>{const a={};for(const r of e){const e=y(t,r);e&&v(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}},je=e=>m(e)?e:te(e)?e.source:i(e)?te(e.value)?e.value.source:e.value:e;const Te="AsyncFunction";var Ue=e=>!(e&&e.validate||!(X(e.validate)&&e.validate.constructor.name===Te||i(e.validate)&&Object.values(e.validate).find((e=>e.constructor.name===Te)))),Be=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function Ne(e,t,r){const s=y(e,r);if(s||_(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),n=y(t,s),i=y(e,s);if(n&&!Array.isArray(n)&&r!==s)return{name:r};if(i&&i.type)return{name:s,error:i};a.pop()}return{name:r}}var Le=(e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e),Re=(e,t)=>!f(y(e,t)).length&&pe(e,t);const Me={mode:b.onSubmit,reValidateMode:b.onChange,shouldFocusError:!0};function Pe(e={}){let t,n={...Me,...e},l={submitCount:0,isDirty:!1,isLoading:X(n.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:n.errors||{},disabled:n.disabled||!1},_={},p=(i(n.defaultValues)||i(n.values))&&c(n.defaultValues||n.values)||{},A=n.shouldUnregister?{}:c(p),F={action:!1,mount:!1,watch:!1},V={mount:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},w={values:he(),array:he(),state:he()},k=G(n.mode),D=G(n.reValidateMode),C=n.criteriaMode===b.all,E=async t=>{if(!e.disabled&&(S.isValid||t)){const e=n.resolver?O((await M()).errors):await P(_,!0);e!==l.isValid&&w.state.next({isValid:e})}},j=(t,r)=>{e.disabled||!S.isValidating&&!S.validatingFields||((t||Array.from(V.mount)).forEach((e=>{e&&(r?v(l.validatingFields,e,r):pe(l.validatingFields,e))})),w.state.next({validatingFields:l.validatingFields,isValidating:!O(l.validatingFields)}))},U=(e,t,r,s)=>{const a=y(_,e);if(a){const n=y(A,e,m(r)?y(p,e):r);m(n)||s&&s.defaultChecked||t?v(A,e,t?n:Ee(a._f)):$(e,n),F.mount&&E()}},B=(t,r,s,a,n)=>{let i=!1,o=!1;const u={name:t};if(!e.disabled){const e=!!(y(_,t)&&y(_,t)._f&&y(_,t)._f.disabled);if(!s||a){S.isDirty&&(o=l.isDirty,l.isDirty=u.isDirty=q(),i=o!==u.isDirty);const s=e||Ae(y(p,t),r);o=!(e||!y(l.dirtyFields,t)),s||e?pe(l.dirtyFields,t):v(l.dirtyFields,t,!0),u.dirtyFields=l.dirtyFields,i=i||S.dirtyFields&&o!==!s}if(s){const e=y(l.touchedFields,t);e||(v(l.touchedFields,t,s),u.touchedFields=l.touchedFields,i=i||S.touchedFields&&e!==s)}i&&n&&w.state.next(u)}return i?u:{}},N=(r,s,a,n)=>{const i=y(l.errors,r),o=S.isValid&&g(s)&&l.isValid!==s;var u;if(e.delayError&&a?(u=()=>((e,t)=>{v(l.errors,e,t),w.state.next({errors:l.errors})})(r,a),t=e=>{clearTimeout(x),x=setTimeout(u,e)},t(e.delayError)):(clearTimeout(x),t=null,a?v(l.errors,r,a):pe(l.errors,r)),(a?!Ae(i,a):i)||!O(n)||o){const e={...n,...o&&g(s)?{isValid:s}:{},errors:l.errors,name:r};l={...l,...e},w.state.next(e)}},M=async e=>{j(e,!0);const t=await n.resolver(A,n.context,Oe(e||V.mount,_,n.criteriaMode,n.shouldUseNativeValidation));return j(e),t},P=async(e,t,r={valid:!0})=>{for(const s in e){const a=e[s];if(a){const{_f:e,...i}=a;if(e){const i=V.array.has(e.name),o=a._f&&Ue(a._f);o&&S.validatingFields&&j([s],!0);const u=await le(a,A,C,n.shouldUseNativeValidation&&!t,i);if(o&&S.validatingFields&&j([s]),u[e.name]&&(r.valid=!1,t))break;!t&&(y(u,e.name)?i?K(l.errors,u,e.name):v(l.errors,e.name,u[e.name]):pe(l.errors,e.name))}!O(i)&&await P(i,t,r)}}return r.valid},q=(t,r)=>!e.disabled&&(t&&r&&v(A,t,r),!Ae(re(),p)),W=(e,t,r)=>R(e,V,{...F.mount?A:m(t)?p:L(e)?{[e]:t}:t},r,t),$=(e,t,s={})=>{const n=y(_,e);let i=t;if(n){const s=n._f;s&&(!s.disabled&&v(A,e,Ce(t,s)),i=Y(s.ref)&&a(t)?"":t,Fe(s.ref)?[...s.ref.options].forEach((e=>e.selected=i.includes(e.value))):s.refs?r(s.ref)?s.refs.length>1?s.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(i)?!!i.find((t=>t===e.value)):i===e.value))):s.refs[0]&&(s.refs[0].checked=!!i):s.refs.forEach((e=>e.checked=e.value===i)):Q(s.ref)?s.ref.value="":(s.ref.value=i,s.ref.type||w.values.next({name:e,values:{...A}})))}(s.shouldDirty||s.shouldTouch)&&B(e,i,s.shouldTouch,s.shouldDirty,!0),s.shouldValidate&&te(e)},I=(e,t,r)=>{for(const a in t){const n=t[a],o=`${e}.${a}`,u=y(_,o);(V.array.has(e)||i(n)||u&&!u._f)&&!s(n)?I(o,n,r):$(o,n,r)}},H=(e,t,r={})=>{const s=y(_,e),n=V.array.has(e),i=c(t);v(A,e,i),n?(w.array.next({name:e,values:{...A}}),(S.isDirty||S.dirtyFields)&&r.shouldDirty&&w.state.next({name:e,dirtyFields:De(p,A),isDirty:q(e,i)})):!s||s._f||a(i)?$(e,i,r):I(e,i,r),J(e,V)&&w.state.next({...l}),w.values.next({name:F.mount?e:void 0,values:{...A}})},Z=async r=>{F.mount=!0;const a=r.target;let i=a.name,u=!0;const d=y(_,i),c=e=>{u=Number.isNaN(e)||s(e)&&isNaN(e.getTime())||Ae(e,y(A,i,e))};if(d){let s,f;const m=a.type?Ee(d._f):o(r),g=r.type===h.BLUR||r.type===h.FOCUS_OUT,p=!Be(d._f)&&!n.resolver&&!y(l.errors,i)&&!d._f.deps||Le(g,y(l.touchedFields,i),l.isSubmitted,D,k),b=J(i,V,g);v(A,i,m),g?(d._f.onBlur&&d._f.onBlur(r),t&&t(0)):d._f.onChange&&d._f.onChange(r);const F=B(i,m,g,!1),x=!O(F)||b;if(!g&&w.values.next({name:i,type:r.type,values:{...A}}),p)return S.isValid&&("onBlur"===e.mode?g&&E():E()),x&&w.state.next({name:i,...b?{}:F});if(!g&&b&&w.state.next({...l}),n.resolver){const{errors:e}=await M([i]);if(c(m),u){const t=Ne(l.errors,_,i),r=Ne(e,_,t.name||i);s=r.error,i=r.name,f=O(e)}}else j([i],!0),s=(await le(d,A,C,n.shouldUseNativeValidation))[i],j([i]),c(m),u&&(s?f=!1:S.isValid&&(f=await P(_,!0)));u&&(d._f.deps&&te(d._f.deps),N(i,f,s,F))}},ee=(e,t)=>{if(y(l.errors,t)&&e.focus)return e.focus(),1},te=async(e,t={})=>{let r,s;const a=T(e);if(n.resolver){const t=await(async e=>{const{errors:t}=await M(e);if(e)for(const r of e){const e=y(t,r);e?v(l.errors,r,e):pe(l.errors,r)}else l.errors=t;return t})(m(e)?e:a);r=O(t),s=e?!a.some((e=>y(t,e))):r}else e?(s=(await Promise.all(a.map((async e=>{const t=y(_,e);return await P(t&&t._f?{[e]:t}:t)})))).every(Boolean),(s||l.isValid)&&E()):s=r=await P(_);return w.state.next({...!L(e)||S.isValid&&r!==l.isValid?{}:{name:e},...n.resolver||!e?{isValid:r}:{},errors:l.errors}),t.shouldFocus&&!s&&z(_,ee,e?a:V.mount),s},re=e=>{const t={...F.mount?A:p};return m(e)?t:L(e)?y(t,e):e.map((e=>y(t,e)))},se=(e,t)=>({invalid:!!y((t||l).errors,e),isDirty:!!y((t||l).dirtyFields,e),error:y((t||l).errors,e),isValidating:!!y(l.validatingFields,e),isTouched:!!y((t||l).touchedFields,e)}),ae=(e,t,r)=>{const s=(y(_,e,{_f:{}})._f||{}).ref,a=y(l.errors,e)||{},{ref:n,message:i,type:o,...u}=a;v(l.errors,e,{...u,...t,ref:s}),w.state.next({name:e,errors:l.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},ne=(e,t={})=>{for(const r of e?T(e):V.mount)V.mount.delete(r),V.array.delete(r),t.keepValue||(pe(_,r),pe(A,r)),!t.keepError&&pe(l.errors,r),!t.keepDirty&&pe(l.dirtyFields,r),!t.keepTouched&&pe(l.touchedFields,r),!t.keepIsValidating&&pe(l.validatingFields,r),!n.shouldUnregister&&!t.keepDefaultValue&&pe(p,r);w.values.next({values:{...A}}),w.state.next({...l,...t.keepDirty?{isDirty:q()}:{}}),!t.keepIsValid&&E()},ie=({disabled:e,name:t,field:r,fields:s,value:a})=>{if(g(e)&&F.mount||e){const n=e?void 0:m(a)?Ee(r?r._f:y(s,t)._f):a;v(A,t,n),B(t,n,!1,!1,!0)}},oe=(t,r={})=>{let s=y(_,t);const a=g(r.disabled)||g(e.disabled);return v(_,t,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:t}},name:t,mount:!0,...r}}),V.mount.add(t),s?ie({field:s,disabled:g(r.disabled)?r.disabled:e.disabled,name:t,value:r.value}):U(t,!0,r.value),{...a?{disabled:r.disabled||e.disabled}:{},...n.progressive?{required:!!r.required,min:je(r.min),max:je(r.max),minLength:je(r.minLength),maxLength:je(r.maxLength),pattern:je(r.pattern)}:{},name:t,onChange:Z,onBlur:Z,ref:e=>{if(e){oe(t,r),s=y(_,t);const a=m(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,n=Ve(a),i=s._f.refs||[];if(n?i.find((e=>e===a)):a===s._f.ref)return;v(_,t,{_f:{...s._f,...n?{refs:[...i.filter(xe),a,...Array.isArray(y(p,t))?[{}]:[]],ref:{type:a.type,name:t}}:{ref:a}}}),U(t,!1,void 0,a)}else s=y(_,t,{}),s._f&&(s._f.mount=!1),(n.shouldUnregister||r.shouldUnregister)&&(!u(V.array,t)||!F.action)&&V.unMount.add(t)}}},ue=()=>n.shouldFocusError&&z(_,ee,V.mount),de=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=c(A);if(w.state.next({isSubmitting:!0}),n.resolver){const{errors:e,values:t}=await M();l.errors=e,a=t}else await P(_);if(pe(l.errors,"root"),O(l.errors)){w.state.next({errors:{}});try{await e(a,r)}catch(e){s=e}}else t&&await t({...l.errors},r),ue(),setTimeout(ue);if(w.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:O(l.errors)&&!s,submitCount:l.submitCount+1,errors:l.errors}),s)throw s},ce=(t,r={})=>{const s=t?c(t):p,a=c(s),n=O(t),i=n?p:a;if(r.keepDefaultValues||(p=s),!r.keepValues){if(r.keepDirtyValues){const e=new Set([...V.mount,...Object.keys(De(p,A))]);for(const t of Array.from(e))y(l.dirtyFields,t)?v(i,t,y(A,t)):H(t,y(i,t))}else{if(d&&m(t))for(const e of V.mount){const t=y(_,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Y(e)){const t=e.closest("form");if(t){t.reset();break}}}}_={}}A=e.shouldUnregister?r.keepDefaultValues?c(p):{}:c(i),w.array.next({values:{...i}}),w.values.next({values:{...i}})}V={mount:r.keepDirtyValues?V.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!S.isValid||!!r.keepIsValid||!!r.keepDirtyValues,F.watch=!!e.shouldUnregister,w.state.next({submitCount:r.keepSubmitCount?l.submitCount:0,isDirty:!n&&(r.keepDirty?l.isDirty:!(!r.keepDefaultValues||Ae(t,p))),isSubmitted:!!r.keepIsSubmitted&&l.isSubmitted,dirtyFields:n?{}:r.keepDirtyValues?r.keepDefaultValues&&A?De(p,A):l.dirtyFields:r.keepDefaultValues&&t?De(p,t):r.keepDirty?l.dirtyFields:{},touchedFields:r.keepTouched?l.touchedFields:{},errors:r.keepErrors?l.errors:{},isSubmitSuccessful:!!r.keepIsSubmitSuccessful&&l.isSubmitSuccessful,isSubmitting:!1})},fe=(e,t)=>ce(X(e)?e(A):e,t);return{control:{register:oe,unregister:ne,getFieldState:se,handleSubmit:de,setError:ae,_executeSchema:M,_getWatch:W,_getDirty:q,_updateValid:E,_removeUnmounted:()=>{for(const e of V.unMount){const t=y(_,e);t&&(t._f.refs?t._f.refs.every((e=>!xe(e))):!xe(t._f.ref))&&ne(e)}V.unMount=new Set},_updateFieldArray:(t,r=[],s,a,n=!0,i=!0)=>{if(a&&s&&!e.disabled){if(F.action=!0,i&&Array.isArray(y(_,t))){const e=s(y(_,t),a.argA,a.argB);n&&v(_,t,e)}if(i&&Array.isArray(y(l.errors,t))){const e=s(y(l.errors,t),a.argA,a.argB);n&&v(l.errors,t,e),Re(l.errors,t)}if(S.touchedFields&&i&&Array.isArray(y(l.touchedFields,t))){const e=s(y(l.touchedFields,t),a.argA,a.argB);n&&v(l.touchedFields,t,e)}S.dirtyFields&&(l.dirtyFields=De(p,A)),w.state.next({name:t,isDirty:q(t,r),dirtyFields:l.dirtyFields,errors:l.errors,isValid:l.isValid})}else v(A,t,r)},_updateDisabledField:ie,_getFieldArray:t=>f(y(F.mount?A:p,t,e.shouldUnregister?y(p,t,[]):[])),_reset:ce,_resetDefaultValues:()=>X(n.defaultValues)&&n.defaultValues().then((e=>{fe(e,n.resetOptions),w.state.next({isLoading:!1})})),_updateFormState:e=>{l={...l,...e}},_disableForm:e=>{g(e)&&(w.state.next({disabled:e}),z(_,((t,r)=>{const s=y(_,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:w,_proxyFormState:S,_setErrors:e=>{l.errors=e,w.state.next({errors:l.errors,isValid:!1})},get _fields(){return _},get _formValues(){return A},get _state(){return F},set _state(e){F=e},get _defaultValues(){return p},get _names(){return V},set _names(e){V=e},get _formState(){return l},set _formState(e){l=e},get _options(){return n},set _options(e){n={...n,...e}}},trigger:te,register:oe,handleSubmit:de,watch:(e,t)=>X(e)?w.values.subscribe({next:r=>e(W(void 0,t),r)}):W(e,t,!0),setValue:H,getValues:re,reset:fe,resetField:(e,t={})=>{y(_,e)&&(m(t.defaultValue)?H(e,c(y(p,e))):(H(e,t.defaultValue),v(p,e,c(t.defaultValue))),t.keepTouched||pe(l.touchedFields,e),t.keepDirty||(pe(l.dirtyFields,e),l.isDirty=t.defaultValue?q(e,c(y(p,e))):q()),t.keepError||(pe(l.errors,e),S.isValid&&E()),w.state.next({...l}))},clearErrors:e=>{e&&T(e).forEach((e=>pe(l.errors,e))),w.state.next({errors:e?l.errors:{}})},unregister:ne,setError:ae,setFocus:(e,t={})=>{const r=y(_,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&e.select())}},getFieldState:se}}e.Controller=e=>e.render(P(e)),e.Form=function(e){const r=C(),[s,a]=t.useState(!1),{control:n=r.control,onSubmit:i,children:o,action:u,method:l=W,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,..._}=e,p=async t=>{let r=!1,s="";await n.handleSubmit((async e=>{const a=new FormData;let o="";try{o=JSON.stringify(e)}catch(e){}const m=q(n._formValues);for(const e in m)a.append(e,m[e]);if(i&&await i({data:e,event:t,method:l,formData:a,formDataJson:o}),u)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(u,{method:l,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(t),r&&e.control&&(e.control._subjects.state.next({isSubmitSuccessful:!1}),e.control.setError("root.server",{type:s}))};return t.useEffect((()=>{a(!0)}),[]),m?t.createElement(t.Fragment,null,m({submit:p})):t.createElement("form",{noValidate:s,action:u,method:l,encType:c,onSubmit:p,..._},o)},e.FormProvider=e=>{const{children:r,...s}=e;return t.createElement(D.Provider,{value:s},r)},e.appendErrors=$,e.get=y,e.set=v,e.useController=P,e.useFieldArray=function(e){const r=C(),{control:s=r.control,name:a,keyName:n="id",shouldUnregister:i}=e,[o,u]=t.useState(s._getFieldArray(a)),l=t.useRef(s._getFieldArray(a).map(I)),d=t.useRef(o),f=t.useRef(a),m=t.useRef(!1);f.current=a,d.current=o,s._names.array.add(a),e.rules&&s.register(a,e.rules),B({next:({values:e,name:t})=>{if(t===f.current||!t){const t=y(e,f.current);Array.isArray(t)&&(u(t),l.current=t.map(I))}},subject:s._subjects.array});const g=t.useCallback((e=>{m.current=!0,s._updateFieldArray(a,e)}),[s,a]);return t.useEffect((()=>{if(s._state.action=!1,J(a,s._names)&&s._subjects.state.next({...s._formState}),m.current&&(!G(s._options.mode).isOnSubmit||s._formState.isSubmitted))if(s._options.resolver)s._executeSchema([a]).then((e=>{const t=y(e.errors,a),r=y(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?v(s._formState.errors,a,t):pe(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=y(s._fields,a);!e||!e._f||G(s._options.reValidateMode).isOnSubmit&&G(s._options.mode).isOnSubmit||le(e,s._formValues,s._options.criteriaMode===b.all,s._options.shouldUseNativeValidation,!0).then((e=>!O(e)&&s._subjects.state.next({errors:K(s._formState.errors,e,a)})))}s._subjects.values.next({name:a,values:{...s._formValues}}),s._names.focus&&z(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._updateValid(),m.current=!1}),[o,a,s]),t.useEffect((()=>(!y(s._formValues,a)&&s._updateFieldArray(a),()=>{(s._options.shouldUnregister||i)&&s.unregister(a)})),[a,s,n,i]),{swap:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);_e(r,e,t),_e(l.current,e,t),g(r),u(r),s._updateFieldArray(a,r,_e,{argA:e,argB:t},!1)}),[g,a,s]),move:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);me(r,e,t),me(l.current,e,t),g(r),u(r),s._updateFieldArray(a,r,me,{argA:e,argB:t},!1)}),[g,a,s]),prepend:t.useCallback(((e,t)=>{const r=T(c(e)),n=ye(s._getFieldArray(a),r);s._names.focus=H(a,0,t),l.current=ye(l.current,r.map(I)),g(n),u(n),s._updateFieldArray(a,n,ye,{argA:ce(e)})}),[g,a,s]),append:t.useCallback(((e,t)=>{const r=T(c(e)),n=de(s._getFieldArray(a),r);s._names.focus=H(a,n.length-1,t),l.current=de(l.current,r.map(I)),g(n),u(n),s._updateFieldArray(a,n,de,{argA:ce(e)})}),[g,a,s]),remove:t.useCallback((e=>{const t=ge(s._getFieldArray(a),e);l.current=ge(l.current,e),g(t),u(t),s._updateFieldArray(a,t,ge,{argA:e})}),[g,a,s]),insert:t.useCallback(((e,t,r)=>{const n=T(c(t)),i=fe(s._getFieldArray(a),e,n);s._names.focus=H(a,e,r),l.current=fe(l.current,e,n.map(I)),g(i),u(i),s._updateFieldArray(a,i,fe,{argA:e,argB:ce(t)})}),[g,a,s]),update:t.useCallback(((e,t)=>{const r=c(t),n=ve(s._getFieldArray(a),e,r);l.current=[...n].map(((t,r)=>t&&r!==e?l.current[r]:I())),g(n),u([...n]),s._updateFieldArray(a,n,ve,{argA:e,argB:r},!0,!1)}),[g,a,s]),replace:t.useCallback((e=>{const t=T(c(e));l.current=t.map(I),g([...t]),u([...t]),s._updateFieldArray(a,[...t],(e=>e),{},!0,!1)}),[g,a,s]),fields:t.useMemo((()=>o.map(((e,t)=>({...e,[n]:l.current[t]||I()})))),[o,n])}},e.useForm=function(e={}){const r=t.useRef(),s=t.useRef(),[a,n]=t.useState({isDirty:!1,isValidating:!1,isLoading:X(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:X(e.defaultValues)?void 0:e.defaultValues});r.current||(r.current={...Pe(e),formState:a});const i=r.current.control;return i._options=e,B({subject:i._subjects.state,next:e=>{j(e,i._proxyFormState,i._updateFormState,!0)&&n({...i._formState})}}),t.useEffect((()=>i._disableForm(e.disabled)),[i,e.disabled]),t.useEffect((()=>{if(i._proxyFormState.isDirty){const e=i._getDirty();e!==a.isDirty&&i._subjects.state.next({isDirty:e})}}),[i,a.isDirty]),t.useEffect((()=>{e.values&&!Ae(e.values,s.current)?(i._reset(e.values,i._options.resetOptions),s.current=e.values,n((e=>({...e})))):i._resetDefaultValues()}),[e.values,i]),t.useEffect((()=>{e.errors&&i._setErrors(e.errors)}),[e.errors,i]),t.useEffect((()=>{i._state.mount||(i._updateValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()})),t.useEffect((()=>{e.shouldUnregister&&i._subjects.values.next({values:i._getWatch()})}),[e.shouldUnregister,i]),t.useEffect((()=>{r.current&&(r.current.watch=r.current.watch.bind({}))}),[a]),r.current.formState=E(a,i),r.current},e.useFormContext=C,e.useFormState=N,e.useWatch=M}));
//# sourceMappingURL=index.umd.js.map
