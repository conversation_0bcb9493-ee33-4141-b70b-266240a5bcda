/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/getNiceTickValues.js":
/*!**********************************!*\
  !*** ./src/getNiceTickValues.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.getTickValuesFixedDomain = exports.getTickValues = exports.getNiceTickValues = void 0;\n\nvar _decimal = _interopRequireDefault(__webpack_require__(/*! decimal.js-light */ \"./node_modules/decimal.js-light/decimal.js\"));\n\nvar _utils = __webpack_require__(/*! ./util/utils */ \"./src/util/utils.js\");\n\nvar _arithmetic = _interopRequireDefault(__webpack_require__(/*! ./util/arithmetic */ \"./src/util/arithmetic.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\n/**\n * Calculate a interval of a minimum value and a maximum value\n *\n * @param  {Number} min       The minimum value\n * @param  {Number} max       The maximum value\n * @return {Array} An interval\n */\nfunction getValidInterval(_ref) {\n  var _ref2 = _slicedToArray(_ref, 2),\n      min = _ref2[0],\n      max = _ref2[1];\n\n  var validMin = min,\n      validMax = max; // exchange\n\n  if (min > max) {\n    validMin = max;\n    validMax = min;\n  }\n\n  return [validMin, validMax];\n}\n/**\n * Calculate the step which is easy to understand between ticks, like 10, 20, 25\n *\n * @param  {Decimal} roughStep        The rough step calculated by deviding the\n * difference by the tickCount\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Integer} correctionFactor A correction factor\n * @return {Decimal} The step which is easy to understand between two ticks\n */\n\n\nfunction getFormatStep(roughStep, allowDecimals, correctionFactor) {\n  if (roughStep.lte(0)) {\n    return new _decimal.default(0);\n  }\n\n  var digitCount = _arithmetic.default.getDigitCount(roughStep.toNumber()); // The ratio between the rough step and the smallest number which has a bigger\n  // order of magnitudes than the rough step\n\n\n  var digitCountValue = new _decimal.default(10).pow(digitCount);\n  var stepRatio = roughStep.div(digitCountValue); // When an integer and a float multiplied, the accuracy of result may be wrong\n\n  var stepRatioScale = digitCount !== 1 ? 0.05 : 0.1;\n  var amendStepRatio = new _decimal.default(Math.ceil(stepRatio.div(stepRatioScale).toNumber())).add(correctionFactor).mul(stepRatioScale);\n  var formatStep = amendStepRatio.mul(digitCountValue);\n  return allowDecimals ? formatStep : new _decimal.default(Math.ceil(formatStep));\n}\n/**\n * calculate the ticks when the minimum value equals to the maximum value\n *\n * @param  {Number}  value         The minimum valuue which is also the maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}                 ticks\n */\n\n\nfunction getTickOfSingleValue(value, tickCount, allowDecimals) {\n  var step = 1; // calculate the middle value of ticks\n\n  var middle = new _decimal.default(value);\n\n  if (!middle.isint() && allowDecimals) {\n    var absVal = Math.abs(value);\n\n    if (absVal < 1) {\n      // The step should be a float number when the difference is smaller than 1\n      step = new _decimal.default(10).pow(_arithmetic.default.getDigitCount(value) - 1);\n      middle = new _decimal.default(Math.floor(middle.div(step).toNumber())).mul(step);\n    } else if (absVal > 1) {\n      // Return the maximum integer which is smaller than 'value' when 'value' is greater than 1\n      middle = new _decimal.default(Math.floor(value));\n    }\n  } else if (value === 0) {\n    middle = new _decimal.default(Math.floor((tickCount - 1) / 2));\n  } else if (!allowDecimals) {\n    middle = new _decimal.default(Math.floor(value));\n  }\n\n  var middleIndex = Math.floor((tickCount - 1) / 2);\n  var fn = (0, _utils.compose)((0, _utils.map)(function (n) {\n    return middle.add(new _decimal.default(n - middleIndex).mul(step)).toNumber();\n  }), _utils.range);\n  return fn(0, tickCount);\n}\n/**\n * Calculate the step\n *\n * @param  {Number}  min              The minimum value of an interval\n * @param  {Number}  max              The maximum value of an interval\n * @param  {Integer} tickCount        The count of ticks\n * @param  {Boolean} allowDecimals    Allow the ticks to be decimals or not\n * @param  {Number}  correctionFactor A correction factor\n * @return {Object}  The step, minimum value of ticks, maximum value of ticks\n */\n\n\nfunction calculateStep(min, max, tickCount, allowDecimals) {\n  var correctionFactor = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n\n  // dirty hack (for recharts' test)\n  if (!Number.isFinite((max - min) / (tickCount - 1))) {\n    return {\n      step: new _decimal.default(0),\n      tickMin: new _decimal.default(0),\n      tickMax: new _decimal.default(0)\n    };\n  } // The step which is easy to understand between two ticks\n\n\n  var step = getFormatStep(new _decimal.default(max).sub(min).div(tickCount - 1), allowDecimals, correctionFactor); // A medial value of ticks\n\n  var middle; // When 0 is inside the interval, 0 should be a tick\n\n  if (min <= 0 && max >= 0) {\n    middle = new _decimal.default(0);\n  } else {\n    // calculate the middle value\n    middle = new _decimal.default(min).add(max).div(2); // minus modulo value\n\n    middle = middle.sub(new _decimal.default(middle).mod(step));\n  }\n\n  var belowCount = Math.ceil(middle.sub(min).div(step).toNumber());\n  var upCount = Math.ceil(new _decimal.default(max).sub(middle).div(step).toNumber());\n  var scaleCount = belowCount + upCount + 1;\n\n  if (scaleCount > tickCount) {\n    // When more ticks need to cover the interval, step should be bigger.\n    return calculateStep(min, max, tickCount, allowDecimals, correctionFactor + 1);\n  }\n\n  if (scaleCount < tickCount) {\n    // When less ticks can cover the interval, we should add some additional ticks\n    upCount = max > 0 ? upCount + (tickCount - scaleCount) : upCount;\n    belowCount = max > 0 ? belowCount : belowCount + (tickCount - scaleCount);\n  }\n\n  return {\n    step: step,\n    tickMin: middle.sub(new _decimal.default(belowCount).mul(step)),\n    tickMax: middle.add(new _decimal.default(upCount).mul(step))\n  };\n}\n/**\n * Calculate the ticks of an interval, the count of ticks will be guraranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\n\n\nfunction getNiceTickValuesFn(_ref3) {\n  var _ref4 = _slicedToArray(_ref3, 2),\n      min = _ref4[0],\n      max = _ref4[1];\n\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n\n  var _getValidInterval = getValidInterval([min, max]),\n      _getValidInterval2 = _slicedToArray(_getValidInterval, 2),\n      cormin = _getValidInterval2[0],\n      cormax = _getValidInterval2[1];\n\n  if (cormin === -Infinity || cormax === Infinity) {\n    var _values = cormax === Infinity ? [cormin].concat(_toConsumableArray((0, _utils.range)(0, tickCount - 1).map(function () {\n      return Infinity;\n    }))) : [].concat(_toConsumableArray((0, _utils.range)(0, tickCount - 1).map(function () {\n      return -Infinity;\n    })), [cormax]);\n\n    return min > max ? (0, _utils.reverse)(_values) : _values;\n  }\n\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  } // Get the step between two ticks\n\n\n  var _calculateStep = calculateStep(cormin, cormax, count, allowDecimals),\n      step = _calculateStep.step,\n      tickMin = _calculateStep.tickMin,\n      tickMax = _calculateStep.tickMax;\n\n  var values = _arithmetic.default.rangeStep(tickMin, tickMax.add(new _decimal.default(0.1).mul(step)), step);\n\n  return min > max ? (0, _utils.reverse)(values) : values;\n}\n/**\n * Calculate the ticks of an interval, the count of ticks won't be guraranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\n\n\nfunction getTickValuesFn(_ref5) {\n  var _ref6 = _slicedToArray(_ref5, 2),\n      min = _ref6[0],\n      max = _ref6[1];\n\n  var tickCount = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 6;\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  // More than two ticks should be return\n  var count = Math.max(tickCount, 2);\n\n  var _getValidInterval3 = getValidInterval([min, max]),\n      _getValidInterval4 = _slicedToArray(_getValidInterval3, 2),\n      cormin = _getValidInterval4[0],\n      cormax = _getValidInterval4[1];\n\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n\n  if (cormin === cormax) {\n    return getTickOfSingleValue(cormin, tickCount, allowDecimals);\n  }\n\n  var step = getFormatStep(new _decimal.default(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var fn = (0, _utils.compose)((0, _utils.map)(function (n) {\n    return new _decimal.default(cormin).add(new _decimal.default(n).mul(step)).toNumber();\n  }), _utils.range);\n  var values = fn(0, count).filter(function (entry) {\n    return entry >= cormin && entry <= cormax;\n  });\n  return min > max ? (0, _utils.reverse)(values) : values;\n}\n/**\n * Calculate the ticks of an interval, the count of ticks won't be guraranteed,\n * but the domain will be guaranteed\n *\n * @param  {Number}  min, max      min: The minimum value, max: The maximum value\n * @param  {Integer} tickCount     The count of ticks\n * @param  {Boolean} allowDecimals Allow the ticks to be decimals or not\n * @return {Array}   ticks\n */\n\n\nfunction getTickValuesFixedDomainFn(_ref7, tickCount) {\n  var _ref8 = _slicedToArray(_ref7, 2),\n      min = _ref8[0],\n      max = _ref8[1];\n\n  var allowDecimals = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n\n  // More than two ticks should be return\n  var _getValidInterval5 = getValidInterval([min, max]),\n      _getValidInterval6 = _slicedToArray(_getValidInterval5, 2),\n      cormin = _getValidInterval6[0],\n      cormax = _getValidInterval6[1];\n\n  if (cormin === -Infinity || cormax === Infinity) {\n    return [min, max];\n  }\n\n  if (cormin === cormax) {\n    return [cormin];\n  }\n\n  var count = Math.max(tickCount, 2);\n  var step = getFormatStep(new _decimal.default(cormax).sub(cormin).div(count - 1), allowDecimals, 0);\n  var values = [].concat(_toConsumableArray(_arithmetic.default.rangeStep(new _decimal.default(cormin), new _decimal.default(cormax).sub(new _decimal.default(0.99).mul(step)), step)), [cormax]);\n  return min > max ? (0, _utils.reverse)(values) : values;\n}\n\nvar getNiceTickValues = (0, _utils.memoize)(getNiceTickValuesFn);\nexports.getNiceTickValues = getNiceTickValues;\nvar getTickValues = (0, _utils.memoize)(getTickValuesFn);\nexports.getTickValues = getTickValues;\nvar getTickValuesFixedDomain = (0, _utils.memoize)(getTickValuesFixedDomainFn);\nexports.getTickValuesFixedDomain = getTickValuesFixedDomain;\n\n//# sourceURL=webpack://recharts-scale/./src/getNiceTickValues.js?");

/***/ }),

/***/ "./src/index.js":
/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"getTickValues\", ({\n  enumerable: true,\n  get: function get() {\n    return _getNiceTickValues.getTickValues;\n  }\n}));\nObject.defineProperty(exports, \"getNiceTickValues\", ({\n  enumerable: true,\n  get: function get() {\n    return _getNiceTickValues.getNiceTickValues;\n  }\n}));\nObject.defineProperty(exports, \"getTickValuesFixedDomain\", ({\n  enumerable: true,\n  get: function get() {\n    return _getNiceTickValues.getTickValuesFixedDomain;\n  }\n}));\n\nvar _getNiceTickValues = __webpack_require__(/*! ./getNiceTickValues */ \"./src/getNiceTickValues.js\");\n\n//# sourceURL=webpack://recharts-scale/./src/index.js?");

/***/ }),

/***/ "./src/util/arithmetic.js":
/*!********************************!*\
  !*** ./src/util/arithmetic.js ***!
  \********************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.default = void 0;\n\nvar _decimal = _interopRequireDefault(__webpack_require__(/*! decimal.js-light */ \"./node_modules/decimal.js-light/decimal.js\"));\n\nvar _utils = __webpack_require__(/*! ./utils */ \"./src/util/utils.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @fileOverview 一些公用的运算方法\n * <AUTHOR> * @date 2015-09-17\n */\n\n/**\n * 获取数值的位数\n * 其中绝对值属于区间[0.1, 1)， 得到的值为0\n * 绝对值属于区间[0.01, 0.1)，得到的位数为 -1\n * 绝对值属于区间[0.001, 0.01)，得到的位数为 -2\n *\n * @param  {Number} value 数值\n * @return {Integer} 位数\n */\nfunction getDigitCount(value) {\n  var result;\n\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new _decimal.default(value).abs().log(10).toNumber()) + 1;\n  }\n\n  return result;\n}\n/**\n * 按照固定的步长获取[start, end)这个区间的数据\n * 并且需要处理js计算精度的问题\n *\n * @param  {Decimal} start 起点\n * @param  {Decimal} end   终点，不包含该值\n * @param  {Decimal} step  步长\n * @return {Array}         若干数值\n */\n\n\nfunction rangeStep(start, end, step) {\n  var num = new _decimal.default(start);\n  var i = 0;\n  var result = []; // magic number to prevent infinite loop\n\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n\n  return result;\n}\n/**\n * 对数值进行线性插值\n *\n * @param  {Number} a  定义域的极点\n * @param  {Number} b  定义域的极点\n * @param  {Number} t  [0, 1]内的某个值\n * @return {Number}    定义域内的某个值\n */\n\n\nvar interpolateNumber = (0, _utils.curry)(function (a, b, t) {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n/**\n * 线性插值的逆运算\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个范围内时，返回值属于[0, 1]\n */\n\nvar uninterpolateNumber = (0, _utils.curry)(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n/**\n * 线性插值的逆运算，并且有截断的操作\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个区间内时，返回值属于[0, 1]，\n * 当x不在 a ~ b这个区间时，会截断到 a ~ b 这个区间\n */\n\nvar uninterpolateTruncation = (0, _utils.curry)(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\nvar _default = {\n  rangeStep: rangeStep,\n  getDigitCount: getDigitCount,\n  interpolateNumber: interpolateNumber,\n  uninterpolateNumber: uninterpolateNumber,\n  uninterpolateTruncation: uninterpolateTruncation\n};\nexports.default = _default;\n\n//# sourceURL=webpack://recharts-scale/./src/util/arithmetic.js?");

/***/ }),

/***/ "./src/util/utils.js":
/*!***************************!*\
  !*** ./src/util/utils.js ***!
  \***************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.memoize = exports.reverse = exports.compose = exports.map = exports.range = exports.curry = exports.PLACE_HOLDER = void 0;\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nvar identity = function identity(i) {\n  return i;\n};\n\nvar PLACE_HOLDER = {\n  '@@functional/placeholder': true\n};\nexports.PLACE_HOLDER = PLACE_HOLDER;\n\nvar isPlaceHolder = function isPlaceHolder(val) {\n  return val === PLACE_HOLDER;\n};\n\nvar curry0 = function curry0(fn) {\n  return function _curried() {\n    if (arguments.length === 0 || arguments.length === 1 && isPlaceHolder(arguments.length <= 0 ? undefined : arguments[0])) {\n      return _curried;\n    }\n\n    return fn.apply(void 0, arguments);\n  };\n};\n\nvar curryN = function curryN(n, fn) {\n  if (n === 1) {\n    return fn;\n  }\n\n  return curry0(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    var argsLength = args.filter(function (arg) {\n      return arg !== PLACE_HOLDER;\n    }).length;\n\n    if (argsLength >= n) {\n      return fn.apply(void 0, args);\n    }\n\n    return curryN(n - argsLength, curry0(function () {\n      for (var _len2 = arguments.length, restArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        restArgs[_key2] = arguments[_key2];\n      }\n\n      var newArgs = args.map(function (arg) {\n        return isPlaceHolder(arg) ? restArgs.shift() : arg;\n      });\n      return fn.apply(void 0, _toConsumableArray(newArgs).concat(restArgs));\n    }));\n  });\n};\n\nvar curry = function curry(fn) {\n  return curryN(fn.length, fn);\n};\n\nexports.curry = curry;\n\nvar range = function range(begin, end) {\n  var arr = [];\n\n  for (var i = begin; i < end; ++i) {\n    arr[i - begin] = i;\n  }\n\n  return arr;\n};\n\nexports.range = range;\nvar map = curry(function (fn, arr) {\n  if (Array.isArray(arr)) {\n    return arr.map(fn);\n  }\n\n  return Object.keys(arr).map(function (key) {\n    return arr[key];\n  }).map(fn);\n});\nexports.map = map;\n\nvar compose = function compose() {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n\n  if (!args.length) {\n    return identity;\n  }\n\n  var fns = args.reverse(); // first function can receive multiply arguments\n\n  var firstFn = fns[0];\n  var tailsFn = fns.slice(1);\n  return function () {\n    return tailsFn.reduce(function (res, fn) {\n      return fn(res);\n    }, firstFn.apply(void 0, arguments));\n  };\n};\n\nexports.compose = compose;\n\nvar reverse = function reverse(arr) {\n  if (Array.isArray(arr)) {\n    return arr.reverse();\n  } // can be string\n\n\n  return arr.split('').reverse.join('');\n};\n\nexports.reverse = reverse;\n\nvar memoize = function memoize(fn) {\n  var lastArgs = null;\n  var lastResult = null;\n  return function () {\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n\n    if (lastArgs && args.every(function (val, i) {\n      return val === lastArgs[i];\n    })) {\n      return lastResult;\n    }\n\n    lastArgs = args;\n    lastResult = fn.apply(void 0, args);\n    return lastResult;\n  };\n};\n\nexports.memoize = memoize;\n\n//# sourceURL=webpack://recharts-scale/./src/util/utils.js?");

/***/ }),

/***/ "./node_modules/decimal.js-light/decimal.js":
/*!**************************************************!*\
  !*** ./node_modules/decimal.js-light/decimal.js ***!
  \**************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */\r\n;(function (globalScope) {\r\n  'use strict';\r\n\r\n\r\n  /*\r\n   *  decimal.js-light v2.5.1\r\n   *  An arbitrary-precision Decimal type for JavaScript.\r\n   *  https://github.com/MikeMcl/decimal.js-light\r\n   *  Copyright (c) 2020 Michael Mclaughlin <<EMAIL>>\r\n   *  MIT Expat Licence\r\n   */\r\n\r\n\r\n  // -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\r\n\r\n\r\n    // The limit on the value of `precision`, and on the value of the first argument to\r\n    // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n  var MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n\r\n    // The initial configuration properties of the Decimal constructor.\r\n    Decimal = {\r\n\r\n      // These values must be integers within the stated ranges (inclusive).\r\n      // Most of these values can be changed during run-time using `Decimal.config`.\r\n\r\n      // The maximum number of significant digits of the result of a calculation or base conversion.\r\n      // E.g. `Decimal.config({ precision: 20 });`\r\n      precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n      // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\r\n      // `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n      //\r\n      // ROUND_UP         0 Away from zero.\r\n      // ROUND_DOWN       1 Towards zero.\r\n      // ROUND_CEIL       2 Towards +Infinity.\r\n      // ROUND_FLOOR      3 Towards -Infinity.\r\n      // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n      // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n      // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n      // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n      // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n      //\r\n      // E.g.\r\n      // `Decimal.rounding = 4;`\r\n      // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n      rounding: 4,                           // 0 to 8\r\n\r\n      // The exponent value at and beneath which `toString` returns exponential notation.\r\n      // JavaScript numbers: -7\r\n      toExpNeg: -7,                          // 0 to -MAX_E\r\n\r\n      // The exponent value at and above which `toString` returns exponential notation.\r\n      // JavaScript numbers: 21\r\n      toExpPos:  21,                         // 0 to MAX_E\r\n\r\n      // The natural logarithm of 10.\r\n      // 115 digits\r\n      LN10: '2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286'\r\n    },\r\n\r\n\r\n  // ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\r\n\r\n\r\n    external = true,\r\n\r\n    decimalError = '[DecimalError] ',\r\n    invalidArgument = decimalError + 'Invalid argument: ',\r\n    exponentOutOfRange = decimalError + 'Exponent out of range: ',\r\n\r\n    mathfloor = Math.floor,\r\n    mathpow = Math.pow,\r\n\r\n    isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n    ONE,\r\n    BASE = 1e7,\r\n    LOG_BASE = 7,\r\n    MAX_SAFE_INTEGER = 9007199254740991,\r\n    MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE),    // 1286742750677284\r\n\r\n    // Decimal.prototype object\r\n    P = {};\r\n\r\n\r\n  // Decimal prototype methods\r\n\r\n\r\n  /*\r\n   *  absoluteValue                       abs\r\n   *  comparedTo                          cmp\r\n   *  decimalPlaces                       dp\r\n   *  dividedBy                           div\r\n   *  dividedToIntegerBy                  idiv\r\n   *  equals                              eq\r\n   *  exponent\r\n   *  greaterThan                         gt\r\n   *  greaterThanOrEqualTo                gte\r\n   *  isInteger                           isint\r\n   *  isNegative                          isneg\r\n   *  isPositive                          ispos\r\n   *  isZero\r\n   *  lessThan                            lt\r\n   *  lessThanOrEqualTo                   lte\r\n   *  logarithm                           log\r\n   *  minus                               sub\r\n   *  modulo                              mod\r\n   *  naturalExponential                  exp\r\n   *  naturalLogarithm                    ln\r\n   *  negated                             neg\r\n   *  plus                                add\r\n   *  precision                           sd\r\n   *  squareRoot                          sqrt\r\n   *  times                               mul\r\n   *  toDecimalPlaces                     todp\r\n   *  toExponential\r\n   *  toFixed\r\n   *  toInteger                           toint\r\n   *  toNumber\r\n   *  toPower                             pow\r\n   *  toPrecision\r\n   *  toSignificantDigits                 tosd\r\n   *  toString\r\n   *  valueOf                             val\r\n   */\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the absolute value of this Decimal.\r\n   *\r\n   */\r\n  P.absoluteValue = P.abs = function () {\r\n    var x = new this.constructor(this);\r\n    if (x.s) x.s = 1;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return\r\n   *   1    if the value of this Decimal is greater than the value of `y`,\r\n   *  -1    if the value of this Decimal is less than the value of `y`,\r\n   *   0    if they have the same value\r\n   *\r\n   */\r\n  P.comparedTo = P.cmp = function (y) {\r\n    var i, j, xdL, ydL,\r\n      x = this;\r\n\r\n    y = new x.constructor(y);\r\n\r\n    // Signs differ?\r\n    if (x.s !== y.s) return x.s || -y.s;\r\n\r\n    // Compare exponents.\r\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\r\n\r\n    xdL = x.d.length;\r\n    ydL = y.d.length;\r\n\r\n    // Compare digit by digit.\r\n    for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n      if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\r\n    }\r\n\r\n    // Compare lengths.\r\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the number of decimal places of the value of this Decimal.\r\n   *\r\n   */\r\n  P.decimalPlaces = P.dp = function () {\r\n    var x = this,\r\n      w = x.d.length - 1,\r\n      dp = (w - x.e) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    w = x.d[w];\r\n    if (w) for (; w % 10 == 0; w /= 10) dp--;\r\n\r\n    return dp < 0 ? 0 : dp;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.dividedBy = P.div = function (y) {\r\n    return divide(this, new this.constructor(y));\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n   * by the value of `y`, truncated to `precision` significant digits.\r\n   *\r\n   */\r\n  P.dividedToIntegerBy = P.idiv = function (y) {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n   *\r\n   */\r\n  P.equals = P.eq = function (y) {\r\n    return !this.cmp(y);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n   *\r\n   */\r\n  P.exponent = function () {\r\n    return getBase10Exponent(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n   * false.\r\n   *\r\n   */\r\n  P.greaterThan = P.gt = function (y) {\r\n    return this.cmp(y) > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n   * otherwise return false.\r\n   *\r\n   */\r\n  P.greaterThanOrEqualTo = P.gte = function (y) {\r\n    return this.cmp(y) >= 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is an integer, otherwise return false.\r\n   *\r\n   */\r\n  P.isInteger = P.isint = function () {\r\n    return this.e > this.d.length - 2;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is negative, otherwise return false.\r\n   *\r\n   */\r\n  P.isNegative = P.isneg = function () {\r\n    return this.s < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is positive, otherwise return false.\r\n   *\r\n   */\r\n  P.isPositive = P.ispos = function () {\r\n    return this.s > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is 0, otherwise return false.\r\n   *\r\n   */\r\n  P.isZero = function () {\r\n    return this.s === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n   *\r\n   */\r\n  P.lessThan = P.lt = function (y) {\r\n    return this.cmp(y) < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n   *\r\n   */\r\n  P.lessThanOrEqualTo = P.lte = function (y) {\r\n    return this.cmp(y) < 1;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   * If no base is specified, return log[10](x).\r\n   *\r\n   * log[base](x) = ln(x) / ln(base)\r\n   *\r\n   * The maximum error of the result is 1 ulp (unit in the last place).\r\n   *\r\n   * [base] {number|string|Decimal} The base of the logarithm.\r\n   *\r\n   */\r\n  P.logarithm = P.log = function (base) {\r\n    var r,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision,\r\n      wpr = pr + 5;\r\n\r\n    // Default base is 10.\r\n    if (base === void 0) {\r\n      base = new Ctor(10);\r\n    } else {\r\n      base = new Ctor(base);\r\n\r\n      // log[-b](x) = NaN\r\n      // log[0](x)  = NaN\r\n      // log[1](x)  = NaN\r\n      if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + 'NaN');\r\n    }\r\n\r\n    // log[b](-x) = NaN\r\n    // log[b](0) = -Infinity\r\n    if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\r\n\r\n    // log[b](1) = 0\r\n    if (x.eq(ONE)) return new Ctor(0);\r\n\r\n    external = false;\r\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\r\n    external = true;\r\n\r\n    return round(r, pr);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.minus = P.sub = function (y) {\r\n    var x = this;\r\n    y = new x.constructor(y);\r\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.modulo = P.mod = function (y) {\r\n    var q,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    y = new Ctor(y);\r\n\r\n    // x % 0 = NaN\r\n    if (!y.s) throw Error(decimalError + 'NaN');\r\n\r\n    // Return x if x is 0.\r\n    if (!x.s) return round(new Ctor(x), pr);\r\n\r\n    // Prevent rounding of intermediate calculations.\r\n    external = false;\r\n    q = divide(x, y, 0, 1).times(y);\r\n    external = true;\r\n\r\n    return x.minus(q);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n   * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n   * significant digits.\r\n   *\r\n   */\r\n  P.naturalExponential = P.exp = function () {\r\n    return exp(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n   * truncated to `precision` significant digits.\r\n   *\r\n   */\r\n  P.naturalLogarithm = P.ln = function () {\r\n    return ln(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n   * -1.\r\n   *\r\n   */\r\n  P.negated = P.neg = function () {\r\n    var x = new this.constructor(this);\r\n    x.s = -x.s || 0;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.plus = P.add = function (y) {\r\n    var x = this;\r\n    y = new x.constructor(y);\r\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the number of significant digits of the value of this Decimal.\r\n   *\r\n   * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n   *\r\n   */\r\n  P.precision = P.sd = function (z) {\r\n    var e, sd, w,\r\n      x = this;\r\n\r\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n    e = getBase10Exponent(x) + 1;\r\n    w = x.d.length - 1;\r\n    sd = w * LOG_BASE + 1;\r\n    w = x.d[w];\r\n\r\n    // If non-zero...\r\n    if (w) {\r\n\r\n      // Subtract the number of trailing zeros of the last word.\r\n      for (; w % 10 == 0; w /= 10) sd--;\r\n\r\n      // Add the number of digits of the first word.\r\n      for (w = x.d[0]; w >= 10; w /= 10) sd++;\r\n    }\r\n\r\n    return z && e > sd ? e : sd;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n   * significant digits.\r\n   *\r\n   */\r\n  P.squareRoot = P.sqrt = function () {\r\n    var e, n, pr, r, s, t, wpr,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    // Negative or zero?\r\n    if (x.s < 1) {\r\n      if (!x.s) return new Ctor(0);\r\n\r\n      // sqrt(-x) = NaN\r\n      throw Error(decimalError + 'NaN');\r\n    }\r\n\r\n    e = getBase10Exponent(x);\r\n    external = false;\r\n\r\n    // Initial estimate.\r\n    s = Math.sqrt(+x);\r\n\r\n    // Math.sqrt underflow/overflow?\r\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n    if (s == 0 || s == 1 / 0) {\r\n      n = digitsToString(x.d);\r\n      if ((n.length + e) % 2 == 0) n += '0';\r\n      s = Math.sqrt(n);\r\n      e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n      if (s == 1 / 0) {\r\n        n = '5e' + e;\r\n      } else {\r\n        n = s.toExponential();\r\n        n = n.slice(0, n.indexOf('e') + 1) + e;\r\n      }\r\n\r\n      r = new Ctor(n);\r\n    } else {\r\n      r = new Ctor(s.toString());\r\n    }\r\n\r\n    pr = Ctor.precision;\r\n    s = wpr = pr + 3;\r\n\r\n    // Newton-Raphson iteration.\r\n    for (;;) {\r\n      t = r;\r\n      r = t.plus(divide(x, t, wpr + 2)).times(0.5);\r\n\r\n      if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\r\n        n = n.slice(wpr - 3, wpr + 1);\r\n\r\n        // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n        // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n        if (s == wpr && n == '4999') {\r\n\r\n          // On the first iteration only, check to see if rounding up gives the exact result as the\r\n          // nines may infinitely repeat.\r\n          round(t, pr + 1, 0);\r\n\r\n          if (t.times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        } else if (n != '9999') {\r\n          break;\r\n        }\r\n\r\n        wpr += 4;\r\n      }\r\n    }\r\n\r\n    external = true;\r\n\r\n    return round(r, pr);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.times = P.mul = function (y) {\r\n    var carry, e, i, k, r, rL, t, xdL, ydL,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      xd = x.d,\r\n      yd = (y = new Ctor(y)).d;\r\n\r\n    // Return 0 if either is 0.\r\n    if (!x.s || !y.s) return new Ctor(0);\r\n\r\n    y.s *= x.s;\r\n    e = x.e + y.e;\r\n    xdL = xd.length;\r\n    ydL = yd.length;\r\n\r\n    // Ensure xd points to the longer array.\r\n    if (xdL < ydL) {\r\n      r = xd;\r\n      xd = yd;\r\n      yd = r;\r\n      rL = xdL;\r\n      xdL = ydL;\r\n      ydL = rL;\r\n    }\r\n\r\n    // Initialise the result array with zeros.\r\n    r = [];\r\n    rL = xdL + ydL;\r\n    for (i = rL; i--;) r.push(0);\r\n\r\n    // Multiply!\r\n    for (i = ydL; --i >= 0;) {\r\n      carry = 0;\r\n      for (k = xdL + i; k > i;) {\r\n        t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n        r[k--] = t % BASE | 0;\r\n        carry = t / BASE | 0;\r\n      }\r\n\r\n      r[k] = (r[k] + carry) % BASE | 0;\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (; !r[--rL];) r.pop();\r\n\r\n    if (carry) ++e;\r\n    else r.shift();\r\n\r\n    y.d = r;\r\n    y.e = e;\r\n\r\n    return external ? round(y, Ctor.precision) : y;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n   * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n   *\r\n   * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toDecimalPlaces = P.todp = function (dp, rm) {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n\r\n    x = new Ctor(x);\r\n    if (dp === void 0) return x;\r\n\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal in exponential notation rounded to\r\n   * `dp` fixed decimal places using rounding mode `rounding`.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toExponential = function (dp, rm) {\r\n    var str,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (dp === void 0) {\r\n      str = toString(x, true);\r\n    } else {\r\n      checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n      if (rm === void 0) rm = Ctor.rounding;\r\n      else checkInt32(rm, 0, 8);\r\n\r\n      x = round(new Ctor(x), dp + 1, rm);\r\n      str = toString(x, true, dp + 1);\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n   * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n   * omitted.\r\n   *\r\n   * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n   * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n   * (-0).toFixed(3) is '0.000'.\r\n   * (-0.5).toFixed(0) is '-0'.\r\n   *\r\n   */\r\n  P.toFixed = function (dp, rm) {\r\n    var str, y,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (dp === void 0) return toString(x);\r\n\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\r\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\r\n\r\n    // To determine whether to add the minus sign look at the value before it was rounded,\r\n    // i.e. look at `x` rather than `y`.\r\n    return x.isneg() && !x.isZero() ? '-' + str : str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n   * rounding mode `rounding`.\r\n   *\r\n   */\r\n  P.toInteger = P.toint = function () {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the value of this Decimal converted to a number primitive.\r\n   *\r\n   */\r\n  P.toNumber = function () {\r\n    return +this;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n   * truncated to `precision` significant digits.\r\n   *\r\n   * For non-integer or very large exponents pow(x, y) is calculated using\r\n   *\r\n   *   x^y = exp(y*ln(x))\r\n   *\r\n   * The maximum error is 1 ulp (unit in last place).\r\n   *\r\n   * y {number|string|Decimal} The power to which to raise this Decimal.\r\n   *\r\n   */\r\n  P.toPower = P.pow = function (y) {\r\n    var e, k, pr, r, sign, yIsInt,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      guard = 12,\r\n      yn = +(y = new Ctor(y));\r\n\r\n    // pow(x, 0) = 1\r\n    if (!y.s) return new Ctor(ONE);\r\n\r\n    x = new Ctor(x);\r\n\r\n    // pow(0, y > 0) = 0\r\n    // pow(0, y < 0) = Infinity\r\n    if (!x.s) {\r\n      if (y.s < 1) throw Error(decimalError + 'Infinity');\r\n      return x;\r\n    }\r\n\r\n    // pow(1, y) = 1\r\n    if (x.eq(ONE)) return x;\r\n\r\n    pr = Ctor.precision;\r\n\r\n    // pow(x, 1) = x\r\n    if (y.eq(ONE)) return round(x, pr);\r\n\r\n    e = y.e;\r\n    k = y.d.length - 1;\r\n    yIsInt = e >= k;\r\n    sign = x.s;\r\n\r\n    if (!yIsInt) {\r\n\r\n      // pow(x < 0, y non-integer) = NaN\r\n      if (sign < 0) throw Error(decimalError + 'NaN');\r\n\r\n    // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n      r = new Ctor(ONE);\r\n\r\n      // Max k of 9007199254740991 takes 53 loop iterations.\r\n      // Maximum digits array length; leaves [28, 34] guard digits.\r\n      e = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n      external = false;\r\n\r\n      for (;;) {\r\n        if (k % 2) {\r\n          r = r.times(x);\r\n          truncate(r.d, e);\r\n        }\r\n\r\n        k = mathfloor(k / 2);\r\n        if (k === 0) break;\r\n\r\n        x = x.times(x);\r\n        truncate(x.d, e);\r\n      }\r\n\r\n      external = true;\r\n\r\n      return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\r\n    }\r\n\r\n    // Result is negative if x is negative and the last digit of integer y is odd.\r\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\r\n\r\n    x.s = 1;\r\n    external = false;\r\n    r = y.times(ln(x, pr + guard));\r\n    external = true;\r\n    r = exp(r);\r\n    r.s = sign;\r\n\r\n    return r;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n   * using rounding mode `rounding`.\r\n   *\r\n   * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n   * the integer part of the value in normal notation.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toPrecision = function (sd, rm) {\r\n    var e, str,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (sd === void 0) {\r\n      e = getBase10Exponent(x);\r\n      str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\r\n    } else {\r\n      checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n      if (rm === void 0) rm = Ctor.rounding;\r\n      else checkInt32(rm, 0, 8);\r\n\r\n      x = round(new Ctor(x), sd, rm);\r\n      e = getBase10Exponent(x);\r\n      str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n   * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n   * omitted.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toSignificantDigits = P.tosd = function (sd, rm) {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (sd === void 0) {\r\n      sd = Ctor.precision;\r\n      rm = Ctor.rounding;\r\n    } else {\r\n      checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n      if (rm === void 0) rm = Ctor.rounding;\r\n      else checkInt32(rm, 0, 8);\r\n    }\r\n\r\n    return round(new Ctor(x), sd, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal.\r\n   *\r\n   * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n   * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n   *\r\n   */\r\n  P.toString = P.valueOf = P.val = P.toJSON = function () {\r\n    var x = this,\r\n      e = getBase10Exponent(x),\r\n      Ctor = x.constructor;\r\n\r\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\r\n  };\r\n\r\n\r\n  // Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n  /*\r\n   *  add                 P.minus, P.plus\r\n   *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n   *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n   *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n   *  exp                 P.exp, P.pow\r\n   *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n   *                      P.toString, divide, round, toString, exp, ln\r\n   *  getLn10             P.log, ln\r\n   *  getZeroString       digitsToString, toString\r\n   *  ln                  P.log, P.ln, P.pow, exp\r\n   *  parseDecimal        Decimal\r\n   *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n   *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n   *                      divide, getLn10, exp, ln\r\n   *  subtract            P.minus, P.plus\r\n   *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n   *  truncate            P.pow\r\n   *\r\n   *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n   *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n   */\r\n\r\n\r\n  function add(x, y) {\r\n    var carry, d, e, i, k, len, xd, yd,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    // If either is zero...\r\n    if (!x.s || !y.s) {\r\n\r\n      // Return x if y is zero.\r\n      // Return y if y is non-zero.\r\n      if (!y.s) y = new Ctor(x);\r\n      return external ? round(y, pr) : y;\r\n    }\r\n\r\n    xd = x.d;\r\n    yd = y.d;\r\n\r\n    // x and y are finite, non-zero numbers with the same sign.\r\n\r\n    k = x.e;\r\n    e = y.e;\r\n    xd = xd.slice();\r\n    i = k - e;\r\n\r\n    // If base 1e7 exponents differ...\r\n    if (i) {\r\n      if (i < 0) {\r\n        d = xd;\r\n        i = -i;\r\n        len = yd.length;\r\n      } else {\r\n        d = yd;\r\n        e = k;\r\n        len = xd.length;\r\n      }\r\n\r\n      // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n      k = Math.ceil(pr / LOG_BASE);\r\n      len = k > len ? k + 1 : len + 1;\r\n\r\n      if (i > len) {\r\n        i = len;\r\n        d.length = 1;\r\n      }\r\n\r\n      // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n      d.reverse();\r\n      for (; i--;) d.push(0);\r\n      d.reverse();\r\n    }\r\n\r\n    len = xd.length;\r\n    i = yd.length;\r\n\r\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n    if (len - i < 0) {\r\n      i = len;\r\n      d = yd;\r\n      yd = xd;\r\n      xd = d;\r\n    }\r\n\r\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n    for (carry = 0; i;) {\r\n      carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n      xd[i] %= BASE;\r\n    }\r\n\r\n    if (carry) {\r\n      xd.unshift(carry);\r\n      ++e;\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n    for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n    y.d = xd;\r\n    y.e = e;\r\n\r\n    return external ? round(y, pr) : y;\r\n  }\r\n\r\n\r\n  function checkInt32(i, min, max) {\r\n    if (i !== ~~i || i < min || i > max) {\r\n      throw Error(invalidArgument + i);\r\n    }\r\n  }\r\n\r\n\r\n  function digitsToString(d) {\r\n    var i, k, ws,\r\n      indexOfLastWord = d.length - 1,\r\n      str = '',\r\n      w = d[0];\r\n\r\n    if (indexOfLastWord > 0) {\r\n      str += w;\r\n      for (i = 1; i < indexOfLastWord; i++) {\r\n        ws = d[i] + '';\r\n        k = LOG_BASE - ws.length;\r\n        if (k) str += getZeroString(k);\r\n        str += ws;\r\n      }\r\n\r\n      w = d[i];\r\n      ws = w + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n    } else if (w === 0) {\r\n      return '0';\r\n    }\r\n\r\n    // Remove trailing zeros of last w.\r\n    for (; w % 10 === 0;) w /= 10;\r\n\r\n    return str + w;\r\n  }\r\n\r\n\r\n  var divide = (function () {\r\n\r\n    // Assumes non-zero x and k, and hence non-zero result.\r\n    function multiplyInteger(x, k) {\r\n      var temp,\r\n        carry = 0,\r\n        i = x.length;\r\n\r\n      for (x = x.slice(); i--;) {\r\n        temp = x[i] * k + carry;\r\n        x[i] = temp % BASE | 0;\r\n        carry = temp / BASE | 0;\r\n      }\r\n\r\n      if (carry) x.unshift(carry);\r\n\r\n      return x;\r\n    }\r\n\r\n    function compare(a, b, aL, bL) {\r\n      var i, r;\r\n\r\n      if (aL != bL) {\r\n        r = aL > bL ? 1 : -1;\r\n      } else {\r\n        for (i = r = 0; i < aL; i++) {\r\n          if (a[i] != b[i]) {\r\n            r = a[i] > b[i] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return r;\r\n    }\r\n\r\n    function subtract(a, b, aL) {\r\n      var i = 0;\r\n\r\n      // Subtract b from a.\r\n      for (; aL--;) {\r\n        a[aL] -= i;\r\n        i = a[aL] < b[aL] ? 1 : 0;\r\n        a[aL] = i * BASE + a[aL] - b[aL];\r\n      }\r\n\r\n      // Remove leading zeros.\r\n      for (; !a[0] && a.length > 1;) a.shift();\r\n    }\r\n\r\n    return function (x, y, pr, dp) {\r\n      var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz,\r\n        Ctor = x.constructor,\r\n        sign = x.s == y.s ? 1 : -1,\r\n        xd = x.d,\r\n        yd = y.d;\r\n\r\n      // Either 0?\r\n      if (!x.s) return new Ctor(x);\r\n      if (!y.s) throw Error(decimalError + 'Division by zero');\r\n\r\n      e = x.e - y.e;\r\n      yL = yd.length;\r\n      xL = xd.length;\r\n      q = new Ctor(sign);\r\n      qd = q.d = [];\r\n\r\n      // Result exponent may be one less than e.\r\n      for (i = 0; yd[i] == (xd[i] || 0); ) ++i;\r\n      if (yd[i] > (xd[i] || 0)) --e;\r\n\r\n      if (pr == null) {\r\n        sd = pr = Ctor.precision;\r\n      } else if (dp) {\r\n        sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\r\n      } else {\r\n        sd = pr;\r\n      }\r\n\r\n      if (sd < 0) return new Ctor(0);\r\n\r\n      // Convert precision in number of base 10 digits to base 1e7 digits.\r\n      sd = sd / LOG_BASE + 2 | 0;\r\n      i = 0;\r\n\r\n      // divisor < 1e7\r\n      if (yL == 1) {\r\n        k = 0;\r\n        yd = yd[0];\r\n        sd++;\r\n\r\n        // k is the carry.\r\n        for (; (i < xL || k) && sd--; i++) {\r\n          t = k * BASE + (xd[i] || 0);\r\n          qd[i] = t / yd | 0;\r\n          k = t % yd | 0;\r\n        }\r\n\r\n      // divisor >= 1e7\r\n      } else {\r\n\r\n        // Normalise xd and yd so highest order digit of yd is >= BASE/2\r\n        k = BASE / (yd[0] + 1) | 0;\r\n\r\n        if (k > 1) {\r\n          yd = multiplyInteger(yd, k);\r\n          xd = multiplyInteger(xd, k);\r\n          yL = yd.length;\r\n          xL = xd.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xd.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL;) rem[remL++] = 0;\r\n\r\n        yz = yd.slice();\r\n        yz.unshift(0);\r\n        yd0 = yd[0];\r\n\r\n        if (yd[1] >= BASE / 2) ++yd0;\r\n\r\n        do {\r\n          k = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yd, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, k.\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\r\n\r\n            // k will be how many times the divisor goes into the current remainder.\r\n            k = rem0 / yd0 | 0;\r\n\r\n            //  Algorithm:\r\n            //  1. product = divisor * trial digit (k)\r\n            //  2. if product > remainder: product -= divisor, k--\r\n            //  3. remainder -= product\r\n            //  4. if product was < remainder at 2:\r\n            //    5. compare new remainder and divisor\r\n            //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n            if (k > 1) {\r\n              if (k >= BASE) k = BASE - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiplyInteger(yd, k);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              cmp = compare(prod, rem, prodL, remL);\r\n\r\n              // product > remainder.\r\n              if (cmp == 1) {\r\n                k--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yd, prodL);\r\n              }\r\n            } else {\r\n\r\n              // cmp is -1.\r\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n              if (k == 0) cmp = k = 1;\r\n              prod = yd.slice();\r\n            }\r\n\r\n            prodL = prod.length;\r\n            if (prodL < remL) prod.unshift(0);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL);\r\n\r\n            // If product was < previous remainder.\r\n            if (cmp == -1) {\r\n              remL = rem.length;\r\n\r\n              // Compare divisor and new remainder.\r\n              cmp = compare(yd, rem, yL, remL);\r\n\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              if (cmp < 1) {\r\n                k++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yd, remL);\r\n              }\r\n            }\r\n\r\n            remL = rem.length;\r\n          } else if (cmp === 0) {\r\n            k++;\r\n            rem = [0];\r\n          }    // if cmp === 1, k will be 0\r\n\r\n          // Add the next digit, k, to the result array.\r\n          qd[i++] = k;\r\n\r\n          // Update the remainder.\r\n          if (cmp && rem[0]) {\r\n            rem[remL++] = xd[xi] || 0;\r\n          } else {\r\n            rem = [xd[xi]];\r\n            remL = 1;\r\n          }\r\n\r\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n      }\r\n\r\n      // Leading zero?\r\n      if (!qd[0]) qd.shift();\r\n\r\n      q.e = e;\r\n\r\n      return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\r\n    };\r\n  })();\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n   * significant digits.\r\n   *\r\n   * Taylor/Maclaurin series.\r\n   *\r\n   * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n   *\r\n   * Argument reduction:\r\n   *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n   *   exp(x) = exp(x / 2^k)^(2^k)\r\n   *\r\n   * Previously, the argument was initially reduced by\r\n   * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n   * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n   * found to be slower than just dividing repeatedly by 32 as above.\r\n   *\r\n   * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n   *\r\n   *  exp(x) is non-terminating for any finite, non-zero x.\r\n   *\r\n   */\r\n  function exp(x, sd) {\r\n    var denominator, guard, pow, sum, t, wpr,\r\n      i = 0,\r\n      k = 0,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\r\n\r\n    // exp(0) = 1\r\n    if (!x.s) return new Ctor(ONE);\r\n\r\n    if (sd == null) {\r\n      external = false;\r\n      wpr = pr;\r\n    } else {\r\n      wpr = sd;\r\n    }\r\n\r\n    t = new Ctor(0.03125);\r\n\r\n    while (x.abs().gte(0.1)) {\r\n      x = x.times(t);    // x = x / 2^5\r\n      k += 5;\r\n    }\r\n\r\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\r\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n    wpr += guard;\r\n    denominator = pow = sum = new Ctor(ONE);\r\n    Ctor.precision = wpr;\r\n\r\n    for (;;) {\r\n      pow = round(pow.times(x), wpr);\r\n      denominator = denominator.times(++i);\r\n      t = sum.plus(divide(pow, denominator, wpr));\r\n\r\n      if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n        while (k--) sum = round(sum.times(sum), wpr);\r\n        Ctor.precision = pr;\r\n        return sd == null ? (external = true, round(sum, pr)) : sum;\r\n      }\r\n\r\n      sum = t;\r\n    }\r\n  }\r\n\r\n\r\n  // Calculate the base 10 exponent from the base 1e7 exponent.\r\n  function getBase10Exponent(x) {\r\n    var e = x.e * LOG_BASE,\r\n      w = x.d[0];\r\n\r\n    // Add the number of digits of the first word of the digits array.\r\n    for (; w >= 10; w /= 10) e++;\r\n    return e;\r\n  }\r\n\r\n\r\n  function getLn10(Ctor, sd, pr) {\r\n\r\n    if (sd > Ctor.LN10.sd()) {\r\n\r\n\r\n      // Reset global state in case the exception is caught.\r\n      external = true;\r\n      if (pr) Ctor.precision = pr;\r\n      throw Error(decimalError + 'LN10 precision limit exceeded');\r\n    }\r\n\r\n    return round(new Ctor(Ctor.LN10), sd);\r\n  }\r\n\r\n\r\n  function getZeroString(k) {\r\n    var zs = '';\r\n    for (; k--;) zs += '0';\r\n    return zs;\r\n  }\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n   * digits.\r\n   *\r\n   *  ln(n) is non-terminating (n != 1)\r\n   *\r\n   */\r\n  function ln(y, sd) {\r\n    var c, c0, denominator, e, numerator, sum, t, wpr, x2,\r\n      n = 1,\r\n      guard = 10,\r\n      x = y,\r\n      xd = x.d,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    // ln(-x) = NaN\r\n    // ln(0) = -Infinity\r\n    if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\r\n\r\n    // ln(1) = 0\r\n    if (x.eq(ONE)) return new Ctor(0);\r\n\r\n    if (sd == null) {\r\n      external = false;\r\n      wpr = pr;\r\n    } else {\r\n      wpr = sd;\r\n    }\r\n\r\n    if (x.eq(10)) {\r\n      if (sd == null) external = true;\r\n      return getLn10(Ctor, wpr);\r\n    }\r\n\r\n    wpr += guard;\r\n    Ctor.precision = wpr;\r\n    c = digitsToString(xd);\r\n    c0 = c.charAt(0);\r\n    e = getBase10Exponent(x);\r\n\r\n    if (Math.abs(e) < 1.5e15) {\r\n\r\n      // Argument reduction.\r\n      // The series converges faster the closer the argument is to 1, so using\r\n      // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n      // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n      // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n      // later be divided by this number, then separate out the power of 10 using\r\n      // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n      // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n      //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n      // max n is 6 (gives 0.7 - 1.3)\r\n      while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n        x = x.times(y);\r\n        c = digitsToString(x.d);\r\n        c0 = c.charAt(0);\r\n        n++;\r\n      }\r\n\r\n      e = getBase10Exponent(x);\r\n\r\n      if (c0 > 1) {\r\n        x = new Ctor('0.' + c);\r\n        e++;\r\n      } else {\r\n        x = new Ctor(c0 + '.' + c.slice(1));\r\n      }\r\n    } else {\r\n\r\n      // The argument reduction method above may result in overflow if the argument y is a massive\r\n      // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n      // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n      t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n      x = ln(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n\r\n      Ctor.precision = pr;\r\n      return sd == null ? (external = true, round(x, pr)) : x;\r\n    }\r\n\r\n    // x is reduced to a value near 1.\r\n\r\n    // Taylor series.\r\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\r\n    x2 = round(x.times(x), wpr);\r\n    denominator = 3;\r\n\r\n    for (;;) {\r\n      numerator = round(numerator.times(x2), wpr);\r\n      t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\r\n\r\n      if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n        sum = sum.times(2);\r\n\r\n        // Reverse the argument reduction.\r\n        if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n        sum = divide(sum, new Ctor(n), wpr);\r\n\r\n        Ctor.precision = pr;\r\n        return sd == null ? (external = true, round(sum, pr)) : sum;\r\n      }\r\n\r\n      sum = t;\r\n      denominator += 2;\r\n    }\r\n  }\r\n\r\n\r\n  /*\r\n   * Parse the value of a new Decimal `x` from string `str`.\r\n   */\r\n  function parseDecimal(x, str) {\r\n    var e, i, len;\r\n\r\n    // Decimal point?\r\n    if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n    // Exponential form?\r\n    if ((i = str.search(/e/i)) > 0) {\r\n\r\n      // Determine exponent.\r\n      if (e < 0) e = i;\r\n      e += +str.slice(i + 1);\r\n      str = str.substring(0, i);\r\n    } else if (e < 0) {\r\n\r\n      // Integer.\r\n      e = str.length;\r\n    }\r\n\r\n    // Determine leading zeros.\r\n    for (i = 0; str.charCodeAt(i) === 48;) ++i;\r\n\r\n    // Determine trailing zeros.\r\n    for (len = str.length; str.charCodeAt(len - 1) === 48;) --len;\r\n    str = str.slice(i, len);\r\n\r\n    if (str) {\r\n      len -= i;\r\n      e = e - i - 1;\r\n      x.e = mathfloor(e / LOG_BASE);\r\n      x.d = [];\r\n\r\n      // Transform base\r\n\r\n      // e is the base 10 exponent.\r\n      // i is where to slice str to get the first word of the digits array.\r\n      i = (e + 1) % LOG_BASE;\r\n      if (e < 0) i += LOG_BASE;\r\n\r\n      if (i < len) {\r\n        if (i) x.d.push(+str.slice(0, i));\r\n        for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n        str = str.slice(i);\r\n        i = LOG_BASE - str.length;\r\n      } else {\r\n        i -= len;\r\n      }\r\n\r\n      for (; i--;) str += '0';\r\n      x.d.push(+str);\r\n\r\n      if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\r\n    } else {\r\n\r\n      // Zero.\r\n      x.s = 0;\r\n      x.e = 0;\r\n      x.d = [0];\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  /*\r\n   * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n   */\r\n   function round(x, sd, rm) {\r\n    var i, j, k, n, rd, doRound, w, xdi,\r\n      xd = x.d;\r\n\r\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\r\n    // xdi: the index of w within xd.\r\n    // n: the number of digits of w.\r\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n    // they had leading zeros)\r\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n    // Get the length of the first word of the digits array xd.\r\n    for (n = 1, k = xd[0]; k >= 10; k /= 10) n++;\r\n    i = sd - n;\r\n\r\n    // Is the rounding digit in the first word of xd?\r\n    if (i < 0) {\r\n      i += LOG_BASE;\r\n      j = sd;\r\n      w = xd[xdi = 0];\r\n    } else {\r\n      xdi = Math.ceil((i + 1) / LOG_BASE);\r\n      k = xd.length;\r\n      if (xdi >= k) return x;\r\n      w = k = xd[xdi];\r\n\r\n      // Get the number of digits of w.\r\n      for (n = 1; k >= 10; k /= 10) n++;\r\n\r\n      // Get the index of rd within w.\r\n      i %= LOG_BASE;\r\n\r\n      // Get the index of rd within w, adjusted for leading zeros.\r\n      // The number of leading zeros of w is given by LOG_BASE - n.\r\n      j = i - LOG_BASE + n;\r\n    }\r\n\r\n    if (rm !== void 0) {\r\n      k = mathpow(10, n - j - 1);\r\n\r\n      // Get the rounding digit at index j of w.\r\n      rd = w / k % 10 | 0;\r\n\r\n      // Are there any non-zero digits after the rounding digit?\r\n      doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\r\n\r\n      // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\r\n      // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\r\n      // 714.\r\n\r\n      doRound = rm < 4\r\n        ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n        : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 &&\r\n\r\n          // Check whether the digit to the left of the rounding digit is odd.\r\n          ((i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n            rm == (x.s < 0 ? 8 : 7));\r\n    }\r\n\r\n    if (sd < 1 || !xd[0]) {\r\n      if (doRound) {\r\n        k = getBase10Exponent(x);\r\n        xd.length = 1;\r\n\r\n        // Convert sd to decimal places.\r\n        sd = sd - k - 1;\r\n\r\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n        x.e = mathfloor(-sd / LOG_BASE) || 0;\r\n      } else {\r\n        xd.length = 1;\r\n\r\n        // Zero.\r\n        xd[0] = x.e = x.s = 0;\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n    // Remove excess digits.\r\n    if (i == 0) {\r\n      xd.length = xdi;\r\n      k = 1;\r\n      xdi--;\r\n    } else {\r\n      xd.length = xdi + 1;\r\n      k = mathpow(10, LOG_BASE - i);\r\n\r\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n      // j > 0 means i > number of leading zeros of w.\r\n      xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\r\n    }\r\n\r\n    if (doRound) {\r\n      for (;;) {\r\n\r\n        // Is the digit to be rounded up in the first word of xd?\r\n        if (xdi == 0) {\r\n          if ((xd[0] += k) == BASE) {\r\n            xd[0] = 1;\r\n            ++x.e;\r\n          }\r\n\r\n          break;\r\n        } else {\r\n          xd[xdi] += k;\r\n          if (xd[xdi] != BASE) break;\r\n          xd[xdi--] = 0;\r\n          k = 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n\r\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\r\n      throw Error(exponentOutOfRange + getBase10Exponent(x));\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  function subtract(x, y) {\r\n    var d, e, i, j, k, len, xd, xe, xLTy, yd,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    // Return y negated if x is zero.\r\n    // Return x if y is zero and x is non-zero.\r\n    if (!x.s || !y.s) {\r\n      if (y.s) y.s = -y.s;\r\n      else y = new Ctor(x);\r\n      return external ? round(y, pr) : y;\r\n    }\r\n\r\n    xd = x.d;\r\n    yd = y.d;\r\n\r\n    // x and y are non-zero numbers with the same sign.\r\n\r\n    e = y.e;\r\n    xe = x.e;\r\n    xd = xd.slice();\r\n    k = xe - e;\r\n\r\n    // If exponents differ...\r\n    if (k) {\r\n      xLTy = k < 0;\r\n\r\n      if (xLTy) {\r\n        d = xd;\r\n        k = -k;\r\n        len = yd.length;\r\n      } else {\r\n        d = yd;\r\n        e = xe;\r\n        len = xd.length;\r\n      }\r\n\r\n      // Numbers with massively different exponents would result in a very high number of zeros\r\n      // needing to be prepended, but this can be avoided while still ensuring correct rounding by\r\n      // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n      i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n      if (k > i) {\r\n        k = i;\r\n        d.length = 1;\r\n      }\r\n\r\n      // Prepend zeros to equalise exponents.\r\n      d.reverse();\r\n      for (i = k; i--;) d.push(0);\r\n      d.reverse();\r\n\r\n    // Base 1e7 exponents equal.\r\n    } else {\r\n\r\n      // Check digits to determine which is the bigger number.\r\n\r\n      i = xd.length;\r\n      len = yd.length;\r\n      xLTy = i < len;\r\n      if (xLTy) len = i;\r\n\r\n      for (i = 0; i < len; i++) {\r\n        if (xd[i] != yd[i]) {\r\n          xLTy = xd[i] < yd[i];\r\n          break;\r\n        }\r\n      }\r\n\r\n      k = 0;\r\n    }\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      xd = yd;\r\n      yd = d;\r\n      y.s = -y.s;\r\n    }\r\n\r\n    len = xd.length;\r\n\r\n    // Append zeros to xd if shorter.\r\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\r\n    for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n    // Subtract yd from xd.\r\n    for (i = yd.length; i > k;) {\r\n      if (xd[--i] < yd[i]) {\r\n        for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n        --xd[j];\r\n        xd[i] += BASE;\r\n      }\r\n\r\n      xd[i] -= yd[i];\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (; xd[--len] === 0;) xd.pop();\r\n\r\n    // Remove leading zeros and adjust exponent accordingly.\r\n    for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n    // Zero?\r\n    if (!xd[0]) return new Ctor(0);\r\n\r\n    y.d = xd;\r\n    y.e = e;\r\n\r\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\r\n    return external ? round(y, pr) : y;\r\n  }\r\n\r\n\r\n  function toString(x, isExp, sd) {\r\n    var k,\r\n      e = getBase10Exponent(x),\r\n      str = digitsToString(x.d),\r\n      len = str.length;\r\n\r\n    if (isExp) {\r\n      if (sd && (k = sd - len) > 0) {\r\n        str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n      } else if (len > 1) {\r\n        str = str.charAt(0) + '.' + str.slice(1);\r\n      }\r\n\r\n      str = str + (e < 0 ? 'e' : 'e+') + e;\r\n    } else if (e < 0) {\r\n      str = '0.' + getZeroString(-e - 1) + str;\r\n      if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n    } else if (e >= len) {\r\n      str += getZeroString(e + 1 - len);\r\n      if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n    } else {\r\n      if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n      if (sd && (k = sd - len) > 0) {\r\n        if (e + 1 === len) str += '.';\r\n        str += getZeroString(k);\r\n      }\r\n    }\r\n\r\n    return x.s < 0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // Does not strip trailing zeros.\r\n  function truncate(arr, len) {\r\n    if (arr.length > len) {\r\n      arr.length = len;\r\n      return true;\r\n    }\r\n  }\r\n\r\n\r\n  // Decimal methods\r\n\r\n\r\n  /*\r\n   *  clone\r\n   *  config/set\r\n   */\r\n\r\n\r\n  /*\r\n   * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n   * constructor.\r\n   *\r\n   */\r\n  function clone(obj) {\r\n    var i, p, ps;\r\n\r\n    /*\r\n     * The Decimal constructor and exported function.\r\n     * Return a new Decimal instance.\r\n     *\r\n     * value {number|string|Decimal} A numeric value.\r\n     *\r\n     */\r\n    function Decimal(value) {\r\n      var x = this;\r\n\r\n      // Decimal called without new.\r\n      if (!(x instanceof Decimal)) return new Decimal(value);\r\n\r\n      // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n      // which points to Object.\r\n      x.constructor = Decimal;\r\n\r\n      // Duplicate.\r\n      if (value instanceof Decimal) {\r\n        x.s = value.s;\r\n        x.e = value.e;\r\n        x.d = (value = value.d) ? value.slice() : value;\r\n        return;\r\n      }\r\n\r\n      if (typeof value === 'number') {\r\n\r\n        // Reject Infinity/NaN.\r\n        if (value * 0 !== 0) {\r\n          throw Error(invalidArgument + value);\r\n        }\r\n\r\n        if (value > 0) {\r\n          x.s = 1;\r\n        } else if (value < 0) {\r\n          value = -value;\r\n          x.s = -1;\r\n        } else {\r\n          x.s = 0;\r\n          x.e = 0;\r\n          x.d = [0];\r\n          return;\r\n        }\r\n\r\n        // Fast path for small integers.\r\n        if (value === ~~value && value < 1e7) {\r\n          x.e = 0;\r\n          x.d = [value];\r\n          return;\r\n        }\r\n\r\n        return parseDecimal(x, value.toString());\r\n      } else if (typeof value !== 'string') {\r\n        throw Error(invalidArgument + value);\r\n      }\r\n\r\n      // Minus sign?\r\n      if (value.charCodeAt(0) === 45) {\r\n        value = value.slice(1);\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      if (isDecimal.test(value)) parseDecimal(x, value);\r\n      else throw Error(invalidArgument + value);\r\n    }\r\n\r\n    Decimal.prototype = P;\r\n\r\n    Decimal.ROUND_UP = 0;\r\n    Decimal.ROUND_DOWN = 1;\r\n    Decimal.ROUND_CEIL = 2;\r\n    Decimal.ROUND_FLOOR = 3;\r\n    Decimal.ROUND_HALF_UP = 4;\r\n    Decimal.ROUND_HALF_DOWN = 5;\r\n    Decimal.ROUND_HALF_EVEN = 6;\r\n    Decimal.ROUND_HALF_CEIL = 7;\r\n    Decimal.ROUND_HALF_FLOOR = 8;\r\n\r\n    Decimal.clone = clone;\r\n    Decimal.config = Decimal.set = config;\r\n\r\n    if (obj === void 0) obj = {};\r\n    if (obj) {\r\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'LN10'];\r\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n    }\r\n\r\n    Decimal.config(obj);\r\n\r\n    return Decimal;\r\n  }\r\n\r\n\r\n  /*\r\n   * Configure global settings for a Decimal constructor.\r\n   *\r\n   * `obj` is an object with one or more of the following properties,\r\n   *\r\n   *   precision  {number}\r\n   *   rounding   {number}\r\n   *   toExpNeg   {number}\r\n   *   toExpPos   {number}\r\n   *\r\n   * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n   *\r\n   */\r\n  function config(obj) {\r\n    if (!obj || typeof obj !== 'object') {\r\n      throw Error(decimalError + 'Object expected');\r\n    }\r\n    var i, p, v,\r\n      ps = [\r\n        'precision', 1, MAX_DIGITS,\r\n        'rounding', 0, 8,\r\n        'toExpNeg', -1 / 0, 0,\r\n        'toExpPos', 0, 1 / 0\r\n      ];\r\n\r\n    for (i = 0; i < ps.length; i += 3) {\r\n      if ((v = obj[p = ps[i]]) !== void 0) {\r\n        if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n        else throw Error(invalidArgument + p + ': ' + v);\r\n      }\r\n    }\r\n\r\n    if ((v = obj[p = 'LN10']) !== void 0) {\r\n        if (v == Math.LN10) this[p] = new this(v);\r\n        else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n\r\n    return this;\r\n  }\r\n\r\n\r\n  // Create and configure initial Decimal constructor.\r\n  Decimal = clone(Decimal);\r\n\r\n  Decimal['default'] = Decimal.Decimal = Decimal;\r\n\r\n  // Internal constant.\r\n  ONE = new Decimal(1);\r\n\r\n\r\n  // Export.\r\n\r\n\r\n  // AMD.\r\n  if (true) {\r\n    !(__WEBPACK_AMD_DEFINE_RESULT__ = (function () {\r\n      return Decimal;\r\n    }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n\r\n  // Node and other environments that support module.exports.\r\n  } else {}\r\n})(this);\r\n\n\n//# sourceURL=webpack://recharts-scale/./node_modules/decimal.js-light/decimal.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		if(__webpack_module_cache__[moduleId]) {
/******/ 			return __webpack_module_cache__[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	// startup
/******/ 	// Load entry module
/******/ 	__webpack_require__("./src/index.js");
/******/ 	// This entry module used 'exports' so it can't be inlined
/******/ })()
;