"use client";var Vt=Object.create;var q=Object.defineProperty;var Ot=Object.getOwnPropertyDescriptor;var Kt=Object.getOwnPropertyNames;var Xt=Object.getPrototypeOf,Jt=Object.prototype.hasOwnProperty;var Gt=(a,e)=>{for(var t in e)q(a,t,{get:e[t],enumerable:!0})},Dt=(a,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let m of Kt(e))!Jt.call(a,m)&&m!==t&&q(a,m,{get:()=>e[m],enumerable:!(s=Ot(e,m))||s.enumerable});return a};var Q=(a,e,t)=>(t=a!=null?Vt(Xt(a)):{},Dt(e||!a||!a.__esModule?q(t,"default",{value:a,enumerable:!0}):t,a)),qt=a=>Dt(q({},"__esModule",{value:!0}),a);var ye={};Gt(ye,{Toaster:()=>be,toast:()=>Lt,useSonner:()=>he});module.exports=qt(ye);var o=Q(require("react")),jt=Q(require("react-dom"));var w=Q(require("react")),Ht=a=>{switch(a){case"success":return Zt;case"info":return ee;case"warning":return te;case"error":return oe;default:return null}},Qt=Array(12).fill(0),Mt=({visible:a})=>w.default.createElement("div",{className:"sonner-loading-wrapper","data-visible":a},w.default.createElement("div",{className:"sonner-spinner"},Qt.map((e,t)=>w.default.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),Zt=w.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},w.default.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),te=w.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},w.default.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),ee=w.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},w.default.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),oe=w.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},w.default.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}));var ft=Q(require("react")),At=()=>{let[a,e]=ft.default.useState(document.hidden);return ft.default.useEffect(()=>{let t=()=>{e(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),a};var mt=1,pt=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)});this.publish=e=>{this.subscribers.forEach(t=>t(e))};this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]};this.create=e=>{var b;let{message:t,...s}=e,m=typeof(e==null?void 0:e.id)=="number"||((b=e.id)==null?void 0:b.length)>0?e.id:mt++,u=this.toasts.find(d=>d.id===m),h=e.dismissible===void 0?!0:e.dismissible;return u?this.toasts=this.toasts.map(d=>d.id===m?(this.publish({...d,...e,id:m,title:t}),{...d,...e,id:m,dismissible:h,title:t}):d):this.addToast({title:t,...s,dismissible:h,id:m}),m};this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(s=>s({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e);this.message=(e,t)=>this.create({...t,message:e});this.error=(e,t)=>this.create({...t,message:e,type:"error"});this.success=(e,t)=>this.create({...t,type:"success",message:e});this.info=(e,t)=>this.create({...t,type:"info",message:e});this.warning=(e,t)=>this.create({...t,type:"warning",message:e});this.loading=(e,t)=>this.create({...t,type:"loading",message:e});this.promise=(e,t)=>{if(!t)return;let s;t.loading!==void 0&&(s=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let m=e instanceof Promise?e:e(),u=s!==void 0;return m.then(async h=>{if(ne(h)&&!h.ok){u=!1;let b=typeof t.error=="function"?await t.error(`HTTP error! status: ${h.status}`):t.error,d=typeof t.description=="function"?await t.description(`HTTP error! status: ${h.status}`):t.description;this.create({id:s,type:"error",message:b,description:d})}else if(t.success!==void 0){u=!1;let b=typeof t.success=="function"?await t.success(h):t.success,d=typeof t.description=="function"?await t.description(h):t.description;this.create({id:s,type:"success",message:b,description:d})}}).catch(async h=>{if(t.error!==void 0){u=!1;let b=typeof t.error=="function"?await t.error(h):t.error,d=typeof t.description=="function"?await t.description(h):t.description;this.create({id:s,type:"error",message:b,description:d})}}).finally(()=>{var h;u&&(this.dismiss(s),s=void 0),(h=t.finally)==null||h.call(t)}),s};this.custom=(e,t)=>{let s=(t==null?void 0:t.id)||mt++;return this.create({jsx:e(s),id:s,...t}),s};this.subscribers=[],this.toasts=[]}},v=new pt,ae=(a,e)=>{let t=(e==null?void 0:e.id)||mt++;return v.addToast({title:a,...e,id:t}),t},ne=a=>a&&typeof a=="object"&&"ok"in a&&typeof a.ok=="boolean"&&"status"in a&&typeof a.status=="number",se=ae,re=()=>v.toasts,Lt=Object.assign(se,{success:v.success,info:v.info,warning:v.warning,error:v.error,custom:v.custom,message:v.message,promise:v.promise,dismiss:v.dismiss,loading:v.loading},{getHistory:re});function gt(a,{insertAt:e}={}){if(!a||typeof document=="undefined")return;let t=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css",e==="top"&&t.firstChild?t.insertBefore(s,t.firstChild):t.appendChild(s),s.styleSheet?s.styleSheet.cssText=a:s.appendChild(document.createTextNode(a))}gt(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function U(a){return a.label!==void 0}var ie=3,le="32px",de=4e3,ce=356,ue=14,fe=20,me=200;function pe(...a){return a.filter(Boolean).join(" ")}var ge=a=>{var wt,Tt,St,Rt,Et,Nt,Pt,Bt,Ct,It;let{invert:e,toast:t,unstyled:s,interacting:m,setHeights:u,visibleToasts:h,heights:b,index:d,toasts:Z,expanded:$,removeToast:V,defaultRichColors:tt,closeButton:i,style:O,cancelButtonStyle:K,actionButtonStyle:et,className:ot="",descriptionClassName:at="",duration:X,position:nt,gap:T,loadingIcon:j,expandByDefault:W,classNames:r,icons:I,closeButtonAriaLabel:st="Close toast",pauseWhenPageIsHidden:k,cn:S}=a,[z,rt]=o.default.useState(!1),[D,H]=o.default.useState(!1),[it,N]=o.default.useState(!1),[M,lt]=o.default.useState(!1),[c,p]=o.default.useState(0),[y,R]=o.default.useState(0),A=o.default.useRef(null),l=o.default.useRef(null),_=d===0,J=d+1<=h,x=t.type,P=t.dismissible!==!1,Yt=t.className||"",Ft=t.descriptionClassName||"",G=o.default.useMemo(()=>b.findIndex(n=>n.toastId===t.id)||0,[b,t.id]),$t=o.default.useMemo(()=>{var n;return(n=t.closeButton)!=null?n:i},[t.closeButton,i]),ht=o.default.useMemo(()=>t.duration||X||de,[t.duration,X]),dt=o.default.useRef(0),Y=o.default.useRef(0),bt=o.default.useRef(0),F=o.default.useRef(null),[yt,Wt]=nt.split("-"),xt=o.default.useMemo(()=>b.reduce((n,f,g)=>g>=G?n:n+f.height,0),[b,G]),vt=At(),_t=t.invert||e,ct=x==="loading";Y.current=o.default.useMemo(()=>G*T+xt,[G,xt]),o.default.useEffect(()=>{rt(!0)},[]),o.default.useLayoutEffect(()=>{if(!z)return;let n=l.current,f=n.style.height;n.style.height="auto";let g=n.getBoundingClientRect().height;n.style.height=f,R(g),u(B=>B.find(E=>E.toastId===t.id)?B.map(E=>E.toastId===t.id?{...E,height:g}:E):[{toastId:t.id,height:g,position:t.position},...B])},[z,t.title,t.description,u,t.id]);let L=o.default.useCallback(()=>{H(!0),p(Y.current),u(n=>n.filter(f=>f.toastId!==t.id)),setTimeout(()=>{V(t)},me)},[t,V,u,Y]);o.default.useEffect(()=>{if(t.promise&&x==="loading"||t.duration===1/0||t.type==="loading")return;let n,f=ht;return $||m||k&&vt?(()=>{if(bt.current<dt.current){let C=new Date().getTime()-dt.current;f=f-C}bt.current=new Date().getTime()})():(()=>{f!==1/0&&(dt.current=new Date().getTime(),n=setTimeout(()=>{var C;(C=t.onAutoClose)==null||C.call(t,t),L()},f))})(),()=>clearTimeout(n)},[$,m,W,t,ht,L,t.promise,x,k,vt]),o.default.useEffect(()=>{let n=l.current;if(n){let f=n.getBoundingClientRect().height;return R(f),u(g=>[{toastId:t.id,height:f,position:t.position},...g]),()=>u(g=>g.filter(B=>B.toastId!==t.id))}},[u,t.id]),o.default.useEffect(()=>{t.delete&&L()},[L,t.delete]);function Ut(){return I!=null&&I.loading?o.default.createElement("div",{className:"sonner-loader","data-visible":x==="loading"},I.loading):j?o.default.createElement("div",{className:"sonner-loader","data-visible":x==="loading"},j):o.default.createElement(Mt,{visible:x==="loading"})}return o.default.createElement("li",{"aria-live":t.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:l,className:S(ot,Yt,r==null?void 0:r.toast,(wt=t==null?void 0:t.classNames)==null?void 0:wt.toast,r==null?void 0:r.default,r==null?void 0:r[x],(Tt=t==null?void 0:t.classNames)==null?void 0:Tt[x]),"data-sonner-toast":"","data-rich-colors":(St=t.richColors)!=null?St:tt,"data-styled":!(t.jsx||t.unstyled||s),"data-mounted":z,"data-promise":!!t.promise,"data-removed":D,"data-visible":J,"data-y-position":yt,"data-x-position":Wt,"data-index":d,"data-front":_,"data-swiping":it,"data-dismissible":P,"data-type":x,"data-invert":_t,"data-swipe-out":M,"data-expanded":!!($||W&&z),style:{"--index":d,"--toasts-before":d,"--z-index":Z.length-d,"--offset":`${D?c:Y.current}px`,"--initial-height":W?"auto":`${y}px`,...O,...t.style},onPointerDown:n=>{ct||!P||(A.current=new Date,p(Y.current),n.target.setPointerCapture(n.pointerId),n.target.tagName!=="BUTTON"&&(N(!0),F.current={x:n.clientX,y:n.clientY}))},onPointerUp:()=>{var B,C,E,ut;if(M||!P)return;F.current=null;let n=Number(((B=l.current)==null?void 0:B.style.getPropertyValue("--swipe-amount").replace("px",""))||0),f=new Date().getTime()-((C=A.current)==null?void 0:C.getTime()),g=Math.abs(n)/f;if(Math.abs(n)>=fe||g>.11){p(Y.current),(E=t.onDismiss)==null||E.call(t,t),L(),lt(!0);return}(ut=l.current)==null||ut.style.setProperty("--swipe-amount","0px"),N(!1)},onPointerMove:n=>{var kt;if(!F.current||!P)return;let f=n.clientY-F.current.y,g=n.clientX-F.current.x,C=(yt==="top"?Math.min:Math.max)(0,f),E=n.pointerType==="touch"?10:2;Math.abs(C)>E?(kt=l.current)==null||kt.style.setProperty("--swipe-amount",`${f}px`):Math.abs(g)>E&&(F.current=null)}},$t&&!t.jsx?o.default.createElement("button",{"aria-label":st,"data-disabled":ct,"data-close-button":!0,onClick:ct||!P?()=>{}:()=>{var n;L(),(n=t.onDismiss)==null||n.call(t,t)},className:S(r==null?void 0:r.closeButton,(Rt=t==null?void 0:t.classNames)==null?void 0:Rt.closeButton)},o.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},o.default.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),o.default.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,t.jsx||o.default.isValidElement(t.title)?t.jsx||t.title:o.default.createElement(o.default.Fragment,null,x||t.icon||t.promise?o.default.createElement("div",{"data-icon":"",className:S(r==null?void 0:r.icon,(Et=t==null?void 0:t.classNames)==null?void 0:Et.icon)},t.promise||t.type==="loading"&&!t.icon?t.icon||Ut():null,t.type!=="loading"?t.icon||(I==null?void 0:I[x])||Ht(x):null):null,o.default.createElement("div",{"data-content":"",className:S(r==null?void 0:r.content,(Nt=t==null?void 0:t.classNames)==null?void 0:Nt.content)},o.default.createElement("div",{"data-title":"",className:S(r==null?void 0:r.title,(Pt=t==null?void 0:t.classNames)==null?void 0:Pt.title)},t.title),t.description?o.default.createElement("div",{"data-description":"",className:S(at,Ft,r==null?void 0:r.description,(Bt=t==null?void 0:t.classNames)==null?void 0:Bt.description)},t.description):null),o.default.isValidElement(t.cancel)?t.cancel:t.cancel&&U(t.cancel)?o.default.createElement("button",{"data-button":!0,"data-cancel":!0,style:t.cancelButtonStyle||K,onClick:n=>{var f,g;U(t.cancel)&&P&&((g=(f=t.cancel).onClick)==null||g.call(f,n),L())},className:S(r==null?void 0:r.cancelButton,(Ct=t==null?void 0:t.classNames)==null?void 0:Ct.cancelButton)},t.cancel.label):null,o.default.isValidElement(t.action)?t.action:t.action&&U(t.action)?o.default.createElement("button",{"data-button":!0,"data-action":!0,style:t.actionButtonStyle||et,onClick:n=>{var f,g;U(t.action)&&(n.defaultPrevented||((g=(f=t.action).onClick)==null||g.call(f,n),L()))},className:S(r==null?void 0:r.actionButton,(It=t==null?void 0:t.classNames)==null?void 0:It.actionButton)},t.action.label):null))};function zt(){if(typeof window=="undefined"||typeof document=="undefined")return"ltr";let a=document.documentElement.getAttribute("dir");return a==="auto"||!a?window.getComputedStyle(document.documentElement).direction:a}function he(){let[a,e]=o.default.useState([]);return o.default.useEffect(()=>v.subscribe(t=>{e(s=>{if("dismiss"in t&&t.dismiss)return s.filter(u=>u.id!==t.id);let m=s.findIndex(u=>u.id===t.id);if(m!==-1){let u=[...s];return u[m]={...u[m],...t},u}else return[t,...s]})}),[]),{toasts:a}}var be=a=>{let{invert:e,position:t="bottom-right",hotkey:s=["altKey","KeyT"],expand:m,closeButton:u,className:h,offset:b,theme:d="light",richColors:Z,duration:$,style:V,visibleToasts:tt=ie,toastOptions:i,dir:O=zt(),gap:K=ue,loadingIcon:et,icons:ot,containerAriaLabel:at="Notifications",pauseWhenPageIsHidden:X,cn:nt=pe}=a,[T,j]=o.default.useState([]),W=o.default.useMemo(()=>Array.from(new Set([t].concat(T.filter(c=>c.position).map(c=>c.position)))),[T,t]),[r,I]=o.default.useState([]),[st,k]=o.default.useState(!1),[S,z]=o.default.useState(!1),[rt,D]=o.default.useState(d!=="system"?d:typeof window!="undefined"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),H=o.default.useRef(null),it=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),N=o.default.useRef(null),M=o.default.useRef(!1),lt=o.default.useCallback(c=>{var p;(p=T.find(y=>y.id===c.id))!=null&&p.delete||v.dismiss(c.id),j(y=>y.filter(({id:R})=>R!==c.id))},[T]);return o.default.useEffect(()=>v.subscribe(c=>{if(c.dismiss){j(p=>p.map(y=>y.id===c.id?{...y,delete:!0}:y));return}setTimeout(()=>{jt.default.flushSync(()=>{j(p=>{let y=p.findIndex(R=>R.id===c.id);return y!==-1?[...p.slice(0,y),{...p[y],...c},...p.slice(y+1)]:[c,...p]})})})}),[]),o.default.useEffect(()=>{if(d!=="system"){D(d);return}d==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?D("dark"):D("light")),typeof window!="undefined"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:c})=>{D(c?"dark":"light")})},[d]),o.default.useEffect(()=>{T.length<=1&&k(!1)},[T]),o.default.useEffect(()=>{let c=p=>{var R,A;s.every(l=>p[l]||p.code===l)&&(k(!0),(R=H.current)==null||R.focus()),p.code==="Escape"&&(document.activeElement===H.current||(A=H.current)!=null&&A.contains(document.activeElement))&&k(!1)};return document.addEventListener("keydown",c),()=>document.removeEventListener("keydown",c)},[s]),o.default.useEffect(()=>{if(H.current)return()=>{N.current&&(N.current.focus({preventScroll:!0}),N.current=null,M.current=!1)}},[H.current]),T.length?o.default.createElement("section",{"aria-label":`${at} ${it}`,tabIndex:-1},W.map((c,p)=>{var A;let[y,R]=c.split("-");return o.default.createElement("ol",{key:c,dir:O==="auto"?zt():O,tabIndex:-1,ref:H,className:h,"data-sonner-toaster":!0,"data-theme":rt,"data-y-position":y,"data-x-position":R,style:{"--front-toast-height":`${((A=r[0])==null?void 0:A.height)||0}px`,"--offset":typeof b=="number"?`${b}px`:b||le,"--width":`${ce}px`,"--gap":`${K}px`,...V},onBlur:l=>{M.current&&!l.currentTarget.contains(l.relatedTarget)&&(M.current=!1,N.current&&(N.current.focus({preventScroll:!0}),N.current=null))},onFocus:l=>{l.target instanceof HTMLElement&&l.target.dataset.dismissible==="false"||M.current||(M.current=!0,N.current=l.relatedTarget)},onMouseEnter:()=>k(!0),onMouseMove:()=>k(!0),onMouseLeave:()=>{S||k(!1)},onPointerDown:l=>{l.target instanceof HTMLElement&&l.target.dataset.dismissible==="false"||z(!0)},onPointerUp:()=>z(!1)},T.filter(l=>!l.position&&p===0||l.position===c).map((l,_)=>{var J,x;return o.default.createElement(ge,{key:l.id,icons:ot,index:_,toast:l,defaultRichColors:Z,duration:(J=i==null?void 0:i.duration)!=null?J:$,className:i==null?void 0:i.className,descriptionClassName:i==null?void 0:i.descriptionClassName,invert:e,visibleToasts:tt,closeButton:(x=i==null?void 0:i.closeButton)!=null?x:u,interacting:S,position:c,style:i==null?void 0:i.style,unstyled:i==null?void 0:i.unstyled,classNames:i==null?void 0:i.classNames,cancelButtonStyle:i==null?void 0:i.cancelButtonStyle,actionButtonStyle:i==null?void 0:i.actionButtonStyle,removeToast:lt,toasts:T.filter(P=>P.position==l.position),heights:r.filter(P=>P.position==l.position),setHeights:I,expandByDefault:m,gap:K,loadingIcon:et,expanded:st,pauseWhenPageIsHidden:X,cn:nt})}))})):null};0&&(module.exports={Toaster,toast,useSonner});
//# sourceMappingURL=index.js.map