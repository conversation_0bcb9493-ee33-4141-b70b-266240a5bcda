
import React from 'react';
import { <PERSON>ap, Target, TrendingUp, BarChart3, Eye } from 'lucide-react';

export const ExclusiveStrategies = () => {
  const strategies = [
    {
      icon: Zap,
      title: "Precision Timing Entries",
      description: "Learn how to execute trades with confidence using time-sensitive entry windows designed for high-probability setups."
    },
    {
      icon: Target,
      title: "XAUUSD Master Method",
      description: "Unlock gold trading with a tailored strategy specifically crafted for the unique movement of XAUUSD in volatile conditions."
    },
    {
      icon: TrendingUp,
      title: "Summit-to-Base Market Flow",
      description: "Identify major highs and lows with this structural mapping approach to spot momentum shifts and reversal points in advance."
    },
    {
      icon: BarChart3,
      title: "Killzone Compression Breakout",
      description: "A powerful setup that capitalizes on tight market ranges during key sessions — just before explosive moves occur."
    },
    {
      icon: Eye,
      title: "Real-Market Strategy Walkthroughs",
      description: "Watch how these concepts are applied in actual market conditions, helping you bridge the gap between theory and execution."
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-[#05044A] to-slate-800 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Exclusive <span className="text-[#56D07F]">UltimA</span> Strategies
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Master our proprietary trading methods developed through years of market experience
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-8">
            {strategies.map((strategy, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300">
                <div className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center w-14 h-14 bg-[#56D07F] rounded-xl">
                      <strategy.icon className="w-7 h-7 text-white" />
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-bold text-white mb-4">
                      ● {strategy.title}
                    </h3>
                    <p className="text-gray-300 leading-relaxed">
                      {strategy.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-16 text-center">
            <div className="bg-[#56D07F]/20 border border-[#56D07F]/40 rounded-2xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-[#56D07F] mb-4">
                Transform Your Trading Journey
              </h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                These exclusive strategies are the result of thousands of hours of market analysis and real trading experience. 
                You won't find these methods anywhere else.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
