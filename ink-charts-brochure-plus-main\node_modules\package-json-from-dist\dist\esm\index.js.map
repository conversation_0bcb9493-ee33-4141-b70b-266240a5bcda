{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAA;AACtC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,WAAW,CAAA;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAA;AAExC,MAAM,EAAE,GAAG,GAAG,GAAG,eAAe,GAAG,EAAE,CAAA;AACrC,MAAM,KAAK,GAAG,SAAS,GAAG,EAAE,CAAA;AAC5B,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,GAAG,EAAE,CAAA;AACjC,MAAM,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,EAAE,CAAA;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAC7B,IAAkB,EAClB,cAAsB,iBAAiB,EAC/B,EAAE;IACV,MAAM,CAAC,GACL,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;QACtD,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,CAAC,IAAI,CAAA;IACR,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;IAE5B,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IACrC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QACf,qEAAqE;QACrE,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAA;QAClD,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAA;QACnD,4DAA4D;QAC5D,sDAAsD;QACtD,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,OAAO,CACZ,EAAE,EACF,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,EACrC,cAAc,CACf,CAAA;YACH,CAAC;QACH,CAAC;QACD,MAAM,OAAO,GACX,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YAChC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAChC,OAAO,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;IAC7C,CAAC;SAAM,CAAC;QACN,kCAAkC;QAClC,MAAM,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACrC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACb,OAAO,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,CAAA;QAC3D,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAC7B,IAAkB,EAClB,cAAsB,iBAAiB,EACvC,EAAE,CACF,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA", "sourcesContent": ["import { readFileSync } from 'node:fs'\nimport { dirname, resolve, sep } from 'node:path'\nimport { fileURLToPath } from 'node:url'\n\nconst NM = `${sep}node_modules${sep}`\nconst STORE = `.store${sep}`\nconst PKG = `${sep}package${sep}`\nconst DIST = `${sep}dist${sep}`\n\n/**\n * Find the package.json file, either from a TypeScript file somewhere not\n * in a 'dist' folder, or a built and/or installed 'dist' folder.\n *\n * Note: this *only* works if you build your code into `'./dist'`, and that the\n * source path does not also contain `'dist'`! If you don't build into\n * `'./dist'`, or if you have files at `./src/dist/dist.ts`, then this will\n * not work properly!\n *\n * The default `pathFromSrc` option assumes that the calling code lives one\n * folder below the root of the package. Otherwise, it must be specified.\n *\n * Example:\n *\n * ```ts\n * // src/index.ts\n * import { findPackageJson } from 'package-json-from-dist'\n *\n * const pj = findPackageJson(import.meta.url)\n * console.log(`package.json found at ${pj}`)\n * ```\n *\n * If the caller is deeper within the project source, then you must provide\n * the appropriate fallback path:\n *\n * ```ts\n * // src/components/something.ts\n * import { findPackageJson } from 'package-json-from-dist'\n *\n * const pj = findPackageJson(import.meta.url, '../../package.json')\n * console.log(`package.json found at ${pj}`)\n * ```\n *\n * When running from CommmonJS, use `__filename` instead of `import.meta.url`\n *\n * ```ts\n * // src/index.cts\n * import { findPackageJson } from 'package-json-from-dist'\n *\n * const pj = findPackageJson(__filename)\n * console.log(`package.json found at ${pj}`)\n * ```\n */\nexport const findPackageJson = (\n  from: string | URL,\n  pathFromSrc: string = '../package.json',\n): string => {\n  const f =\n    typeof from === 'object' || from.startsWith('file://') ?\n      fileURLToPath(from)\n    : from\n  const __dirname = dirname(f)\n\n  const nms = __dirname.lastIndexOf(NM)\n  if (nms !== -1) {\n    // inside of node_modules. find the dist directly under package name.\n    const nm = __dirname.substring(0, nms + NM.length)\n    const pkgDir = __dirname.substring(nms + NM.length)\n    // affordance for yarn berry, which puts package contents in\n    // '.../node_modules/.store/${id}-${hash}/package/...'\n    if (pkgDir.startsWith(STORE)) {\n      const pkg = pkgDir.indexOf(PKG, STORE.length)\n      if (pkg) {\n        return resolve(\n          nm,\n          pkgDir.substring(0, pkg + PKG.length),\n          'package.json',\n        )\n      }\n    }\n    const pkgName =\n      pkgDir.startsWith('@') ?\n        pkgDir.split(sep, 2).join(sep)\n      : String(pkgDir.split(sep)[0])\n    return resolve(nm, pkgName, 'package.json')\n  } else {\n    // see if we are in a dist folder.\n    const d = __dirname.lastIndexOf(DIST)\n    if (d !== -1) {\n      return resolve(__dirname.substring(0, d), 'package.json')\n    } else {\n      return resolve(__dirname, pathFromSrc)\n    }\n  }\n}\n\n/**\n * Load the package.json file, either from a TypeScript file somewhere not\n * in a 'dist' folder, or a built and/or installed 'dist' folder.\n *\n * Note: this *only* works if you build your code into `'./dist'`, and that the\n * source path does not also contain `'dist'`! If you don't build into\n * `'./dist'`, or if you have files at `./src/dist/dist.ts`, then this will\n * not work properly!\n *\n * The default `pathFromSrc` option assumes that the calling code lives one\n * folder below the root of the package. Otherwise, it must be specified.\n *\n * Example:\n *\n * ```ts\n * // src/index.ts\n * import { loadPackageJson } from 'package-json-from-dist'\n *\n * const pj = loadPackageJson(import.meta.url)\n * console.log(`Hello from ${pj.name}@${pj.version}`)\n * ```\n *\n * If the caller is deeper within the project source, then you must provide\n * the appropriate fallback path:\n *\n * ```ts\n * // src/components/something.ts\n * import { loadPackageJson } from 'package-json-from-dist'\n *\n * const pj = loadPackageJson(import.meta.url, '../../package.json')\n * console.log(`Hello from ${pj.name}@${pj.version}`)\n * ```\n *\n * When running from CommmonJS, use `__filename` instead of `import.meta.url`\n *\n * ```ts\n * // src/index.cts\n * import { loadPackageJson } from 'package-json-from-dist'\n *\n * const pj = loadPackageJson(__filename)\n * console.log(`Hello from ${pj.name}@${pj.version}`)\n * ```\n */\nexport const loadPackageJson = (\n  from: string | URL,\n  pathFromSrc: string = '../package.json',\n) =>\n  JSON.parse(readFileSync(findPackageJson(from, pathFromSrc), 'utf8'))\n"]}