
import React from 'react';
import { MessageCircle, Users, HelpCircle, TrendingUp, CheckCircle } from 'lucide-react';

export const ContinuedSupport = () => {
  const supportFeatures = [
    {
      icon: MessageCircle,
      title: "Private Discord access to stay connected with the community"
    },
    {
      icon: HelpCircle,
      title: "Doubt-clearing support directly with the mentor"
    },
    {
      icon: TrendingUp,
      title: "Ongoing trade discussions, updates, and insights"
    },
    {
      icon: Users,
      title: "Priority access to future sessions and strategy refinements"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-[#05044A] mb-6">
              Continued Support & Community
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              After completing the mentorship, you'll get continued support including:
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {supportFeatures.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4 p-6 bg-gradient-to-r from-slate-50 to-white rounded-xl border hover:shadow-lg transition-shadow">
                <div className="flex-shrink-0">
                  <div className="flex items-center justify-center w-12 h-12 bg-[#56D07F] rounded-lg">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-6 h-6 text-[#56D07F] mt-1 flex-shrink-0" />
                  <p className="text-gray-700 leading-relaxed font-medium">
                    {feature.title}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-gradient-to-br from-[#56D07F]/10 to-emerald-50 rounded-2xl p-12 text-center border border-[#56D07F]/20">
            <h3 className="text-3xl font-bold text-[#05044A] mb-6">
              You're Never Alone
            </h3>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto">
              The learning continues even after the session ends. Our community and ongoing support ensure 
              you have everything you need to succeed in your trading journey.
            </p>
            
            <div className="mt-8 inline-flex items-center space-x-2 bg-[#56D07F] text-white px-8 py-4 rounded-lg font-semibold text-lg">
              <Users className="w-6 h-6" />
              <span>Join Our Trading Community</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
