
import React from 'react';
import { Play, MessageCircle, Users, CheckCircle, <PERSON><PERSON><PERSON>, <PERSON>, Award } from 'lucide-react';

export const ProgramOverview = () => {
  return (
    <section className="py-24 bg-gradient-to-br from-slate-50 via-white to-slate-100 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-20 left-20 w-40 h-40 bg-[#56D07F]/10 rounded-full blur-2xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-32 h-32 bg-[#05044A]/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
      
      <div className="container mx-auto px-4 relative">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-block p-4 bg-gradient-to-r from-[#56D07F]/10 to-emerald-50 rounded-3xl mb-8">
              <Sparkles className="w-12 h-12 text-[#56D07F]" />
            </div>
            <h2 className="text-4xl lg:text-6xl font-bold text-[#05044A] mb-8">
              What's This Program All About?
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-6">
              This program is designed to cut through the noise and teach trading the way it should be — clear, structured, and practical.
            </p>
            <div className="bg-gradient-to-r from-[#56D07F]/10 to-emerald-50 px-8 py-4 rounded-2xl border border-[#56D07F]/20 inline-block">
              <p className="text-lg text-[#56D07F] font-bold">
                No overpromises. No fluff. Just a real, repeatable system built for those who are ready to learn, apply, and grow with purpose.
              </p>
            </div>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 mb-20">
            <div className="group bg-white p-10 rounded-3xl shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:bg-gradient-to-br hover:from-white hover:to-slate-50">
              <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-[#56D07F] to-emerald-400 rounded-2xl mb-8 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <div className="text-2xl font-bold text-white">₹12,500</div>
              </div>
              <h3 className="text-2xl font-bold text-[#05044A] mb-4">Program Fee</h3>
              <p className="text-gray-600 text-lg">Per student investment for comprehensive trading education</p>
            </div>
            
            <div className="group bg-white p-10 rounded-3xl shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:bg-gradient-to-br hover:from-white hover:to-slate-50">
              <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-[#05044A] to-slate-800 rounded-2xl mb-8 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <Play className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-[#05044A] mb-4">Live + Replay</h3>
              <p className="text-gray-600 text-lg">Interactive live sessions with permanent access to recordings</p>
            </div>
            
            <div className="group bg-white p-10 rounded-3xl shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:bg-gradient-to-br hover:from-white hover:to-slate-50">
              <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-[#56D07F] to-emerald-400 rounded-2xl mb-8 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <MessageCircle className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-[#05044A] mb-4">Discord Community</h3>
              <p className="text-gray-600 text-lg">Exclusive access to private trading community</p>
            </div>
          </div>
          
          <div className="bg-white rounded-3xl shadow-2xl p-12 lg:p-16 border border-gray-100 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-[#56D07F]/5 to-emerald-50 rounded-full blur-3xl"></div>
            
            <div className="relative">
              <h3 className="text-4xl font-bold text-[#05044A] mb-12 text-center flex items-center justify-center">
                <Award className="w-10 h-10 mr-4 text-[#56D07F]" />
                Why Choose Ultima Plus?
              </h3>
              <div className="grid md:grid-cols-2 gap-12">
                <div className="space-y-6">
                  <div className="flex items-start space-x-4 group">
                    <div className="p-2 bg-[#56D07F]/10 rounded-lg group-hover:bg-[#56D07F]/20 transition-colors flex-shrink-0">
                      <CheckCircle className="w-8 h-8 text-[#56D07F]" />
                    </div>
                    <p className="text-gray-700 text-lg leading-relaxed">You'll learn in real time — ask questions, clear your doubts, and understand every concept deeply.</p>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="p-2 bg-[#56D07F]/10 rounded-lg group-hover:bg-[#56D07F]/20 transition-colors flex-shrink-0">
                      <CheckCircle className="w-8 h-8 text-[#56D07F]" />
                    </div>
                    <p className="text-gray-700 text-lg leading-relaxed">Private Discord access for continued support, discussion, and community growth.</p>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4 group">
                    <div className="p-2 bg-[#56D07F]/10 rounded-lg group-hover:bg-[#56D07F]/20 transition-colors flex-shrink-0">
                      <CheckCircle className="w-8 h-8 text-[#56D07F]" />
                    </div>
                    <p className="text-gray-700 text-lg leading-relaxed">High-impact session built for those who are serious about mastering trading.</p>
                  </div>
                  <div className="flex items-start space-x-4 group">
                    <div className="p-2 bg-[#56D07F]/10 rounded-lg group-hover:bg-[#56D07F]/20 transition-colors flex-shrink-0">
                      <CheckCircle className="w-8 h-8 text-[#56D07F]" />
                    </div>
                    <p className="text-gray-700 text-lg leading-relaxed">Real, repeatable system with clarity and purpose.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
