
import React from 'react';
import { TrendingUp, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users, Award } from 'lucide-react';
import { TradingChart } from './TradingChart';

export const Hero = () => {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-32 h-32 bg-[#56D07F]/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-[#05044A]/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/4 w-20 h-20 bg-[#56D07F]/10 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>
      
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rg<PERSON>(255,255,255,0.6))]" />
      
      <div className="relative container mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8 animate-fade-in">
            <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-[#56D07F]/10 to-emerald-50 px-6 py-3 rounded-full border border-[#56D07F]/20 hover:scale-105 transition-transform duration-300">
              <TrendingUp className="w-5 h-5 text-[#56D07F]" />
              <span className="text-[#56D07F] font-semibold">Live Trading Program</span>
            </div>
            
            <h1 className="text-5xl lg:text-7xl font-bold text-[#05044A] leading-tight">
              Ultima Plus<br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#56D07F] via-emerald-400 to-[#56D07F] animate-pulse">
                Trading Program
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
              More than a session — it's a transformation. Master trading with clarity, purpose, and real-time learning.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="bg-gradient-to-r from-[#05044A] to-slate-800 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="flex items-center space-x-2">
                  <Award className="w-5 h-5" />
                  <span>Program Fee: ₹12,500</span>
                </div>
              </div>
              <div className="border-2 border-[#56D07F] text-[#56D07F] px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#56D07F] hover:text-white transition-all duration-300 hover:scale-105 hover:shadow-lg">
                Live + Replay Access
              </div>
            </div>
            
            <div className="flex items-center space-x-8 pt-6">
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-[#56D07F]/10 rounded-lg group-hover:bg-[#56D07F]/20 transition-colors">
                  <BarChart3 className="w-6 h-6 text-[#56D07F]" />
                </div>
                <span className="text-gray-600 font-medium">Live Sessions</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="p-2 bg-[#56D07F]/10 rounded-lg group-hover:bg-[#56D07F]/20 transition-colors">
                  <LineChart className="w-6 h-6 text-[#56D07F]" />
                </div>
                <span className="text-gray-600 font-medium">Discord Community</span>
              </div>
            </div>
          </div>
          
          <div className="relative animate-fade-in delay-300">
            <div className="bg-white p-8 rounded-3xl shadow-2xl border border-gray-100 hover:shadow-3xl transition-shadow duration-500">
              <TradingChart />
            </div>
            <div className="absolute -top-6 -right-6 bg-gradient-to-r from-[#56D07F] to-emerald-400 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl animate-bounce">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <span>1500+ Students Trained</span>
              </div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-[#05044A]/5 rounded-full blur-sm animate-pulse"></div>
            <div className="absolute -top-2 left-8 w-8 h-8 bg-[#56D07F]/20 rounded-full animate-ping"></div>
          </div>
        </div>
      </div>
      
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white via-white/80 to-transparent" />
    </section>
  );
};
