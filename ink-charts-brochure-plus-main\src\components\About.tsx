
import React from 'react';
import { Award, Users, TrendingUp, Star, Trophy, Target } from 'lucide-react';

export const About = () => {
  return (
    <section className="py-24 bg-gradient-to-br from-white to-slate-50 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-[#56D07F]/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-[#05044A]/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-block p-4 bg-[#56D07F]/10 rounded-2xl mb-6">
              <Trophy className="w-12 h-12 text-[#56D07F]" />
            </div>
            <h2 className="text-4xl lg:text-6xl font-bold text-[#05044A] mb-6">
              Hello, I am <span className="text-[#56D07F] relative">
                <PERSON><PERSON><PERSON><PERSON>landaivel
                <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-[#56D07F] to-emerald-400 rounded-full"></div>
              </span>
            </h2>
            <p className="text-2xl text-gray-700 mb-4 font-medium">The Founder of Ink Charts</p>
            <div className="bg-gradient-to-r from-[#56D07F]/10 to-emerald-50 px-8 py-4 rounded-2xl border border-[#56D07F]/20 inline-block">
              <p className="text-lg text-[#56D07F] font-bold flex items-center">
                <Target className="w-6 h-6 mr-3" />
                I am on the journey to help 1 lakh+ people achieve Financial Independence.
              </p>
            </div>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-16 items-start">
            <div className="space-y-10">
              <div className="bg-white p-8 rounded-3xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <h3 className="text-3xl font-bold text-[#05044A] mb-6 flex items-center">
                  <div className="p-3 bg-[#56D07F]/10 rounded-xl mr-4">
                    <Award className="w-8 h-8 text-[#56D07F]" />
                  </div>
                  My Qualification:
                </h3>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start group">
                    <div className="w-3 h-3 bg-[#56D07F] rounded-full mt-2 mr-4 flex-shrink-0 group-hover:scale-125 transition-transform"></div>
                    <span className="text-lg leading-relaxed">Bachelor of Commerce Graduate</span>
                  </li>
                  <li className="flex items-start group">
                    <div className="w-3 h-3 bg-[#56D07F] rounded-full mt-2 mr-4 flex-shrink-0 group-hover:scale-125 transition-transform"></div>
                    <span className="text-lg leading-relaxed">Certified Chartered Market Technician (CMT Level 1) with 4+ years of expertise in Forex trading</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white p-8 rounded-3xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <h3 className="text-3xl font-bold text-[#05044A] mb-6 flex items-center">
                  <div className="p-3 bg-[#56D07F]/10 rounded-xl mr-4">
                    <Star className="w-8 h-8 text-[#56D07F]" />
                  </div>
                  My Achievements:
                </h3>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start group">
                    <div className="w-3 h-3 bg-[#56D07F] rounded-full mt-2 mr-4 flex-shrink-0 group-hover:scale-125 transition-transform"></div>
                    <span className="text-lg leading-relaxed">As a Mentor, I trained 1500+ students to help them achieve financial success.</span>
                  </li>
                  <li className="flex items-start group">
                    <div className="w-3 h-3 bg-[#56D07F] rounded-full mt-2 mr-4 flex-shrink-0 group-hover:scale-125 transition-transform"></div>
                    <span className="text-lg leading-relaxed">International gold medalist in Roll Ball (2013, 2017)</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="space-y-8">
              <div className="bg-gradient-to-br from-[#56D07F]/10 via-emerald-50 to-[#56D07F]/5 p-10 rounded-3xl border border-[#56D07F]/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-[#56D07F] to-emerald-400 rounded-2xl mb-6 shadow-lg">
                  <Users className="w-10 h-10 text-white" />
                </div>
                <h4 className="text-4xl font-bold text-[#05044A] mb-3">1500+</h4>
                <p className="text-xl text-gray-600 font-medium">Students Trained Successfully</p>
              </div>
              
              <div className="bg-gradient-to-br from-[#05044A]/10 via-slate-50 to-[#05044A]/5 p-10 rounded-3xl border border-[#05044A]/20 hover:shadow-xl transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-[#05044A] to-slate-800 rounded-2xl mb-6 shadow-lg">
                  <TrendingUp className="w-10 h-10 text-white" />
                </div>
                <h4 className="text-4xl font-bold text-[#05044A] mb-3">4+ Years</h4>
                <p className="text-xl text-gray-600 font-medium">Forex Trading Expertise</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
