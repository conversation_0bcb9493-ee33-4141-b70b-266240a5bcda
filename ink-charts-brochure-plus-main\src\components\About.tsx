
import React from 'react';
import { Award, Users, TrendingUp, Star, Trophy, Target, Sparkles } from 'lucide-react';
import mentor  from "../../public/mentor.jpg"

export const About = () => {
  return (
    <section className="py-24 bg-gradient-to-br from-white via-slate-50/30 to-emerald-50/20 relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-[#56D07F]/10 to-emerald-200/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-[#05044A]/10 to-slate-300/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-[#56D07F]/5 to-[#05044A]/5 rounded-full blur-2xl"></div>

      <div className="container mx-auto px-4 relative">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced Header Section with Portrait */}
          <div className="text-center mb-24">
            <div className="flex flex-col items-center space-y-8">
              {/* Portrait Image Placeholder */}
              <div className="relative group">
                <div className="w-48 h-48 lg:w-56 lg:h-56 rounded-full bg-gradient-to-br from-[#56D07F]/20 via-emerald-100 to-[#56D07F]/10 border-4 border-white shadow-2xl overflow-hidden relative group-hover:scale-105 transition-all duration-500">
                <img src={mentor/>
                  <div className="absolute inset-0 bg-gradient-to-br from-[#56D07F]/30 to-emerald-200/40 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-white/80 rounded-full flex items-center justify-center mb-4 mx-auto">
                        
                      </div>
                      <p className="text-[#05044A] font-semibold text-sm">Portrait Photo</p>
                      <p className="text-[#05044A]/70 text-xs">Kiruthika Kulandaivel</p>
                    </div>
                  </div>
                  {/* Decorative ring */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-[#56D07F] via-emerald-400 to-[#56D07F] rounded-full opacity-20 animate-spin-slow"></div>
                </div>
                {/* Floating elements around portrait */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-[#56D07F] rounded-full flex items-center justify-center shadow-lg animate-bounce">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-[#05044A] rounded-full animate-pulse"></div>
              </div>

              {/* Enhanced Title Section */}
              <div className="space-y-6">
                <div className="inline-block p-4 bg-gradient-to-r from-[#56D07F]/10 via-emerald-50 to-[#56D07F]/10 rounded-2xl mb-6 border border-[#56D07F]/20 shadow-lg">
                  <Trophy className="w-12 h-12 text-[#56D07F] mx-auto animate-pulse" />
                </div>
                <h2 className="text-4xl lg:text-6xl font-bold text-[#05044A] mb-6 leading-tight">
                  Hello, I am <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#56D07F] via-emerald-400 to-[#56D07F] relative animate-pulse">
                    Kiruthika Kulandaivel
                    <div className="absolute -bottom-3 left-0 right-0 h-2 bg-gradient-to-r from-[#56D07F] via-emerald-400 to-[#56D07F] rounded-full opacity-30"></div>
                  </span>
                </h2>
                <div className="space-y-4">
                  <p className="text-2xl lg:text-3xl text-gray-700 font-bold">The Founder of <span className="text-[#56D07F]">Ink Charts</span></p>
                  <div className="bg-gradient-to-r from-[#56D07F]/10 via-emerald-50 to-[#56D07F]/10 px-8 py-6 rounded-3xl border-2 border-[#56D07F]/20 inline-block shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <p className="text-lg lg:text-xl text-[#56D07F] font-bold flex items-center justify-center">
                      <Target className="w-7 h-7 mr-3 animate-pulse" />
                      I am on the journey to help <span className="text-[#05044A] mx-2 font-black">1 lakh+</span> people achieve Financial Independence.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Content Grid */}
          <div className="grid lg:grid-cols-2 gap-20 items-start">
            <div className="space-y-12">
              {/* Enhanced Qualification Card */}
              <div className="bg-gradient-to-br from-white via-slate-50/50 to-white p-10 rounded-3xl shadow-2xl border-2 border-gray-100/50 hover:shadow-3xl transition-all duration-500 hover:scale-105 hover:border-[#56D07F]/30 group relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#56D07F]/5 to-transparent rounded-full blur-2xl group-hover:scale-150 transition-transform duration-500"></div>

                <div className="relative">
                  <h3 className="text-3xl lg:text-4xl font-bold text-[#05044A] mb-8 flex items-center">
                    <div className="p-4 bg-gradient-to-br from-[#56D07F]/10 via-emerald-50 to-[#56D07F]/5 rounded-2xl mr-5 shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <Award className="w-10 h-10 text-[#56D07F] group-hover:scale-110 transition-transform" />
                    </div>
                    My Qualifications
                  </h3>
                  <ul className="space-y-6 text-gray-700">
                    <li className="flex items-start group/item">
                      <div className="w-4 h-4 bg-gradient-to-r from-[#56D07F] to-emerald-400 rounded-full mt-2 mr-5 flex-shrink-0 group-hover/item:scale-125 transition-transform shadow-lg"></div>
                      <span className="text-lg lg:text-xl leading-relaxed font-medium group-hover/item:text-[#05044A] transition-colors">Bachelor of Commerce Graduate</span>
                    </li>
                    <li className="flex items-start group/item">
                      <div className="w-4 h-4 bg-gradient-to-r from-[#56D07F] to-emerald-400 rounded-full mt-2 mr-5 flex-shrink-0 group-hover/item:scale-125 transition-transform shadow-lg"></div>
                      <span className="text-lg lg:text-xl leading-relaxed font-medium group-hover/item:text-[#05044A] transition-colors">Certified Chartered Market Technician (CMT Level 1) with 4+ years of expertise in Forex trading</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Enhanced Achievements Card */}
              <div className="bg-gradient-to-br from-white via-emerald-50/30 to-white p-10 rounded-3xl shadow-2xl border-2 border-gray-100/50 hover:shadow-3xl transition-all duration-500 hover:scale-105 hover:border-[#56D07F]/30 group relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-[#05044A]/5 to-transparent rounded-full blur-2xl group-hover:scale-150 transition-transform duration-500"></div>

                <div className="relative">
                  <h3 className="text-3xl lg:text-4xl font-bold text-[#05044A] mb-8 flex items-center">
                    <div className="p-4 bg-gradient-to-br from-[#05044A]/10 via-slate-100 to-[#05044A]/5 rounded-2xl mr-5 shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <Star className="w-10 h-10 text-[#05044A] group-hover:scale-110 transition-transform" />
                    </div>
                    My Achievements
                  </h3>
                  <ul className="space-y-6 text-gray-700">
                    <li className="flex items-start group/item">
                      <div className="w-4 h-4 bg-gradient-to-r from-[#05044A] to-slate-600 rounded-full mt-2 mr-5 flex-shrink-0 group-hover/item:scale-125 transition-transform shadow-lg"></div>
                      <span className="text-lg lg:text-xl leading-relaxed font-medium group-hover/item:text-[#05044A] transition-colors">As a Mentor, I trained <span className="font-bold text-[#56D07F]">1500+</span> students to help them achieve financial success.</span>
                    </li>
                    <li className="flex items-start group/item">
                      <div className="w-4 h-4 bg-gradient-to-r from-[#05044A] to-slate-600 rounded-full mt-2 mr-5 flex-shrink-0 group-hover/item:scale-125 transition-transform shadow-lg"></div>
                      <span className="text-lg lg:text-xl leading-relaxed font-medium group-hover/item:text-[#05044A] transition-colors">International gold medalist in Roll Ball <span className="font-bold text-[#56D07F]">(2013, 2017)</span></span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Enhanced Statistics Section */}
            <div className="space-y-10">
              {/* Students Trained Card */}
              <div className="bg-gradient-to-br from-[#56D07F]/10 via-emerald-50 to-[#56D07F]/5 p-12 rounded-3xl border-2 border-[#56D07F]/20 hover:shadow-3xl transition-all duration-500 hover:scale-105 hover:border-[#56D07F]/40 group relative overflow-hidden">
                {/* Animated background elements */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-[#56D07F]/10 rounded-full blur-xl animate-pulse"></div>
                <div className="absolute bottom-0 left-0 w-16 h-16 bg-emerald-200/20 rounded-full blur-lg animate-pulse delay-1000"></div>

                <div className="relative text-center">
                  <div className="flex items-center justify-center w-24 h-24 bg-gradient-to-br from-[#56D07F] via-emerald-400 to-[#56D07F] rounded-3xl mb-8 shadow-2xl group-hover:shadow-3xl transition-all duration-300 group-hover:scale-110 mx-auto">
                    <Users className="w-12 h-12 text-white group-hover:animate-pulse" />
                  </div>
                  <h4 className="text-5xl lg:text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#56D07F] to-emerald-400 mb-4 animate-pulse">1500+</h4>
                  <p className="text-xl lg:text-2xl text-gray-700 font-bold">Students Trained Successfully</p>
                  <div className="mt-4 w-20 h-1 bg-gradient-to-r from-[#56D07F] to-emerald-400 rounded-full mx-auto"></div>
                </div>
              </div>

              {/* Experience Card */}
              <div className="bg-gradient-to-br from-[#05044A]/10 via-slate-100 to-[#05044A]/5 p-12 rounded-3xl border-2 border-[#05044A]/20 hover:shadow-3xl transition-all duration-500 hover:scale-105 hover:border-[#05044A]/40 group relative overflow-hidden">
                {/* Animated background elements */}
                <div className="absolute top-0 left-0 w-20 h-20 bg-[#05044A]/10 rounded-full blur-xl animate-pulse delay-500"></div>
                <div className="absolute bottom-0 right-0 w-28 h-28 bg-slate-200/30 rounded-full blur-lg animate-pulse delay-1500"></div>

                <div className="relative text-center">
                  <div className="flex items-center justify-center w-24 h-24 bg-gradient-to-br from-[#05044A] via-slate-700 to-[#05044A] rounded-3xl mb-8 shadow-2xl group-hover:shadow-3xl transition-all duration-300 group-hover:scale-110 mx-auto">
                    <TrendingUp className="w-12 h-12 text-white group-hover:animate-pulse" />
                  </div>
                  <h4 className="text-5xl lg:text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-[#05044A] to-slate-600 mb-4 animate-pulse">4+ Years</h4>
                  <p className="text-xl lg:text-2xl text-gray-700 font-bold">Forex Trading Expertise</p>
                  <div className="mt-4 w-20 h-1 bg-gradient-to-r from-[#05044A] to-slate-600 rounded-full mx-auto"></div>
                </div>
              </div>

              {/* Additional Achievement Card */}
              <div className="bg-gradient-to-br from-amber-50 via-yellow-50 to-amber-50 p-12 rounded-3xl border-2 border-amber-200/50 hover:shadow-3xl transition-all duration-500 hover:scale-105 hover:border-amber-300/60 group relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/20 to-yellow-100/20 rounded-3xl"></div>

                <div className="relative text-center">
                  <div className="flex items-center justify-center w-24 h-24 bg-gradient-to-br from-amber-400 via-yellow-400 to-amber-500 rounded-3xl mb-8 shadow-2xl group-hover:shadow-3xl transition-all duration-300 group-hover:scale-110 mx-auto">
                    <Trophy className="w-12 h-12 text-white group-hover:animate-bounce" />
                  </div>
                  <h4 className="text-4xl lg:text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-amber-600 to-yellow-600 mb-4">Gold Medalist</h4>
                  <p className="text-lg lg:text-xl text-gray-700 font-bold">International Roll Ball Champion</p>
                  <p className="text-sm text-gray-600 mt-2 font-medium">2013 & 2017</p>
                  <div className="mt-4 w-20 h-1 bg-gradient-to-r from-amber-400 to-yellow-400 rounded-full mx-auto"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
