
import React from 'react';
import { Phone, Globe, Mail, MapPin, Clock } from 'lucide-react';

export const Contact = () => {
  const inspirationalQuotes = [
    "Success in trading comes to those who are prepared, patient, and persistent.",
    "The market rewards discipline, not dreams.",
    "Every expert was once a beginner. Every professional was once an amateur.",
    "Trading is not about being right or wrong, it's about making money.",
    "The best traders are made through education, practice, and perseverance."
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-[#05044A] to-slate-800 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to Transform Your Trading?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Join thousands of successful traders who have transformed their financial future with our proven methods.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 mb-16">
            <div className="space-y-8">
              <h3 className="text-2xl font-bold text-[#56D07F] mb-6">Get In Touch</h3>
              
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-[#56D07F] rounded-lg">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Phone Number</p>
                    <p className="text-white font-semibold text-lg">+91 98765 43210</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-[#56D07F] rounded-lg">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Website</p>
                    <p className="text-white font-semibold text-lg">www.inkcharts.com</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-[#56D07F] rounded-lg">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Email</p>
                    <p className="text-white font-semibold text-lg"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-[#56D07F] rounded-lg">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm">Available</p>
                    <p className="text-white font-semibold text-lg">Mon - Fri, 9 AM - 7 PM IST</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-8">
              <h3 className="text-2xl font-bold text-[#56D07F] mb-6">Words of Wisdom</h3>
              <div className="space-y-4">
                {inspirationalQuotes.map((quote, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                    <p className="text-gray-300 italic">"{quote}"</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl font-bold text-[#56D07F] mb-4">
              Start Your Trading Journey Today
            </h3>
            <p className="text-gray-300 mb-6 text-lg">
              Don't wait for the "perfect" moment. The best time to start learning is now.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="bg-[#56D07F] text-white px-8 py-4 rounded-lg font-semibold text-lg">
                Enroll Now - ₹12,500
              </div>
              <div className="border-2 border-[#56D07F] text-[#56D07F] px-8 py-4 rounded-lg font-semibold text-lg hover:bg-[#56D07F] hover:text-white transition-colors">
                Contact Us
              </div>
            </div>
          </div>
          
          <div className="mt-16 text-center border-t border-white/20 pt-8">
            <p className="text-gray-400">
              © 2024 Ink Charts. All rights reserved. | Ultima Plus Trading Program
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
