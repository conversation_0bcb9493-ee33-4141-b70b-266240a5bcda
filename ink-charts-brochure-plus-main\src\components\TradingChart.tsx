
import React from 'react';

export const TradingChart = () => {
  return (
    <div className="w-full h-80 bg-gradient-to-br from-slate-50 to-white rounded-2xl p-6 relative overflow-hidden">
      {/* Background grid with animation */}
      <div className="absolute inset-0">
        <svg className="w-full h-full opacity-30" style={{ zIndex: 1 }}>
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e2e8f0" strokeWidth="0.5"/>
            </pattern>
            <linearGradient id="priceGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#56D07F" stopOpacity="0.8"/>
              <stop offset="100%" stopColor="#10b981" stopOpacity="0.9"/>
            </linearGradient>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>
      
      {/* Enhanced Candlestick Chart */}
      <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 2 }}>
        {/* Bullish Candles with glow effect */}
        <g className="animate-pulse">
          <rect x="30" y="100" width="10" height="50" fill="url(#priceGradient)" rx="2" />
          <line x1="35" y1="80" x2="35" y2="170" stroke="#56D07F" strokeWidth="2" />
          
          <rect x="60" y="80" width="10" height="60" fill="url(#priceGradient)" rx="2" />
          <line x1="65" y1="65" x2="65" y2="160" stroke="#56D07F" strokeWidth="2" />
          
          <rect x="90" y="90" width="10" height="45" fill="url(#priceGradient)" rx="2" />
          <line x1="95" y1="75" x2="95" y2="150" stroke="#56D07F" strokeWidth="2" />
        </g>
        
        {/* Bearish Candles */}
        <g className="animate-pulse delay-500">
          <rect x="120" y="110" width="10" height="50" fill="#ef4444" rx="2" />
          <line x1="125" y1="95" x2="125" y2="175" stroke="#ef4444" strokeWidth="2" />
          
          <rect x="150" y="125" width="10" height="35" fill="#ef4444" rx="2" />
          <line x1="155" y1="110" x2="155" y2="175" stroke="#ef4444" strokeWidth="2" />
        </g>
        
        {/* More Bullish Candles */}
        <g className="animate-pulse delay-1000">
          <rect x="180" y="95" width="10" height="65" fill="url(#priceGradient)" rx="2" />
          <line x1="185" y1="80" x2="185" y2="175" stroke="#56D07F" strokeWidth="2" />
          
          <rect x="210" y="70" width="10" height="70" fill="url(#priceGradient)" rx="2" />
          <line x1="215" y1="55" x2="215" y2="155" stroke="#56D07F" strokeWidth="2" />
          
          <rect x="240" y="60" width="10" height="55" fill="url(#priceGradient)" rx="2" />
          <line x1="245" y1="45" x2="245" y2="130" stroke="#56D07F" strokeWidth="2" />
        </g>
        
        {/* Animated trend line */}
        <line x1="30" y1="180" x2="260" y2="80" stroke="#05044A" strokeWidth="3" strokeDasharray="6,6" className="animate-pulse">
          <animate attributeName="stroke-dashoffset" values="0;12;0" dur="3s" repeatCount="indefinite"/>
        </line>
        
        {/* Support/Resistance Lines with animation */}
        <line x1="0" y1="140" x2="300" y2="140" stroke="#56D07F" strokeWidth="2" strokeDasharray="4,4" opacity="0.8" className="animate-pulse">
          <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
        </line>
        <line x1="0" y1="100" x2="300" y2="100" stroke="#ef4444" strokeWidth="2" strokeDasharray="4,4" opacity="0.8" className="animate-pulse delay-1000">
          <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
        </line>
        
        {/* Volume bars at bottom */}
        <g opacity="0.6">
          <rect x="32" y="220" width="6" height="20" fill="#56D07F"/>
          <rect x="62" y="210" width="6" height="30" fill="#56D07F"/>
          <rect x="92" y="225" width="6" height="15" fill="#56D07F"/>
          <rect x="122" y="215" width="6" height="25" fill="#ef4444"/>
          <rect x="152" y="230" width="6" height="10" fill="#ef4444"/>
          <rect x="182" y="205" width="6" height="35" fill="#56D07F"/>
          <rect x="212" y="200" width="6" height="40" fill="#56D07F"/>
          <rect x="242" y="195" width="6" height="45" fill="#56D07F"/>
        </g>
      </svg>
      
      {/* Enhanced Chart Labels */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg shadow-md">
        <div className="text-sm text-[#05044A] font-bold">XAUUSD - Gold Trading</div>
      </div>
      <div className="absolute top-4 right-4 bg-gradient-to-r from-[#56D07F] to-emerald-400 text-white px-4 py-2 rounded-lg shadow-lg">
        <div className="text-sm font-bold flex items-center">
          ↗ +2.45%
        </div>
      </div>
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-md">
        <div className="text-xs text-gray-600 font-medium">Volume: 24.5K</div>
      </div>
      <div className="absolute bottom-4 right-4 bg-[#05044A] text-white px-3 py-1 rounded-md">
        <div className="text-xs font-semibold flex items-center">
          <div className="w-2 h-2 bg-[#56D07F] rounded-full mr-2 animate-pulse"></div>
          Live Chart
        </div>
      </div>
    </div>
  );
};
