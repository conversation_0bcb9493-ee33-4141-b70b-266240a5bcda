
import React from 'react';
import { BookOpen, TrendingUp, Brain, Target } from 'lucide-react';

export const Curriculum = () => {
  const modules = [
    {
      icon: BookOpen,
      title: "Market Foundations",
      items: [
        "What is Trading & How Markets Actually Work",
        "Types of Markets (Forex, Indices, Crypto, etc.)",
        "Trading Styles: Scalping, Day Trading, Swing",
        "Understanding Trends, Volatility & Sessions"
      ]
    },
    {
      icon: TrendingUp,
      title: "Price Action Trading",
      items: [
        "How to Read Candlestick Behaviour Like a Pro",
        "Dynamic Support & Resistance Zones",
        "High-Probability Chart Patterns",
        "Entry, Exit, and Risk-Based Trade Management"
      ]
    },
    {
      icon: Brain,
      title: "Smart Money Concepts (SMC)",
      items: [
        "Market Structure: BOS, CHoCH, and Internal Order Flow",
        "Liquidity Grabs & Stop Hunt Recognition",
        "Precision Entries using Order Blocks & Imbalances",
        "FVGs (Fair Value Gaps), Breaker Blocks, and Institutional Zones"
      ]
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-[#05044A] mb-6">
              Complete Trading Curriculum
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Master every aspect of trading with our comprehensive, structured approach
            </p>
          </div>
          
          <div className="space-y-8">
            {modules.map((module, index) => (
              <div key={index} className="bg-gradient-to-r from-white to-slate-50 rounded-2xl shadow-lg border p-8 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center w-16 h-16 bg-[#56D07F] rounded-xl">
                      <module.icon className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  
                  <div className="flex-grow">
                    <h3 className="text-2xl font-bold text-[#05044A] mb-6">
                      🔹 {module.title}
                    </h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {module.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-[#56D07F] rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-gray-700 leading-relaxed">{item}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
